package com.concise.common.file.param;

import javax.validation.constraints.NotNull;

import com.concise.common.pojo.base.param.BaseParam;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 文件信息表
 * </p>
 *
 * <AUTHOR>
 * @date 2020/6/7 22:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysFileInfoParam extends BaseParam {

    /**
     * 主键id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {delete.class, detail.class})
    private Long id;

    /**
     * 文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）
     */
    private Integer fileLocation;

    /**
     * 文件仓库
     */
    private String fileBucket;

    /**
     * 文件名称（上传时候的文件名）
     */
    private String fileOriginName;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件大小kb
     */
    private Long fileSizeKb;

    /**
     * 存储到bucket的名称（文件唯一标识id）
     */
    private String fileObjectName;

    /**
     * 存储路径
     */
    private String filePath;

    /**
     * id(为了匹配前端取值属性)
     */
    private String uid;

    /**
     * 文件名(为了匹配前端取值属性)
     */
    private String name;

    /**
     * url(为了匹配前端取值属性)
     */
    private String url;

    /**
     * 关联业务id
     */
    private String bizId;
    /**
     * 关联业务类别
     */
    private String bizType;

    /**
     * 印章位置X
     */
    private Integer posX;

    /**
     * 印章位置Y
     */
    private Integer posY;

    /**
     * 页数
     */
    private Integer posPage;

    /**
     * 印章编号
     */
    private String sealSn;

    /**
     * 关键字（关键字盖章时使用）
     */
    private String key;

    /**
     * 是否删除（0：否，1：是）
     */
    private Integer delFlag;

    /**
     * 笔录id
     */
    private String blId;

    /**
     * 文件版本
     */
    private Integer version;
}
