package com.concise.common.file.util;

import com.aliyun.oss.*;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.concise.common.consts.SymbolConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.util.Date;

/**
 * 阿里云OSS对象存储预签名URL工具类
 * 支持使用自定义域名生成预签名URL
 */
@Slf4j
public class OssSignedUrlUtil {

    private static String endpoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String region = "oss-cn-hangzhou-zjzwy01-d01-a";
    private static String publicDomain;
    private static String staticDomain;
    private static String folder;
    private static Boolean active;

    private static OSS ossClient = null;

    /**
     * 设置OSS服务端点
     * @param endpoint OSS服务端点
     */
    public static void setEndpoint(String endpoint) {
        OssSignedUrlUtil.endpoint = endpoint;
    }

    /**
     * 设置访问密钥ID
     * @param accessKeyId 访问密钥ID
     */
    public static void setAccessKeyId(String accessKeyId) {
        OssSignedUrlUtil.accessKeyId = accessKeyId;
    }

    /**
     * 设置访问密钥密码
     * @param accessKeySecret 访问密钥密码
     */
    public static void setAccessKeySecret(String accessKeySecret) {
        OssSignedUrlUtil.accessKeySecret = accessKeySecret;
    }

    /**
     * 设置存储桶名称
     * @param bucketName 存储桶名称
     */
    public static void setBucketName(String bucketName) {
        OssSignedUrlUtil.bucketName = bucketName;
    }

    /**
     * 设置区域
     * @param region OSS区域（例如：oss-cn-hangzhou）
     */
    public static void setRegion(String region) {
        OssSignedUrlUtil.region = region;
    }

    public static void setStaticDomain(String staticDomain) {
        OssSignedUrlUtil.staticDomain = staticDomain;
    }

    public static String getStaticDomain() {
        return staticDomain;
    }
    /**
     * 设置公共访问域名
     * @param publicDomain 公共访问域名
     */
    public static void setPublicDomain(String publicDomain) {
        OssSignedUrlUtil.publicDomain = publicDomain;
    }

    public static void setFolder(String folder) {
        OssSignedUrlUtil.folder = folder;
    }

    public static void setActive(Boolean active) {
        OssSignedUrlUtil.active = active;
    }

    /**
     * 获取OSS客户端实例
     * @return OSS客户端实例
     */
    private static OSS getOssClient() {
        if (ossClient == null) {
            initOssClient();
        }
        return ossClient;
    }

    /**
     * 初始化OSS客户端
     */
    private static synchronized void initOssClient() {
        if (ossClient != null) {
            return;
        }


        try {
            DefaultCredentialProvider credentialsProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);

            // 创建OSSClient实例
            ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
            // 支持自定义域名
            clientBuilderConfiguration.setSupportCname(true);
            // 显式声明使用 V4 签名算法
            clientBuilderConfiguration.setSignatureVersion(SignVersion.V2);
            ossClient = OSSClientBuilder.create()
                .endpoint(publicDomain)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();

            log.debug("OSS客户端初始化成功，使用域名：{}", StringUtils.isNotEmpty(publicDomain) ? publicDomain : endpoint);
        } catch (ClientException ce) {
            log.error("OSS客户端初始化失败 - 客户端异常：{}", ce.getMessage());
            throw new RuntimeException("OSS客户端初始化失败", ce);
        } catch (Exception e) {
            log.error("OSS客户端初始化失败：", e);
            throw new RuntimeException("OSS客户端初始化失败", e);
        }
    }

    // private static OSS initOSSClient(String endpoint, String accessKeyId, String accessKeySecret) {
    //     if (ossClient == null) {
    //         // 私有云要关闭CNAME
    //         ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
    //         conf.setSupportCname(false);
    //         // 使用正确的链式调用方式
    //         ossClient = OSSClientBuilder.create()
    //                 .endpoint(endpoint)
    //                 .credentialsProvider(new DefaultCredentialProvider(accessKeyId, accessKeySecret))
    //                 .clientConfiguration(conf)
    //                 .region(region)
    //                 .build();
    //     }
    //     return ossClient;
    // }

    /**
     * 关闭OSS客户端
     */
    public static void shutdown() {
        if (ossClient != null) {
            ossClient.shutdown();
            ossClient = null;
        }
    }

    /**
     * 生成预签名URL
     * @param objectName 对象名称
     * @param expireTime 过期时间（毫秒）
     * @return 预签名URL
     */
    public static String generatePresignedUrl(String objectName, long expireTime) {
        return generatePresignedUrl(bucketName, objectName, expireTime);
    }

    /**
     * 生成预签名URL，使用指定的存储桶
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expireTime 过期时间（毫秒）
     * @return 预签名URL
     */
    public static String generatePresignedUrl(String bucketName, String objectName, long expireTime) {
        if (StringUtils.isEmpty(bucketName) || StringUtils.isEmpty(objectName)) {
            log.error("生成预签名URL失败：存储桶名称或对象名称为空");
            return null;
        }

        OSS client = getOssClient();
        try {
            // 设置URL过期时间
            Date expiration = new Date(System.currentTimeMillis() + expireTime);
            // 生成预签名URL
            URL url = client.generatePresignedUrl(bucketName, objectName, expiration);
            // System.out.println("url:" + url.toString());
            return url.toString();
        } catch (OSSException oe) {
            log.error("生成预签名URL失败 - OSS异常：{}, 错误码：{}, 请求ID：{}",
                    oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId());
            return null;
        } catch (ClientException ce) {
            log.error("生成预签名URL失败 - 客户端异常：{}", ce.getMessage());
            return null;
        } catch (Exception e) {
            log.error("生成预签名URL失败：", e);
            return null;
        }
    }

    /**
     * 生成预签名URL，使用默认存储桶，默认7天过期时间
     * @param objectName 对象名称
     * @return 预签名URL
     */
    public static String generatePresignedUrl(String objectName) {
        // 默认7天过期时间
        return generatePresignedUrl(objectName, 7 * 24 * 60 * 60 * 1000L);
    }

    /**
     * 生成预签名URL，适用于已经经过转发的外网地址
     * @param formalUrl 转发后的完整URL
     * @return 预签名URL
     */
    public static String generatePresignedUrlWithPrefix(String formalUrl) {
        if (StringUtils.isEmpty(formalUrl)) {
            log.error("生成预签名URL失败：参数不完整");
            return null;
        }

        try {
            String objectName = "";
            int beginIndex = formalUrl.indexOf(folder);
            if (beginIndex > 0) {
                objectName = formalUrl.substring(beginIndex);
            }else {
                objectName = formalUrl;
            }
            if (objectName.indexOf(SymbolConstant.QUESTION_MARK)>0) {
                objectName = objectName.substring(0, objectName.indexOf("?"));
            }
            log.debug("objectName:" + objectName);
            String url = generatePresignedUrl(objectName, 7 * 24 * 60 * 60 * 1000L);
            if (url != null) {
                log.debug("url:" + url);
                return url;
            }
            return null;
        } catch (Exception e) {
            log.error("生成带前缀的预签名URL失败：", e);
            return null;
        }
    }

    /**
     * 生成预签名URL，使用项目配置的staticDomain
     * @param formalUrl 转发后的完整URL
     * @return 预签名URL
     */
    public static String generatePresignedUrlWithPublicDomain(String formalUrl) {
        if (!active) {
            return formalUrl;
        }

        if (StringUtils.isEmpty(publicDomain)) {
            log.error("生成预签名URL失败：publicDomain未配置");
            return null;
        }
        return generatePresignedUrlWithPrefix(formalUrl);
    }

}
