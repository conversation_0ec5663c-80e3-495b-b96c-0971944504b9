package com.concise.common.file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.file.result.SysFileInfoResult;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * 文件信息表 服务类
 *
 * <AUTHOR>
 * @date 2020/6/7 22:15
 */
public interface SysFileInfoService extends IService<SysFileInfo> {

    /**
     * 分页查询文件信息表
     *
     * @param sysFileInfoParam 查询参数
     * @return 查询分页结果
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    PageResult<SysFileInfo> page(SysFileInfoParam sysFileInfoParam);

    /**
     * 查询所有文件信息表
     *
     * @param sysFileInfoParam 查询参数
     * @return 文件信息列表
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    List<SysFileInfo> list(SysFileInfoParam sysFileInfoParam);

    /**
     * 添加文件信息表
     *
     * @param sysFileInfoParam 添加参数
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    void add(SysFileInfoParam sysFileInfoParam);

    /**
     * 删除文件信息表
     *
     * @param sysFileInfoParam 删除参数
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    void delete(SysFileInfoParam sysFileInfoParam);

    /**
     * 编辑文件信息表
     *
     * @param sysFileInfoParam 编辑参数
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    void edit(SysFileInfoParam sysFileInfoParam);

    /**
     * 查看详情文件信息表
     *
     * @param sysFileInfoParam 查看参数
     * @return 文件信息
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    SysFileInfo detail(SysFileInfoParam sysFileInfoParam);

    /**
     * 根据业务id获取文件信息
     * @param bizId 业务id
     * @return SysFileInfo
     */
    List<SysFileInfo> detail(String bizId);

    /**
     * 根据ids获取文件信息
     * @param ids id集合 逗号分割
     * @return SysFileInfoParam
     */
    List<SysFileInfoVO> getDetailByIds(String ids);

    /**
     * 根据ids拼装文书信息
     * @param ids ids
     * @return AcceptCorrectionDoc
     */
    List<AcceptCorrectionDoc> getDocListByIds(String ids);
    List<SysFileInfoVO> getDocList(String bizId,String bizType);

    /**
     * 上传文件，返回文件的唯一标识
     *
     * @param file 要上传的文件
     * @return 文件id
     * <AUTHOR>
     * @date 2020/6/9 21:21
     */
    SysFileInfo uploadFile(MultipartFile file);

    /**
     * 获取文件信息结果集
     *
     * @param fileId 文件id
     * @return 文件信息结果集
     * <AUTHOR>
     * @date 2020/6/9 21:56
     */
    SysFileInfoResult getFileInfoResult(Long fileId);

    /**
     * 判断文件是否存在
     *
     * @param field 文件id
     * <AUTHOR>
     * @date 2020/6/28 15:55
     */
    void assertFile(Long field);

    /**
     * 文件预览
     *
     * @param sysFileInfoParam 文件预览参数
     * @param response         响应结果
     * <AUTHOR>
     * @date 2020/7/7 11:23
     */
    void preview(SysFileInfoParam sysFileInfoParam, HttpServletResponse response);

    /**
     * 文件下载
     *
     * @param sysFileInfoParam 文件下载参数
     * @param response         响应结果
     * <AUTHOR>
     * @date 2020/7/7 12:09
     */
    void download(SysFileInfoParam sysFileInfoParam, HttpServletResponse response);

    /**
     * 上传文件至阿里云，返回文件的唯一标识
     *
     * @param file 要上传的文件
     * @param type 类型： 1：表示页面手动上传的，空则是生成的
     * @param signType 1: 表示有电子签章 其余值或空值则表示无电子签章
     * @return 文件id
     * <AUTHOR>
     * @date 2020/6/9 21:21
     */
    SysFileInfo uploadFileOss(MultipartFile file, String type, String signType);
    SysFileInfo uploadFileOss(String fileName, byte[] bytes, String type, String signType);

    /**
     * 上传到政法一体化oss
     * @param file MultipartFile
     * @return SysFileInfo
     */
    SysFileInfo uploadFileExtOss(MultipartFile file);
    SysFileInfo uploadFileExtOss(String fileOriginName, byte[] bytes);

    /**
     * 上传文件至阿里云，返回文件的唯一标识
     * @param fileList FileParam
     * @return list
     */
    List<SysFileInfo> uploadFileOss(List<AcceptCorrectionDocParam> fileList);

    /**
     * 上传文件至阿里云,返回文件的唯一标识
     * @param file File
     * @param bizId bizId
     * @return SysFileInfo
     */
    SysFileInfo uploadFileOss(File file, String bizId);

    /**
     * 上传填充模版完成后的文件
     * @param file File
     * @param fileName fileName
     * @return SysFileInfo
     */
    SysFileInfo uploadTemplatedFileOss(String fileName, File file);

    /**
     * 设置业务id
     * @param ids 逗号分隔附件id
     * @param bizId bizId
     * @param bizType bizType
     */
    void setBiz(String ids,String bizId,String bizType);

    /**
     * 移除业务绑定
     * @param bizId bizId
     * @param bizType bizType
     */
    void removeBizBind(String bizId,String bizType);


    /**
     * 设置业务id
     * @param list 附件list
     * @param bizId bizId
     * @param bizType bizType
     */
    void setBizList(List<SysFileInfo> list,String bizId,String bizType);

    /**
     * 文件上传
     * @param fileName 文件名
     * @param baos 输出流
     * @return
     */
    SysFileInfo uploadFileOss(String fileName, ByteArrayOutputStream baos);

    List<SysFileInfoVO> getFileList(String bizId, String bizType, boolean sign);

    InputStream download(SysFileInfo sysFileInfo);


    /**
     * 上传文件至阿里云,返回文件的唯一标识(笔录文书用)
     * @param file 笔录文件
     * @param bizId 调查评估id
     * @param bizType 固定传BLLX
     * @param fileName 文件名
     * @param blType  笔录类型
     * @param transcriptId 笔录id
     * @param type 类型： 1：表示页面手动上传的，空则是生成的
     * @return
     */
    SysFileInfo uploadFileOss(File file, String bizId, String bizType, String fileName, String blType, String transcriptId, String type);


    /**
     * 上传文件至阿里云，返回文件的唯一标识
     *
     * @param file 要上传的文件
     * @param type 类型： 1：表示页面手动上传的，空则是生成的
     * @param signType 1: 表示有电子签章 其余值或空值则表示无电子签章
     * @return 文件id
     * <AUTHOR>
     * @date 2020/6/9 21:21
     */
    SysFileInfo uploadFileOss(MultipartFile file, String type, String signType, SysFileInfo fileInfo);


    /**
     * WPS编辑文件 上传文件至阿里云，返回文件的唯一标识
     * @param file 文件
     * @param fileId 附件id
     * @param updateUser 修改人
     * @return
     */
    SysFileInfo uploadWPSFile(MultipartFile file, Long fileId, String updateUser);
}
