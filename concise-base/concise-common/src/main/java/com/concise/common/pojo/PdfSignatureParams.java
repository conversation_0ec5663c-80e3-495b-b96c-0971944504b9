package com.concise.common.pojo;

import lombok.Data;

/**
 * PDF签名相关统一请求参数
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Data
public class PdfSignatureParams {
    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 图片文件ID
     */
    private Long imageFileId;

    /**
     * 印章图片ID
     */
    private Long sealImageId;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * X坐标
     */
    private Float posX;

    /**
     * Y坐标
     */
    private Float posY;

    /**
     * 起始X坐标
     */
    private Integer startX;

    /**
     * 起始Y坐标
     */
    private Integer startY;

    /**
     * 宽度
     */
    private Float width;

    /**
     * 高度
     */
    private Float height;

    /**
     * 起始页码
     */
    private Integer startPage;

    /**
     * 结束页码
     */
    private Integer endPage;

    /**
     * 水印文字
     */
    private String watermarkText;

    /**
     * 透明度
     */
    private Float opacity;

    /**
     * 字体大小
     */
    private Float fontSize;

    /**
     * 旋转角度
     */
    private Float rotation;

    /**
     * 印章大小
     */
    private Float sealSize;

    /**
     * 表单字段名称
     */
    private String fieldName;

    /**
     * 表单字段名称，多个字段用逗号分隔
     */
    private String fieldNames;

    /**
     * 图片文字
     */
    private String text;

    /**
     * 要查找的文本
     */
    private String searchText;

    /**
     * 图片相对于文本的X偏移量
     */
    private Float offsetX;

    /**
     * 图片相对于文本的Y偏移量
     */
    private Float offsetY;

    /**
     * 签名图片文件ID列表，用逗号分隔
     */
    private String imageFileIds;

    /**
     * 每个图片的宽度
     */
    private Integer imageWidth;

    /**
     * 每个图片的高度
     */
    private Integer imageHeight;

    /**
     * 列间距
     */
    private Integer horizontalGap;

    /**
     * 图片之间的垂直间距
     */
    private Integer verticalGap;

    /**
     * 左边距
     */
    private Integer marginLeft;

    /**
     * 右边距
     */
    private Integer marginRight;

    /**
     * 底部边距
     */
    private Float marginBottom;

    /**
     * 第几个关键字（如果文档中有多个相同的关键字，0表示第一个）
     */
    private Integer keyWordIndex;

}
