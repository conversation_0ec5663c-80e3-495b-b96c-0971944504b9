package com.concise.common.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.element.Image;
import com.itextpdf.io.image.ImageDataFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.multipdf.PDFMergerUtility;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
/**
 * PDF 工具类
 */
@Slf4j
public class PDFUtil {
    /**
     *
     * 经测试，相同文件的合并用itext比Pdfbox快一点，且合并后的文件更小
     *
    **/
    public static void main(String[] args) {
        ArrayList<String> url = new ArrayList<String>();
        url.add("https://lq1990.oss-cn-hangzhou.aliyuncs.com/dataCollaboration/202506/1929732472213209089.pdf");
        url.add("https://lq1990.oss-cn-hangzhou.aliyuncs.com/dataCollaboration/202506/1932639333572902913.pdf");
        url.add("https://lq1990.oss-cn-hangzhou.aliyuncs.com/dataCollaboration/202505/1925852561916530690.pdf");
        url.add("C:\\Users\\<USER>\\Desktop\\pdf\\wszz_notice.pdf");
         PDFUtil.createNewPdfByPdfbox(url, "dddd.pdf", "1");
        //PDFUtil.createNewPdfByIText(url, "dddd.pdf", "1");
    }

    /**
     * 多份pdf合并，返回新的pdf
     * @param filePath
     * @return
     */
    public static File createNewPdfByPdfbox(List<String> filePath, String fileName, String id) {
        // 临时目录用于缓存网络文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        // 存储临时文件的列表（用于后续清理）
        List<File> tempFiles = new ArrayList<>();
        try {
            PDFMergerUtility merger = new PDFMergerUtility();
            for (String pdfSource : filePath) {
                if (pdfSource.startsWith("http://") || pdfSource.startsWith("https://")) {
                    // 处理网络文件：下载到临时目录
                    String tempFileName = "temp_" + id + "_" + System.currentTimeMillis() + ".pdf";
                    File tempFile = new File(tempDir, tempFileName);
                    tempFiles.add(tempFile);
                    try (InputStream in = new URL(pdfSource).openStream()) {
                        Files.copy(in, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                        merger.addSource(tempFile);
                    }
                } else {
                    // 处理本地文件
                    File localFile = new File(pdfSource);
                    if (!localFile.exists()) {
                        log.warn("警告：本地文件不存在，已跳过 - " + pdfSource + "id=" + id);
                        continue;
                    }
                    merger.addSource(localFile);
                    tempFiles.add(localFile);
                }
            }

            // 设置输出文件路径
            merger.setDestinationFileName(tempDir.getPath() + "/" + fileName);
            // 执行合并
            merger.mergeDocuments(null);

        } catch (IOException e) {
            e.printStackTrace();
            log.error("createNewPdfByPdfbox 合并失败: " + id);
        } finally {
            for (File file : tempFiles) {
                if (file.exists() && !file.delete()) {
                    log.warn("警告：临时文件删除失败 - " + file.getAbsolutePath());
                }
            }
        }
        return new File(tempDir.getPath() + "/" + fileName);
    }


    /**
     * 多份pdf合并，返回新的pdf
     * @param filePath
     * @return
     */
    public static File createNewPdfByIText(List<String> filePath, String fileName, String id) {
        // 临时目录用于缓存网络文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        // 存储临时文件的列表（用于后续清理）
        List<File> tempFiles = new ArrayList<>();

        try (PdfWriter writer = new PdfWriter(tempDir.getPath() + "/" + fileName);
             PdfDocument mergedPdf = new PdfDocument(writer)) {

            for (String pdfSource : filePath) {
                PdfReader reader;
                if (pdfSource.startsWith("http://") || pdfSource.startsWith("https://")) {
                    // 处理网络文件：下载到临时文件
                    File tempFile = File.createTempFile("temp_" + id + "_" + System.currentTimeMillis(), ".pdf");
                    tempFiles.add(tempFile); // 记录临时文件

                    try (InputStream in = new URL(pdfSource).openStream()) {
                        Files.copy(in, tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    }
                    reader = new PdfReader(tempFile.getAbsolutePath());
                } else {
                    // 处理本地文件
                    File localFile = new File(pdfSource);
                    if (!localFile.exists()) {
                        log.warn("警告：本地文件不存在，已跳过 - " + pdfSource + "id=" + id);
                        continue;
                    }
                    reader = new PdfReader(localFile.getAbsolutePath());
                    tempFiles.add(localFile);
                }

                // 合并PDF页面
                try (PdfDocument srcPdf = new PdfDocument(reader)) {
                    int pageCount = srcPdf.getNumberOfPages();
                    for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                        PdfPage page = srcPdf.getPage(pageNum).copyTo(mergedPdf);
                        mergedPdf.addPage(page);
                    }
                }
            }
        } catch (IOException e) {
            log.error("createNewPdfByIText 合并失败: " + id);
            e.printStackTrace();
        } finally {
            // 清理所有临时文件
            for (File file : tempFiles) {
                if (file.exists() && !file.delete()) {
                    log.warn("警告：临时文件删除失败 - " + file.getAbsolutePath());
                }
            }
        }
        return new File(tempDir.getPath() + "/" + fileName);
    }


}
