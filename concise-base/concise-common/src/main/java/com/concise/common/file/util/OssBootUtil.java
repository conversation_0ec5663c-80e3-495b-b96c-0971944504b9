package com.concise.common.file.util;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.concise.common.config.FileConfig;
import com.concise.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * <AUTHOR>
 * @Description: 阿里云 oss 工具类
 * @Date: 2019/5/10
 */
@Slf4j
public class OssBootUtil {
    private static String endPoint;
    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String staticDomain;
    private static String publicDomain;
    private static String folder;

    public static String getEndPoint() {
        return endPoint;
    }

    public static void setEndPoint(String endPoint) {
        OssBootUtil.endPoint = endPoint;
    }

    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static void setAccessKeyId(String accessKeyId) {
        OssBootUtil.accessKeyId = accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static void setAccessKeySecret(String accessKeySecret) {
        OssBootUtil.accessKeySecret = accessKeySecret;
    }

    public static String getBucketName() {
        return bucketName;
    }

    public static void setBucketName(String bucketName) {
        OssBootUtil.bucketName = bucketName;
    }

    public static String getStaticDomain() {
        return staticDomain;
    }

    public static void setStaticDomain(String staticDomain) {
        OssBootUtil.staticDomain = staticDomain;
    }

    public static String getPublicDomain() {
        return publicDomain;
    }

    public static void setPublicDomain(String publicDomain) {
        OssBootUtil.publicDomain = publicDomain;
    }

    public static String getFolder() {
        return folder;
    }

    public static void setFolder(String folder) {
        OssBootUtil.folder = folder;
    }

    /**
     * oss 工具客户端
     */
    private static OSSClient ossClient = null;
    public static OSSClient getOssClient() {
        return ossClient;
    }

    /**
     * 上传文件至阿里云 OSS
     * 文件上传成功,返回文件完整访问路径
     * 文件上传失败,返回 null
     *
     * @param file    待上传文件
     * @param key 文件保存目录
     * @return oss 中的相对文件路径
     */
    public static String upload(MultipartFile file, String key, String endPoint, String accessKeyId, String accessKeySecret, String bucketName,String staticDomain) {
        initOSS(endPoint, accessKeyId, accessKeySecret);
        if(StringUtils.isEmpty(bucketName)){
            return "";
        }
        try {
            //判断桶是否存在,不存在则创建桶
            if(!ossClient.doesBucketExist(bucketName)){
                ossClient.createBucket(bucketName);
            }
            PutObjectResult result = ossClient.putObject(bucketName,key,file.getInputStream());
            if (result != null) {
                log.debug(ossClient.getEndpoint() + ":------OSS文件上传成功------" + key);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
        return staticDomain + "/" + key;
    }

    public static void upload(MultipartFile file, String key, OSSClient client, String bucketName) {
        if(StringUtils.isEmpty(bucketName)){
            return;
        }
        try {
            //判断桶是否存在,不存在则创建桶
            if(!client.doesBucketExist(bucketName)){
                client.createBucket(bucketName);
            }
            PutObjectResult result = client.putObject(bucketName, key, file.getInputStream());

            if (result != null) {
                log.debug(client.getEndpoint() + ":------OSS文件上传成功------" + key);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static void upload(InputStream inputStream, String key, OSSClient client, String bucketName) {
        if(StringUtils.isEmpty(bucketName)){
            return;
        }
        //判断桶是否存在,不存在则创建桶
        if(!client.doesBucketExist(bucketName)){
            client.createBucket(bucketName);
        }
        PutObjectResult result = client.putObject(bucketName, key, inputStream);

        if (result != null) {
            log.debug(client.getEndpoint() + ":------OSS文件上传成功------" + key);
        }
    }

    /**
     * Upload
     * @param file file
     * @param bucketName bucketName
     * @param key key
     * @return r
     */
    public static String upload(File file, OSSClient client, String bucketName, String key){
        String resultStr = null;
        try {
            InputStream is = new FileInputStream(file);
            String fileName = file.getName();
            long fileSize = file.length();
            //创建上传Object的Metadata
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(is.available());
            metadata.setCacheControl("no-cache");
            metadata.setHeader("Pragma", "no-cache");
            metadata.setContentEncoding("utf-8");
            //metadata.setContentType(getContentType(fileName));
            metadata.setContentDisposition("filename/filesize=" + fileName + "/" + fileSize + "Byte.");
            // 上传文件
            PutObjectResult putResult = client.putObject(bucketName, key, is, metadata);
            //解析结果
            //resultStr = putResult.getETag();
            resultStr = String.valueOf(fileSize);
            // 删除硬盘上的文件
            file.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultStr;
    }

    /**
     * 上传文件至阿里云 OSS
     * 文件上传成功,返回文件完整访问路径
     * 文件上传失败,返回 null
     *
     * @param file    待上传文件
     * @param key 文件保存目录
     * @return oss 中的相对文件路径
     */
    public static String upload(File file, String key, String endPoint, String accessKeyId, String accessKeySecret, String bucketName, String staticDomain) {
        initOSS(endPoint, accessKeyId, accessKeySecret);
        if(StringUtils.isEmpty(bucketName)){
            return "";
        }
        try {
            //判断桶是否存在,不存在则创建桶
            if(!ossClient.doesBucketExist(bucketName)){
                ossClient.createBucket(bucketName);
            }
            InputStream is = new FileInputStream(file);
            PutObjectResult result = ossClient.putObject(bucketName,key,is);
            if (result != null) {
                log.debug(ossClient.getEndpoint() + ":------OSS文件上传成功------" + key);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
        return staticDomain + "/" + key;
    }

    /**
     * 从oss上下载附件
     * @param filePathName 文件存放路径
     * @param bucketName bucketName
     * @param key 文件在oss上的路径
     * @return file
     */
    public static File download(String filePathName,String fileName ,OSSClient client, String bucketName, String key){
        File dir = new File(FileConfig.DEFAULT_TEMP);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(filePathName+fileName);
        try {
            client.getObject(new GetObjectRequest(bucketName, key), file);
        }catch (Exception e){
            System.out.println("NoSuchKey: "+key);
            throw new ServiceException(500,"NoSuchKey");
        }
        return file;
    }

    public static InputStream getObject(OSSClient client, String bucketName, String key){
        OSSObject ossObject = client.getObject(bucketName, key);
        if (ossObject != null) {
            return ossObject.getObjectContent();
        }
        return null;
    }


    /**
     * 初始化 oss 客户端
     */
    private static OSSClient initOSS(String endpoint, String accessKeyId, String accessKeySecret) {
        if (ossClient == null) {
            // 私有云要关闭CNAME
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setSupportCname(false);
            ossClient = (OSSClient)new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret, conf);
        }
        return ossClient;
    }

    /**
     * 判断文件名是否带盘符，重新处理
     */
    public static String getFileName(String fileName){
        //判断是否带有盘符信息
        // Check for Unix-style path
        int unixSep = fileName.lastIndexOf('/');
        // Check for Windows-style path
        int winSep = fileName.lastIndexOf('\\');
        // Cut off at latest possible point
        int pos = (winSep > unixSep ? winSep : unixSep);
        if (pos != -1)  {
            // Any sort of path separator found...
            fileName = fileName.substring(pos + 1);
        }
        //替换上传文件名字的特殊字符
        fileName = fileName.replace("=","").replace(",","").replace("&","").replace("#", "");
        return fileName;
    }

    public static Long getFileSize(String bucketName,String key, OSSClient client) {
        if (client == null) {
            initOSS(endPoint, accessKeyId, accessKeySecret);
            client = getOssClient();
        }
        SimplifiedObjectMeta meta = client.getSimplifiedObjectMeta(bucketName, key);
        if (meta == null) {
            return 0L;
        }
        return meta.getSize();
    }

    public static String filter(String str) throws PatternSyntaxException {
        // 清除掉所有特殊字符
        String regEx = "[`_《》~!@#$%^&*()+=|{}':;',\\[\\].<>?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    public static String getContentType(String fileName) {
        String fileExtension = fileName.substring(fileName.lastIndexOf("."));
        if ("bmp".equalsIgnoreCase(fileExtension)) {
            return "image/bmp";
        }
        if ("gif".equalsIgnoreCase(fileExtension)) {
            return "image/gif";
        }
        if ("jpeg".equalsIgnoreCase(fileExtension) || "jpg".equalsIgnoreCase(fileExtension) || "png".equalsIgnoreCase(fileExtension)) {
            return "image/jpeg";
        }
        if ("html".equalsIgnoreCase(fileExtension)) {
            return "text/html";
        }
        if ("txt".equalsIgnoreCase(fileExtension)) {
            return "text/plain";
        }
        if ("vsd".equalsIgnoreCase(fileExtension)) {
            return "application/vnd.visio";
        }
        if ("ppt".equalsIgnoreCase(fileExtension) || "pptx".equalsIgnoreCase(fileExtension)) {
            return "application/vnd.ms-powerpoint";
        }
        if ("doc".equalsIgnoreCase(fileExtension) || "docx".equalsIgnoreCase(fileExtension)) {
            return "application/msword";
        }
        if ("xml".equalsIgnoreCase(fileExtension)) {
            return "text/xml";
        }
        return "text/html";
    }

}
