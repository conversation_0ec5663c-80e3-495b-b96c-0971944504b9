package com.concise.common.file.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.concise.common.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 文件信息表
 * </p>
 *
 * <AUTHOR>
 * @date 2020/6/7 22:15
 */
@Data
@TableName("sys_file_info")
public class SysFileInfo {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联业务id
     */
    private String bizId;
    /**
     * 关联业务类别
     */
    private String bizType;

    /**
     * 类型： 1：表示页面手动上传的，空则是生成的
     */
    private String type;

    /**
     * 是否有电子签章： 1：表示有电子签章，空则表示无签章
     */
    private String signType;

    /**
     * 文件存储位置（1:阿里云，2:腾讯云，3:minio，4:本地）
     */
    private Integer fileLocation;

    /**
     * 文件仓库/ bizType是 BLLX 时 该字段存笔录类型的字典 如 BLLX01、BLLX02...
     */
    private String fileBucket;

    /**
     * 文件名称（上传时候的文件名）
     */
    private String fileOriginName;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件大小kb
     */
    private Long fileSizeKb;

    /**
     * 文件大小信息，计算后的
     */
    private String fileSizeInfo;

    /**
     * 存储到bucket的名称（文件唯一标识id）
     */
    private String fileObjectName;

    /**
     * 存储路径
     */
    private String filePath;

    /**
     * 外部oss
     */
    private String extFilePath;

    /**
     * 是否删除（0：否，1：是）
     */
    private Integer delFlag;

    /**
     * 笔录id
     */
    private String blId;

    /**
     * 文件版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Excel(name = "创建时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @Excel(name = "更新时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateUser;
}
