package com.concise.common.pojo.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020/3/10 16:02
 */
@Data
public class BaseAcceptEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String taskId;

    /**
     * 数据状态
     * 0,待接收
     * 1,待反馈
     * 2,退回
     * 3,拒接收
     * 4,已反馈
     * 98,流程终止
     */
    private String zt;

    /**
     * 数据来源(机构类型)
     * OrgTypeEnum
     *        10  "政法委"
     *        20  "法院"
     *        30  "检察院"
     *        40  "公安"
     *        41  "监管场所" 监所 看守所
     *        42  "公安戒毒场所"
     *        50  "安全"
     *        60  "司法"
     *        61  "监狱"
     *        62  "社区矫正"
     *        63  "司法戒毒场所"
     *        64  "法律援助中心"
     *        65  "司法局数据中心"
     *        70  "物管中心"
     *        80  "纪委监委"
     */
    private String sjlylx;

    /**
     * 接收单位
     */
    private String jsdw;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 接收单位名称
     */
    private String jsdwmc;

    /**
     * 推送单位
     */
    private String tsdw;

    /**
     * 推送单位名称
     */
    private String tsdwmc;

    /**
     * 送达时间
     */
    private Date sdsj;




    /**
     * 审核时间
     */
    private Date shsj;

    /**
     * 审核人员
     */
    private String shry;

    /**
     * 审核矫正机构
     */
    private String shjzjg;

    /**
     * 审核结果
     */
    private String shjg;
    /**
     * 审核备注
     */
    private String shbz;






    /**
     * 执行通知书回执文号
     */
    private String zxtzshzwh;

    /**
     * 入矫日期
     */
    private Date rjrq;

    /**
     * 社区矫正机关
     */
    private String sqjzjg;

    /**
     * 反馈人
     */
    private String fkr;

    /**
     * 反馈时间
     */
    private Date fksj;

    /**
     * 反馈结果
     */
    private String fkjg;

    /**
     * 反馈备注
     */
    private String fkbz;


    /**
     * 回执材料
     */
    private String hzcl;
    /**
     * 回执材料文件
     */
    @TableField(exist = false)
    private List<SysFileInfoVO> hzclList;
    @TableField(exist = false)
    private List<AcceptCorrectionDoc> docList;

}
