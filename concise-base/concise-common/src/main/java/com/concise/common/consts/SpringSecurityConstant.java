package com.concise.common.consts;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;

import cn.hutool.core.util.ArrayUtil;

/**
 * SpringSecurity相关常量
 *
 * <AUTHOR>
 * @date 2020/3/18 17:49
 */
@Configurable
public class SpringSecurityConstant {

    @Value("${spring.profiles.active}")
    private String active;


    /**
     * 放开权限校验的接口
     */
    public static String[] NONE_SECURITY_URL_PATTERNS = {

            //前端的
            "/favicon.ico",

            //后端的
            "/login",
            "/loginAuthCode",
            "/loginQrCode",
            "/logout",
            "/oauth/**",
            "/approval/showPic",

            //统一用户
            "/unifyUser/userReceive",
            //文件的
            "/sysFileInfo/upload",
            "/sysFileInfo/uploadOss",
            "/sysFileInfo/download",
            "/sysFileInfo/preview",

            //druid的
            "/druid/**",

            //获取验证码
            "/captcha/**",
            "/getCaptchaOpen",

            //webservice相关
            "/services/**",
            "/test/**",
            "/openApi/**",

            //随行智控驾驶舱
            "/horizontalCollaboration/**",
            "/verticalCollaboration/**",
            "/regionalCollaboration/**",

            //综管单点
            "/loginSso",

            "/investigationInfo/transcriptDownload*",

            //wps在线编辑
            "/open/wps/**",

            //调查评估签名通知
            "/investigationSign/receiveSigned",
            //调查评估签名效果预览
            "/investigationSign/preSign",
            //移动端笔录放行
            "/investigationInfo/getTranscriptById",
            "/investigationInfo/getTranscriptFillInfoById",
            "/investigationInfo/transcriptCommit",
            "/investigationSign/preSignBl",
            "/investigationSign/subSign",
            "/investigationInfo/transcriptSure",


            //设备端放行
            "/deviceTerminal/**"

    };

    {
        if (!"prod".equals(active)) {
            //swagger相关的
            SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS = ArrayUtil.addAll(SpringSecurityConstant.NONE_SECURITY_URL_PATTERNS,
                    new String[]{
                            "/doc.html",
                            "/webjars/**",
                            "/swagger-resources/**",
                            "/v2/api-docs",
                            "/v2/api-docs-ext",
                            "/configuration/ui",
                            "/configuration/security"});

        }
    }

}
