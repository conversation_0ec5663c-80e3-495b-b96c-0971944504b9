package com.concise.common.pojo.base.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * 机构类型(协同数据来源)
 * <AUTHOR>
 */

public enum OrgTypeEnum {
    /**
     * 政法委
     */
    ZHENG_FA_WEI("10", "政法委"),
    /**
     * 法院
     */
    FA_YUAN("20", "法院"),
    /**
     * 检察院
     */
    JIAN_CHA_YUAN("30", "检察院"),
    /**
     * 公安
     */
    GONG_AN("40", "公安"),
    /**
     * 监管场所
     * 监所 看守所
     */
    JIAN_GUAN_CHANG_SUO("41", "监管场所"),
    /**
     * 公安戒毒场所
     */
    GONG_AN_JIE_DU_CHANG_SUO("42", "公安戒毒场所"),
    /**
     * 安全
     */
    AN_QUAN("50", "安全"),
    /**
     * 司法
     */
    SI_FA("60", "司法"),
    /**
     * 监狱
     */
    JIAN_YU("61", "监狱"),
    /**
     * 社区矫正
     */
    SHE_QU_JIAO_ZHENG("62", "社区矫正"),
    /**
     * 司法戒毒场所
     */
    SI_FA_JIE_DU_CHANG_SUO("63", "司法戒毒场所"),
    /**
     * 法律援助中心
     */
    FA_LU_YUAN_ZHU_ZHONG_XIN("64", "法律援助中心"),
    /**
     * 司法局数据中心
     */
    SI_FA_JU_SHU_JU_ZHONG_XIN("65", "司法局数据中心"),
    /**
     * 物管中心
     */
    WU_GUAN_ZHONG_XIN("70", "物管中心"),
    /**
     * 纪委监委
     */
    JI_WEI_JIAN_WEI("80", "纪委监委")

    ;

    private final String code;
    private final String name;

    OrgTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {return code;}
    public String getName() {return name;}
    public static OrgTypeEnum getEnumByCode(String code){
        OrgTypeEnum[] enums = OrgTypeEnum.class.getEnumConstants();
        Optional<OrgTypeEnum> any = Arrays.stream(enums).filter(e -> e.getCode().equals(code) ).findAny();
        return any.orElse(OrgTypeEnum.FA_YUAN);
    }
}
