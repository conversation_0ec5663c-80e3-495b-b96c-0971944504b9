package com.concise.common.util;

import java.time.Instant;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtil {

    /**
     * 获取参数日期 一分钟之后的时间
     * @param date
     * @param second 秒
     * @return
     */
    public static Date addOneMinute(Date date, int second) {
        Instant instant = date.toInstant().plusSeconds(second);
        return Date.from(instant);
    }

    /**
     * 获取当前时间 N 天前后的日期
     * @param dayNum 天数
     * @return
     */
    public static Date getDay(int dayNum) {
        //请求参数 更新时间
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, dayNum);
        Date date = c.getTime();
        return date;
    }
}
