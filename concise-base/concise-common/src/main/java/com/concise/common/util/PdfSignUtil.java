package com.concise.common.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfFormField;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.canvas.parser.PdfCanvasProcessor;
import com.itextpdf.kernel.pdf.canvas.parser.listener.LocationTextExtractionStrategy;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.VerticalAlignment;

import lombok.extern.slf4j.Slf4j;

/**
 * PDF图片签名工具类
 * 提供图片签名、文字签名、多种签名方式组合功能
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Slf4j
@Component
public class PdfSignUtil {

    @Value("${electronicSignature.fontPath}")
    private String fontPath;

    @Value("${electronicSignature.fontSongPath}")
    private String fontSongPath;

    // 字体路径静态变量
    private static String FONT_PATH;
    private static String FONT_KAI_PATH;

    @PostConstruct
    public void init() {
        // 设置字体路径
        FONT_PATH = fontPath;
        FONT_KAI_PATH = fontSongPath;
        log.debug("字体路径设置成功，fontPath: {}, fontSongPath: {}", FONT_PATH, FONT_KAI_PATH);
    }


    /**
     * 在PDF指定位置添加图片签名
     *
     * @param pdfBytes   原PDF字节数组
     * @param imageBytes 签名图片字节数组
     * @param pageNum    页码，从1开始
     * @param x          X坐标
     * @param y          Y坐标
     * @param width      图片宽度
     * @param height     图片高度
     * @param opacity    透明度，0.0-1.0之间
     * @return 添加签名后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addImageSignature(byte[] pdfBytes, byte[] imageBytes, int pageNum, float x, float y,
                                           float width, float height, float opacity) throws IOException {
        log.debug("开始添加图片签名, 页码: {}, 坐标: ({}, {}), 尺寸: {}x{}, 透明度: {}",
                pageNum, x, y, width, height, opacity);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }

            // 获取指定页面
            PdfPage page = pdfDocument.getPage(pageNum);

            // 创建图片对象
            ImageData imageData = ImageDataFactory.create(imageBytes);
            Image image = new Image(imageData);

            // 设置图片位置和大小
            image.setFixedPosition(x, y);
            if (width > 0 && height > 0) {
                image.setWidth(width);
                image.setHeight(height);
            }

            // 设置透明度
            PdfCanvas pdfCanvas = new PdfCanvas(page);
            PdfExtGState extGState = new PdfExtGState();
            extGState.setFillOpacity(opacity);
            pdfCanvas.setExtGState(extGState);

            // 添加图片到页面
            Canvas canvas = new Canvas(pdfCanvas, page.getPageSize());
            canvas.add(image);
            canvas.close();
        }

        log.debug("图片签名添加成功");
        return outputStream.toByteArray();
    }

    /**
     * 在PDF指定位置添加文字签名
     *
     * @param pdfBytes    原PDF字节数组
     * @param text        签名文字
     * @param pageNum     页码，从1开始
     * @param x           X坐标
     * @param y           Y坐标
     * @param fontSize    字体大小
     * @param isUnderline 是否添加下划线
     * @return 添加签名后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addTextSignature(byte[] pdfBytes, String text, int pageNum, float x, float y,
                                          float fontSize, boolean isUnderline) throws IOException {
        log.debug("开始添加文字签名, 文字: {}, 页码: {}, 坐标: ({}, {}), 字体大小: {}, 下划线: {}",
                text, pageNum, x, y, fontSize, isUnderline);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }

            // 获取指定页面
            PdfPage page = pdfDocument.getPage(pageNum);

            // 创建画布
            PdfCanvas pdfCanvas = new PdfCanvas(page);
            Rectangle rectangle = new Rectangle(x, y, 300, 50);  // 为文本分配空间
            Canvas canvas = new Canvas(pdfCanvas, rectangle);

            // 加载字体
            PdfFont font = PdfFontFactory.createFont(FONT_PATH, "Identity-H", true);

            // 创建文本元素
            Text textElement = new Text(text);
            if (isUnderline) {
                textElement.setUnderline();
            }
            Paragraph paragraph = new Paragraph(textElement)
                    .setFont(font)
                    .setFontSize(fontSize)
                    .setTextAlignment(TextAlignment.LEFT);

            // 添加段落到画布
            canvas.add(paragraph);
            canvas.close();
        }

        log.debug("文字签名添加成功");
        return outputStream.toByteArray();
    }

    /**
     * 在PDF指定位置添加手写签名（含日期）
     *
     * @param pdfBytes   原PDF字节数组
     * @param imageBytes 签名图片字节数组
     * @param pageNum    页码，从1开始
     * @param x          X坐标
     * @param y          Y坐标
     * @param width      图片宽度
     * @param height     图片高度
     * @param date       日期文本，如"2024年5月15日"
     * @return 添加签名后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addHandwrittenSignatureWithDate(byte[] pdfBytes, byte[] imageBytes,
                                                         int pageNum, float x, float y,
                                                         float width, float height,
                                                         String date) throws IOException {
        log.debug("开始添加手写签名（含日期）, 页码: {}, 坐标: ({}, {}), 尺寸: {}x{}, 日期: {}",
                pageNum, x, y, width, height, date);

        // 先添加图片签名
        byte[] signedPdf = addImageSignature(pdfBytes, imageBytes, pageNum, x, y, width, height, 1.0f);

        // 再添加日期文本（在签名图片下方）
        float textY = y - 20;  // 日期文本位置为图片下方20单位
        return addTextSignature(signedPdf, date, pageNum, x, textY, 10, false);
    }

    /**
     * 在PDF指定位置添加印章
     *
     * @param pdfBytes       原PDF字节数组
     * @param sealImageBytes 印章图片字节数组
     * @param pageNum        页码，从1开始
     * @param x              X坐标
     * @param y              Y坐标
     * @param width          印章宽度
     * @param height         印章高度
     * @return 添加印章后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addSealSignature(byte[] pdfBytes, byte[] sealImageBytes,
                                          int pageNum, float x, float y,
                                          float width, float height) throws IOException {
        log.debug("开始添加印章, 页码: {}, 坐标: ({}, {}), 尺寸: {}x{}",
                pageNum, x, y, width, height);

        // 印章使用不透明图片
        return addImageSignature(pdfBytes, sealImageBytes, pageNum, x, y, width, height, 1.0f);
    }

    /**
     * 为PDF文件添加骑缝章（在多页之间添加印章）
     *
     * @param pdfBytes       原PDF字节数组
     * @param sealImageBytes 印章图片字节数组
     * @param startPage      开始页码，从1开始
     * @param endPage        结束页码
     * @param x              X坐标
     * @param sealSize       印章大小
     * @return 添加骑缝章后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addCrossPagingSeal(byte[] pdfBytes, byte[] sealImageBytes,
                                            int startPage, int endPage,
                                            float x, float sealSize) throws IOException {
        log.debug("开始添加骑缝章, 页码范围: {}-{}, X坐标: {}, 印章大小: {}",
                startPage, endPage, x, sealSize);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            int pageCount = pdfDocument.getNumberOfPages();
            if (startPage < 1 || endPage > pageCount || startPage > endPage) {
                throw new IllegalArgumentException("无效的页码范围: " + startPage + "-" + endPage);
            }

            // 创建图片对象
            ImageData imageData = ImageDataFactory.create(sealImageBytes);

            // 计算印章在每页的位置
            float halfSealSize = sealSize / 2;
            for (int pageNum = startPage; pageNum <= endPage; pageNum++) {
                PdfPage page = pdfDocument.getPage(pageNum);
                Rectangle pageSize = page.getPageSize();

                // 确定Y坐标（根据页码偶数页和奇数页的不同位置）
                float y = pageSize.getHeight() / 2;

                // 创建图片并设置位置
                Image image = new Image(imageData);
                image.setWidth(sealSize);
                image.setHeight(sealSize);

                // 计算印章的位置，使其一半在页面内，一半超出页面（骑缝效果）
                float posX;
                if (pageNum % 2 == 0) {
                    // 偶数页，印章靠右
                    posX = pageSize.getWidth() - halfSealSize;
                } else {
                    // 奇数页，印章靠左
                    posX = -halfSealSize;
                }

                // 添加图片到页面
                PdfCanvas pdfCanvas = new PdfCanvas(page);
                Canvas canvas = new Canvas(pdfCanvas, pageSize);
                image.setFixedPosition(posX, y);
                canvas.add(image);
                canvas.close();
            }
        }

        log.debug("骑缝章添加成功");
        return outputStream.toByteArray();
    }

    /**
     * 在PDF所有页面添加水印
     *
     * @param pdfBytes      原PDF字节数组
     * @param watermarkText 水印文字
     * @param opacity       透明度，0.0-1.0之间
     * @param fontSize      字体大小
     * @param rotation      旋转角度，单位为度
     * @return 添加水印后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addWatermark(byte[] pdfBytes, String watermarkText,
                                      float opacity, float fontSize, float rotation) throws IOException {
        log.debug("开始添加水印, 文字: {}, 透明度: {}, 字体大小: {}, 旋转角度: {}",
                watermarkText, opacity, fontSize, rotation);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 加载字体
            PdfFont font = PdfFontFactory.createFont(FONT_PATH, "Identity-H", true);

            // 计算旋转角度的弧度值
            double radians = Math.toRadians(rotation);

            // 为每一页添加水印
            int pageCount = pdfDocument.getNumberOfPages();
            for (int i = 1; i <= pageCount; i++) {
                PdfPage page = pdfDocument.getPage(i);
                Rectangle pageSize = page.getPageSize();

                // 计算水印位置（居中）
                float x = pageSize.getWidth() / 2;
                float y = pageSize.getHeight() / 2;

                // 创建透明文本的画布
                PdfCanvas pdfCanvas = new PdfCanvas(page);
                pdfCanvas.saveState();

                // 设置透明度
                PdfExtGState extGState = new PdfExtGState();
                extGState.setFillOpacity(opacity);
                pdfCanvas.setExtGState(extGState);

                // 创建一个可以旋转的段落
                Canvas canvas = new Canvas(pdfCanvas, pageSize);

                // 创建文本元素
                Paragraph p = new Paragraph(watermarkText)
                        .setFont(font)
                        .setFontSize(fontSize)
                        .setFontColor(ColorConstants.LIGHT_GRAY)
                        .setTextAlignment(TextAlignment.CENTER);

                // 将文字放置在指定位置并应用旋转
                canvas.showTextAligned(p, x, y, i, TextAlignment.CENTER, VerticalAlignment.MIDDLE, (float) radians);

                pdfCanvas.restoreState();
                canvas.close();
            }
        }

        log.debug("水印添加成功");
        return outputStream.toByteArray();
    }

    /**
     * 将Base64编码的PDF添加图片签名
     *
     * @param pdfBase64   Base64编码的PDF
     * @param imageBase64 Base64编码的图片
     * @param pageNum     页码，从1开始
     * @param x           X坐标
     * @param y           Y坐标
     * @param width       图片宽度
     * @param height      图片高度
     * @param opacity     透明度，0.0-1.0之间
     * @return Base64编码的签名后PDF
     * @throws IOException IO异常
     */
    public static String addImageSignatureBase64(String pdfBase64, String imageBase64,
                                                 int pageNum, float x, float y,
                                                 float width, float height, float opacity) throws IOException {
        // 解码Base64
        byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);
        byte[] imageBytes = Base64.getDecoder().decode(imageBase64);

        // 添加签名
        byte[] signedPdfBytes = addImageSignature(pdfBytes, imageBytes, pageNum, x, y, width, height, opacity);

        // 返回Base64编码结果
        return Base64.getEncoder().encodeToString(signedPdfBytes);
    }

    /**
     * 将PDF字节数组转换为MultipartFile
     *
     * @param pdfBytes PDF字节数组
     * @param fileName 文件名
     * @return MultipartFile对象
     * @throws IOException IO异常
     */
    public static MultipartFile convertPdfToMultipartFile(byte[] pdfBytes, String fileName) throws IOException {
        return new MockMultipartFile(
                fileName,
                fileName + ".pdf",
                "application/pdf",
                pdfBytes
        );
    }

    /**
     * 将Base64编码的PDF转换为MultipartFile
     *
     * @param pdfBase64 Base64编码的PDF
     * @param fileName  文件名
     * @return MultipartFile对象
     * @throws IOException IO异常
     */
    public static MultipartFile convertBase64ToMultipartFile(String pdfBase64, String fileName) throws IOException {
        byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);
        return convertPdfToMultipartFile(pdfBytes, fileName);
    }

    /**
     * 将签名后的PDF输出到HttpServletResponse
     *
     * @param response HTTP响应对象
     * @param pdfBytes PDF字节数组
     * @param fileName 文件名（不含扩展名）
     * @throws IOException IO异常
     */
    public static void outputPdfToResponse(HttpServletResponse response, byte[] pdfBytes, String fileName) throws IOException {
        // 设置响应头
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=" +
                java.net.URLEncoder.encode(fileName + ".pdf", "UTF-8"));

        // 输出PDF内容
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            outputStream.write(pdfBytes);
            outputStream.flush();
        }
    }

    /**
     * 将签名后的PDF保存到文件
     *
     * @param pdfBytes PDF字节数组
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void savePdfToFile(byte[] pdfBytes, String filePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(pdfBytes);
        }
    }

    /**
     * 生成临时文件路径
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 临时文件路径
     */
    public static String generateTempFilePath(String prefix, String suffix) {
        String tempDir = System.getProperty("java.io.tmpdir");
        String fileName = prefix + UUID.randomUUID().toString() + suffix;
        return Paths.get(tempDir, fileName).toString();
    }

    /**
     * 生成白底黑字的图片
     *
     * @param width      图片宽度
     * @param height     图片高度
     * @param text       图片文字内容（如果为空或null则生成随机文字）
     * @param outputPath 输出路径（如果为空或null则保存到下载目录）
     * @return 生成的图片文件路径
     * @throws IOException IO异常
     */
    public static String generateTextImage(int width, int height, String text, String outputPath) throws IOException {
        log.debug("开始生成白底黑字图片，尺寸: {}x{}, 文字: {}", width, height, text);

        // 创建图片缓冲区对象
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        // 获取Graphics2D绘图对象
        java.awt.Graphics2D g2d = bufferedImage.createGraphics();

        // 设置背景色（白色）并填充
        g2d.setColor(java.awt.Color.WHITE);
        g2d.fillRect(0, 0, width, height);

        // 设置文字颜色（黑色）
        g2d.setColor(java.awt.Color.BLACK);

        // 设置抗锯齿
        g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);

        // 加载字体
        java.awt.Font font;
        try {
            // 尝试加载系统字体，支持中文
            font = new java.awt.Font("宋体", java.awt.Font.PLAIN, 20);
        } catch (Exception e) {
            // 如果加载失败，使用默认字体
            font = new java.awt.Font(java.awt.Font.SANS_SERIF, java.awt.Font.PLAIN, 20);
        }
        g2d.setFont(font);

        // 如果没有指定文字，则生成随机文字
        if (text == null || text.isEmpty()) {
            // 生成随机中文文字
            StringBuilder sb = new StringBuilder();
            String[] randomChars = {"签名", "文档", "认证", "盖章", "有效", "审核", "确认", "法律", "电子", "安全"};
            sb.append(randomChars[(int) (Math.random() * randomChars.length)]);
            sb.append(randomChars[(int) (Math.random() * randomChars.length)]);
            text = sb.toString();
        }

        // 计算文本绘制位置（居中）
        java.awt.FontMetrics fontMetrics = g2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(text);
        int textHeight = fontMetrics.getHeight();
        int x = (width - textWidth) / 2;
        int y = (height - textHeight) / 2 + fontMetrics.getAscent();

        // 绘制文字
        g2d.drawString(text, x, y);

        // 添加边框
        g2d.setColor(java.awt.Color.LIGHT_GRAY);
        g2d.drawRect(0, 0, width - 1, height - 1);

        // 释放图形资源
        g2d.dispose();

        // 如果没有指定输出路径，则保存到下载目录
        if (outputPath == null || outputPath.isEmpty()) {
            String userHome = System.getProperty("user.home");
            String downloadsPath = Paths.get(userHome, "Downloads").toString();

            // 确保下载目录存在
            File downloadsDir = new File(downloadsPath);
            if (!downloadsDir.exists()) {
                downloadsDir.mkdirs();
            }

            outputPath = Paths.get(downloadsPath, "sign_image_" + System.currentTimeMillis() + ".png").toString();
        }

        // 确保输出目录存在
        File outputFile = new File(outputPath);
        if (outputFile.getParentFile() != null && !outputFile.getParentFile().exists()) {
            outputFile.getParentFile().mkdirs();
        }

        // 保存图片
        ImageIO.write(bufferedImage, "png", outputFile);

        log.debug("白底黑字图片生成成功，保存路径: {}", outputPath);
        return outputPath;
    }

    /**
     * 生成白底黑字的图片（使用默认尺寸和随机文字）
     *
     * @return 生成的图片文件路径
     * @throws IOException IO异常
     */
    public static String generateTextImage() throws IOException {
        return generateTextImage(200, 50, null, null);
    }

    /**
     * 读取图片文件并转换为字节数组
     *
     * @param imagePath 图片文件路径
     * @return 图片字节数组
     * @throws IOException IO异常
     */
    public static byte[] readImageFileToBytes(String imagePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(imagePath)) {
            return IOUtils.toByteArray(fis);
        }
    }

    /**
     * 读取图片文件并转换为Base64字符串
     *
     * @param imagePath 图片文件路径
     * @return Base64编码的图片字符串
     * @throws IOException IO异常
     */
    public static String readImageFileToBase64(String imagePath) throws IOException {
        byte[] imageBytes = readImageFileToBytes(imagePath);
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 获取PDF文件页数
     *
     * @param pdfBytes PDF字节数组
     * @return 页数
     * @throws IOException IO异常
     */
    public static int getPdfPageCount(byte[] pdfBytes) throws IOException {
        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfDocument pdfDocument = new PdfDocument(reader)) {
            return pdfDocument.getNumberOfPages();
        }
    }

    /**
     * 获取PDF文件页面尺寸
     *
     * @param pdfBytes PDF字节数组
     * @param pageNum  页码（从1开始）
     * @return 页面尺寸Rectangle对象
     * @throws IOException IO异常
     */
    public static Rectangle getPdfPageSize(byte[] pdfBytes, int pageNum) throws IOException {
        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfDocument pdfDocument = new PdfDocument(reader)) {
            if (pageNum < 1 || pageNum > pdfDocument.getNumberOfPages()) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }
            return pdfDocument.getPage(pageNum).getPageSize();
        }
    }

    /**
     * 在PDF表单的指定字段中添加图片
     *
     * @param pdfBytes   原PDF字节数组
     * @param imageBytes 图片字节数组
     * @param fieldName  表单字段名称
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addImageToFormField(byte[] pdfBytes, byte[] imageBytes, String fieldName) throws IOException {
        log.debug("开始在表单字段 {} 中添加图片", fieldName);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 获取表单
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, false);
            if (form == null) {
                throw new IllegalArgumentException("PDF文档不包含表单");
            }

            // 获取指定的表单字段
            PdfFormField field = form.getField(fieldName);
            if (field == null) {
                throw new IllegalArgumentException("表单字段不存在: " + fieldName);
            }

            // 获取字段的位置和尺寸
            Rectangle rect = field.getWidgets().get(0).getRectangle().toRectangle();

            // 获取字段所在的页码
            int pageNum = 1; // 默认为第一页
            if (field.getWidgets() != null && !field.getWidgets().isEmpty()) {
                pageNum = pdfDocument.getPageNumber(field.getWidgets().get(0).getPage());
            }

            // 获取该页面
            PdfPage page = pdfDocument.getPage(pageNum);

            // 创建图片对象
            ImageData imageData = ImageDataFactory.create(imageBytes);
            Image image = new Image(imageData);

            // 设置图片尺寸适应字段区域
            image.setWidth(rect.getWidth());
            image.setHeight(rect.getHeight());
            float x = rect.getX();
            float y = rect.getY();

            // 移除表单字段
            form.removeField(fieldName);

            // 在原字段位置添加图片
            PdfCanvas pdfCanvas = new PdfCanvas(page);
            Canvas canvas = new Canvas(pdfCanvas, rect);
            image.setFixedPosition(x, y);
            canvas.add(image);
            canvas.close();

            // 确保所有表单不会再次被编辑（可选）
            form.flattenFields();
        }

        log.debug("表单字段图片添加成功");
        return outputStream.toByteArray();
    }

    /**
     * 在PDF表单的指定字段中添加Base64编码的图片
     *
     * @param pdfBase64   Base64编码的PDF
     * @param imageBase64 Base64编码的图片
     * @param fieldName   表单字段名称
     * @return Base64编码的处理后PDF
     * @throws IOException IO异常
     */
    public static String addImageToFormFieldBase64(String pdfBase64, String imageBase64, String fieldName) throws IOException {
        // 解码Base64
        byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);
        byte[] imageBytes = Base64.getDecoder().decode(imageBase64);

        // 添加图片到表单字段
        byte[] resultPdfBytes = addImageToFormField(pdfBytes, imageBytes, fieldName);

        // 返回Base64编码结果
        return Base64.getEncoder().encodeToString(resultPdfBytes);
    }

    /**
     * 获取PDF表单中所有字段名称
     *
     * @param pdfBytes PDF字节数组
     * @return 字段名称数组
     * @throws IOException IO异常
     */
    public static String[] getFormFieldNames(byte[] pdfBytes) throws IOException {
        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfDocument pdfDocument = new PdfDocument(reader)) {

            // 获取表单
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, false);
            if (form == null) {
                return new String[0];
            }

            // 获取所有字段名称
            return form.getFormFields().keySet().toArray(new String[0]);
        }
    }

    /**
     * 在PDF多个表单字段中添加同一张图片
     *
     * @param pdfBytes   原PDF字节数组
     * @param imageBytes 图片字节数组
     * @param fieldNames 表单字段名称数组
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addImageToMultipleFormFields(byte[] pdfBytes, byte[] imageBytes, String[] fieldNames) throws IOException {
        log.debug("开始在多个表单字段中添加图片，字段数量: {}", fieldNames.length);

        byte[] resultPdfBytes = pdfBytes;
        for (String fieldName : fieldNames) {
            resultPdfBytes = addImageToFormField(resultPdfBytes, imageBytes, fieldName);
        }

        log.debug("多个表单字段图片添加成功");
        return resultPdfBytes;
    }

    /**
     * 在PDF多个表单字段中添加不同图片
     *
     * @param pdfBytes      原PDF字节数组
     * @param fieldImageMap 字段名到图片字节数组的映射
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addImagesToFormFields(byte[] pdfBytes, java.util.Map<String, byte[]> fieldImageMap) throws IOException {
        log.debug("开始在多个表单字段中添加不同图片，字段数量: {}", fieldImageMap.size());

        byte[] resultPdfBytes = pdfBytes;
        for (java.util.Map.Entry<String, byte[]> entry : fieldImageMap.entrySet()) {
            String fieldName = entry.getKey();
            byte[] imageBytes = entry.getValue();
            resultPdfBytes = addImageToFormField(resultPdfBytes, imageBytes, fieldName);
        }

        log.debug("多个表单字段不同图片添加成功");
        return resultPdfBytes;
    }

    /**
     * 在PDF多个表单字段中添加Base64编码的同一张图片
     *
     * @param pdfBase64   Base64编码的PDF
     * @param imageBase64 Base64编码的图片
     * @param fieldNames  表单字段名称数组
     * @return Base64编码的处理后PDF
     * @throws IOException IO异常
     */
    public static String addImageToMultipleFormFieldsBase64(String pdfBase64, String imageBase64, String[] fieldNames) throws IOException {
        // 解码Base64
        byte[] pdfBytes = Base64.getDecoder().decode(pdfBase64);
        byte[] imageBytes = Base64.getDecoder().decode(imageBase64);

        // 添加图片到多个表单字段
        byte[] resultPdfBytes = addImageToMultipleFormFields(pdfBytes, imageBytes, fieldNames);

        // 返回Base64编码结果
        return Base64.getEncoder().encodeToString(resultPdfBytes);
    }

    /**
     * 在PDF中定位指定文本，并返回文本在每个页面上的位置信息
     * 支持部分匹配（子字符串）
     *
     * @param pdfBytes   PDF文件字节数组
     * @param searchText 要查找的文本
     * @return 包含文本位置信息的列表，每个元素包含页码、矩形区域和文本内容
     * @throws IOException 如果文件处理过程中发生错误
     */
    public static List<TextLocation> locateTextInPdf(byte[] pdfBytes, String searchText) throws IOException {
        log.debug("开始在PDF中定位文本: {}", searchText);
        List<TextLocation> locations = new ArrayList<>();

        // 打开PDF文档
        try (PdfDocument pdfDoc = new PdfDocument(new PdfReader(new ByteArrayInputStream(pdfBytes)))) {

            // 遍历PDF的每一页
            for (int pageNum = 1; pageNum <= pdfDoc.getNumberOfPages(); pageNum++) {
                final int currentPage = pageNum;
                PdfPage page = pdfDoc.getPage(pageNum);

                // 使用自定义文本提取策略获取文字坐标
                class CustomLocationTextExtractionStrategy extends LocationTextExtractionStrategy {
                    private StringBuilder pageText = new StringBuilder();
                    private List<TextInfo> textInfoList = new ArrayList<>();

                    @Override
                    public void eventOccurred(com.itextpdf.kernel.pdf.canvas.parser.data.IEventData data,
                                              com.itextpdf.kernel.pdf.canvas.parser.EventType type) {
                        if (data instanceof com.itextpdf.kernel.pdf.canvas.parser.data.TextRenderInfo) {
                            com.itextpdf.kernel.pdf.canvas.parser.data.TextRenderInfo renderInfo =
                                    (com.itextpdf.kernel.pdf.canvas.parser.data.TextRenderInfo) data;

                            // 获取文字内容
                            String text = renderInfo.getText();
                            if (text != null && !text.isEmpty()) {
                                pageText.append(text);

                                // 获取文字基线坐标
                                com.itextpdf.kernel.geom.LineSegment baseline = renderInfo.getBaseline();
                                com.itextpdf.kernel.geom.Vector startPoint = baseline.getStartPoint();
                                com.itextpdf.kernel.geom.Vector endPoint = baseline.getEndPoint();

                                // 获取文字顶线坐标
                                com.itextpdf.kernel.geom.LineSegment ascentLine = renderInfo.getAscentLine();
                                com.itextpdf.kernel.geom.Vector topLeft = ascentLine.getStartPoint();
                                com.itextpdf.kernel.geom.Vector topRight = ascentLine.getEndPoint();

                                // 计算文字矩形区域
                                float x = startPoint.get(0);
                                float y = startPoint.get(1);
                                float width = endPoint.get(0) - startPoint.get(0);
                                float height = topLeft.get(1) - startPoint.get(1);

                                // 保存文字信息
                                textInfoList.add(new TextInfo(text, x, y, width, height));

                                log.debug("文字: '{}' 起始坐标: ({}, {}), 尺寸: {}x{}",
                                        text, x, y, width, height);

                                // 检查是否包含搜索文本（部分匹配）
                                if (text.contains(searchText)) {
                                    // 创建一个矩形区域
                                    Rectangle textRect = new Rectangle(x, y, width, height);

                                    // 保存文本位置信息
                                    locations.add(new TextLocation(currentPage, textRect, searchText));

                                    log.debug("在第 {} 页找到部分匹配文本: '{}' 在 '{}' 中, 坐标: ({}, {}), 尺寸: {}x{}",
                                            currentPage, searchText, text, x, y, width, height);
                                }
                            }
                        }

                        // 调用父类方法以保持原有功能
                        super.eventOccurred(data, type);
                    }

                    @Override
                    public String getResultantText() {
                        return pageText.toString();
                    }

                    public List<TextInfo> getTextInfoList() {
                        return textInfoList;
                    }
                }

                // 创建自定义策略实例
                CustomLocationTextExtractionStrategy strategy = new CustomLocationTextExtractionStrategy();

                // 提取当前页文本
                PdfCanvasProcessor processor = new PdfCanvasProcessor(strategy);
                processor.processPageContent(page);

                // 获取当前页文本内容
                String pageText = strategy.getResultantText();
                List<TextInfo> textInfoList = strategy.getTextInfoList();

                // 如果页面文本包含搜索文本但单个文本块中没有找到匹配，尝试跨文本块组合查找
                if (pageText.contains(searchText) && locations.isEmpty()) {
                    log.debug("在第 {} 页找到文本: '{}', 尝试跨文本块组合匹配", currentPage, searchText);

                    // 查找可能的连续文本块组合
                    for (int i = 0; i < textInfoList.size(); i++) {
                        StringBuilder combinedText = new StringBuilder();
                        float startX = 0;
                        float startY = 0;
                        float totalWidth = 0;
                        float height = 0;

                        for (int j = i; j < textInfoList.size(); j++) {
                            TextInfo info = textInfoList.get(j);
                            if (j == i) {
                                startX = info.x;
                                startY = info.y;
                                height = info.height;
                            }

                            combinedText.append(info.text);
                            totalWidth += info.width;

                            // 检查组合的文本是否包含搜索文本
                            if (combinedText.toString().contains(searchText)) {
                                // 为找到的位置创建一个矩形区域
                                Rectangle textRect = new Rectangle(startX, startY, totalWidth, height);

                                // 保存文本位置信息
                                locations.add(new TextLocation(currentPage, textRect, searchText));

                                log.debug("在第 {} 页找到组合匹配位置，搜索文本 '{}' 在组合文本 '{}' 中, 坐标: ({}, {}), 尺寸: {}x{}",
                                        currentPage, searchText, combinedText, startX, startY, totalWidth, height);

                                break; // 找到一个匹配就停止当前组合
                            }

                            // 如果组合文本已经很长但仍未找到匹配，提前停止以提高效率
                            if (combinedText.length() > searchText.length() * 3) {
                                break;
                            }
                        }

                        // 如果已经找到匹配，停止继续查找
                        if (!locations.isEmpty()) {
                            break;
                        }
                    }
                }
            }
        }

        log.debug("文本定位完成，共找到 {} 个匹配位置", locations.size());
        return locations;
    }

    /**
     * 内部类，用于存储文本信息
     */
    private static class TextInfo {
        String text;
        float x;
        float y;
        float width;
        float height;

        public TextInfo(String text, float x, float y, float width, float height) {
            this.text = text;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
    }

    /**
     * 在PDF中定位指定文本，并在文本附近添加图片
     *
     * @param pdfBytes   PDF文件字节数组
     * @param searchText 要查找的文本
     * @param imageBytes 要添加的图片字节数组
     * @param offsetX    图片相对于文本的X偏移量
     * @param offsetY    图片相对于文本的Y偏移量
     * @param width      图片宽度
     * @param height     图片高度
     * @return 处理后的PDF字节数组
     * @throws IOException 如果文件处理过程中发生错误
     */
    public static byte[] addImageNearText(byte[] pdfBytes, String searchText, byte[] imageBytes,
                                          float offsetX, float offsetY, float width, float height) throws IOException {
        log.debug("开始在文本 '{}' 附近添加图片", searchText);

        // 定位文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的文本: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        // 获取第一个匹配位置
        TextLocation firstMatch = locations.get(0);
        int pageNum = firstMatch.getPageNumber();
        Rectangle textRect = firstMatch.getRectangle();

        // 计算图片位置
        float imageX = textRect.getX() + offsetX;
        float imageY = textRect.getY() + offsetY;

        // 在找到的文本附近添加图片
        return addImageSignature(pdfBytes, imageBytes, pageNum, imageX, imageY, width, height, 1.0f);
    }

    /**
     * 在PDF中定位所有指定文本，并在每个文本附近添加相同的图片
     *
     * @param pdfBytes   PDF文件字节数组
     * @param searchText 要查找的文本
     * @param imageBytes 要添加的图片字节数组
     * @param offsetX    图片相对于文本的X偏移量
     * @param offsetY    图片相对于文本的Y偏移量
     * @param width      图片宽度
     * @param height     图片高度
     * @return 处理后的PDF字节数组
     * @throws IOException 如果文件处理过程中发生错误
     */
    public static byte[] addImageToAllTextLocations(byte[] pdfBytes, String searchText, byte[] imageBytes,
                                                    float offsetX, float offsetY, float width, float height) throws IOException {
        log.debug("开始在所有 '{}' 文本附近添加图片", searchText);

        // 定位所有文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的文本: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        byte[] resultPdf = pdfBytes;

        // 为每个找到的位置添加图片
        for (TextLocation location : locations) {
            int pageNum = location.getPageNumber();
            Rectangle textRect = location.getRectangle();

            // 计算图片位置
            float imageX = textRect.getX() + offsetX;
            float imageY = textRect.getY() + offsetY;

            // 添加图片
            resultPdf = addImageSignature(resultPdf, imageBytes, pageNum, imageX, imageY, width, height, 1.0f);
        }

        return resultPdf;
    }

    /**
     * 高亮显示PDF中的指定文本
     *
     * @param pdfBytes   PDF文件字节数组
     * @param searchText 要高亮显示的文本
     * @param opacity    高亮透明度，0.0-1.0之间
     * @return 处理后的PDF字节数组
     * @throws IOException 如果文件处理过程中发生错误
     */
    public static byte[] highlightText(byte[] pdfBytes, String searchText, float opacity) throws IOException {
        log.debug("开始高亮显示文本: {}", searchText);

        // 定位文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的文本: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 为每个找到的位置添加高亮
            for (TextLocation location : locations) {
                int pageNum = location.getPageNumber();
                Rectangle textRect = location.getRectangle();

                // 确保页码有效
                if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                    continue;
                }

                // 获取指定页面
                PdfPage page = pdfDocument.getPage(pageNum);

                // 创建画布
                PdfCanvas pdfCanvas = new PdfCanvas(page);

                // 设置透明度
                PdfExtGState extGState = new PdfExtGState();
                extGState.setFillOpacity(opacity);
                pdfCanvas.setExtGState(extGState);

                // 设置高亮颜色（黄色）
                pdfCanvas.setFillColor(ColorConstants.YELLOW);

                // 绘制矩形高亮
                pdfCanvas.rectangle(textRect.getX(), textRect.getY(),
                        textRect.getWidth(), textRect.getHeight());
                pdfCanvas.fill();
            }
        }

        log.debug("文本高亮处理完成");
        return outputStream.toByteArray();
    }

    /**
     * 文本位置信息类
     */
    public static class TextLocation {
        private int pageNumber;
        private com.itextpdf.kernel.geom.Rectangle rectangle;
        private String text;

        public TextLocation(int pageNumber, com.itextpdf.kernel.geom.Rectangle rectangle, String text) {
            this.pageNumber = pageNumber;
            this.rectangle = rectangle;
            this.text = text;
        }

        public int getPageNumber() {
            return pageNumber;
        }

        public com.itextpdf.kernel.geom.Rectangle getRectangle() {
            return rectangle;
        }

        public String getText() {
            return text;
        }

        @Override
        public String toString() {
            return "页码: " + pageNumber +
                    ", 位置: [x=" + rectangle.getX() +
                    ", y=" + rectangle.getY() +
                    ", 宽=" + rectangle.getWidth() +
                    ", 高=" + rectangle.getHeight() +
                    "], 文本: " + text;
        }
    }

    /**
     * 在PDF指定页面上按照排序位置添加单个图片
     *
     * @param pdfBytes         原PDF字节数组
     * @param targetImageBytes 要添加的图片字节数组
     * @param pageNum          页码，从1开始
     * @param targetPosition   要放置图片的位置序号（从1开始，表示第几个位置）
     * @param startX           第一张图片左下角的X坐标
     * @param startY           第一张图片左下角的Y坐标
     * @param imageWidth       图片的宽度
     * @param imageHeight      图片的高度
     * @param horizontalGap    图片之间的水平间距
     * @param verticalGap      行之间的垂直间距
     * @param marginLeft       左边距（除第一行外）
     * @param marginRight      右边距
     * @param marginBottom     底部边距
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addSingleImageAtOrderedPosition(byte[] pdfBytes, byte[] targetImageBytes, int pageNum,
                                                         int targetPosition, float startX, float startY,
                                                         float imageWidth, float imageHeight,
                                                         float horizontalGap, float verticalGap,
                                                         float marginLeft, float marginRight,
                                                         float marginBottom) throws IOException {
        log.debug("开始在PDF页面 {} 的第 {} 个位置添加图片", pageNum, targetPosition);

        if (targetPosition < 1) {
            throw new IllegalArgumentException("位置序号必须大于等于1");
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }

            // 获取页面尺寸
            PdfPage page = pdfDocument.getPage(pageNum);
            Rectangle pageSize = page.getPageSize();
            float pageWidth = pageSize.getWidth();

            // 计算第一行可用宽度
            float firstRowWidth = pageWidth - startX - marginRight;
            // 计算其他行可用宽度
            float otherRowWidth = pageWidth - marginLeft - marginRight;

            // 计算第一行可以放置的图片数量
            int firstRowImages = (int) Math.floor((firstRowWidth + horizontalGap) / (imageWidth + horizontalGap));
            // 计算其他行可以放置的图片数量
            int otherRowImages = (int) Math.floor((otherRowWidth + horizontalGap) / (imageWidth + horizontalGap));

            if (firstRowImages <= 0 || otherRowImages <= 0) {
                log.warn("无法放置图片：行宽度不足");
                return pdfBytes;
            }

            // 计算目标位置在哪一行以及行内位置
            int targetRow;    // 目标行号（0表示第一行）
            int positionInRow; // 行内位置（从0开始）

            if (targetPosition <= firstRowImages) {
                // 目标位置在第一行
                targetRow = 0;
                positionInRow = targetPosition - 1;
            } else {
                // 计算在第一行之后的位置
                int remainingPosition = targetPosition - firstRowImages;
                // 计算目标行号（减1是因为第一行已经单独处理）
                targetRow = (remainingPosition - 1) / otherRowImages + 1;
                // 计算行内位置
                positionInRow = (remainingPosition - 1) % otherRowImages;
            }

            // 计算Y坐标（每行向下移动）
            float currentY;
            if (targetRow == 0) {
                currentY = startY;
            } else {
                // 重新设计行距计算逻辑
                // 每行的位置 = 起始位置 - (当前行号 * (图片高度 + 垂直间距))
                currentY = startY - targetRow * verticalGap;
            }

            // 检查是否超出底部边距
            if (currentY <= marginBottom) {
                log.warn("目标位置超出底部边距，无法放置图片");
                return pdfBytes;
            }

            // 计算X坐标
            float currentX;
            if (targetRow == 0) {
                // 第一行
                currentX = startX + positionInRow * (imageWidth + horizontalGap);
            } else {
                // 其他行
                currentX = marginLeft + positionInRow * (imageWidth + horizontalGap);
            }

            // 检查X坐标是否超出右边距
            if (targetRow == 0 && currentX + imageWidth > pageWidth - marginRight) {
                log.warn("目标位置超出右边距，无法放置图片");
                return pdfBytes;
            } else if (targetRow > 0 && currentX + imageWidth > pageWidth - marginRight) {
                log.warn("目标位置超出右边距，无法放置图片");
                return pdfBytes;
            }

            // 在计算出的位置放置图片
            PdfCanvas pdfCanvas = new PdfCanvas(page);
            ImageData imageData = ImageDataFactory.create(targetImageBytes);
            Image image = new Image(imageData);

            // 设置图片位置和大小
            image.setFixedPosition(currentX, currentY);
            if (imageWidth > 0 && imageHeight > 0) {
                image.scaleToFit(imageWidth, imageHeight);
            }

            // 添加图片到页面
            Canvas canvas = new Canvas(pdfCanvas, pdfDocument, page.getPageSize());
            canvas.add(image);
            canvas.close();

            log.debug("成功在位置 ({}, {}) 添加图片", currentX, currentY);
        } catch (Exception e) {
            log.error("在指定位置添加图片时出错: {}", e.getMessage(), e);
            throw new IOException("在指定位置添加图片时出错: " + e.getMessage(), e);
        }

        return outputStream.toByteArray();
    }

    /**
     * 根据关键字定位后，在其右侧按顺序位置添加单个图片
     *
     * @param pdfBytes      原PDF字节数组
     * @param searchText    要查找的关键字文本
     * @param keyWordIndex  第几个关键字（从0开始）
     * @param offsetX       图片起始位置相对于关键字的X轴偏移量
     * @param offsetY       图片起始位置相对于关键字的Y轴偏移量
     * @param imageBytes    要添加的图片字节数组
     * @param position      要放置图片的位置序号（从1开始，表示第几个位置）
     * @param imageWidth    图片的宽度
     * @param imageHeight   图片的高度
     * @param horizontalGap 图片之间的水平间距
     * @param verticalGap   行之间的垂直间距
     * @param marginLeft    左边距（除第一行外）
     * @param marginRight   右边距
     * @param marginBottom  底部边距
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addSingleImageAtOrderedPositionAfterKeyword(byte[] pdfBytes, String searchText,
                                                                     int keyWordIndex, float offsetX, float offsetY,
                                                                     byte[] imageBytes, int position,
                                                                     float imageWidth, float imageHeight,
                                                                     float horizontalGap, float verticalGap,
                                                                     float marginLeft, float marginRight,
                                                                     float marginBottom) throws IOException {
        log.debug("开始查找关键字'{}',并在其右侧按顺序位置添加图片", searchText);

        // 定位文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的关键字: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        // 获取指定索引位置的关键字（如果索引越界，则使用第一个匹配位置）
        TextLocation matchedLocation;
        try {
            matchedLocation = locations.get(keyWordIndex);
        } catch (IndexOutOfBoundsException e) {
            log.warn("关键字索引 {} 越界，使用第一个匹配位置", keyWordIndex);
            matchedLocation = locations.get(0);
        }

        int pageNum = matchedLocation.getPageNumber();
        Rectangle textRect = matchedLocation.getRectangle();

        // 计算图片起始位置
        float startX = textRect.getX() + offsetX;
        float startY = textRect.getY() + offsetY;

        log.debug("找到关键字位置：页码={}, 位置=({}, {}), 将在偏移({}, {})后放置图片，起始坐标=({}, {})",
                pageNum, textRect.getX(), textRect.getY(), offsetX, offsetY, startX, startY);

        // 使用计算得到的起始位置调用单个图片按位置放置方法
        return addSingleImageAtOrderedPosition(pdfBytes, imageBytes, pageNum, position, startX, startY,
                imageWidth, imageHeight, horizontalGap, verticalGap,
                marginLeft, marginRight, marginBottom);
    }

    /**
     * 在PDF指定页面上添加多个图片，并自动排列（从上往下，从左往右）
     *
     * @param pdfBytes       原PDF字节数组
     * @param imageBytesList 要添加的图片字节数组列表
     * @param pageNum        页码（从1开始）
     * @param startX         第一张图片左下角的X坐标（iText坐标系）
     * @param startY         第一张图片左下角的Y坐标（iText坐标系）
     * @param imageWidth     每个图片的宽度
     * @param imageHeight    每个图片的高度
     * @param horizontalGap  图片之间的水平间距
     * @param verticalGap    行之间的垂直间距（当前行图片底部到下一行图片顶部的距离）
     * @param marginLeft     左边距（除第一行外）
     * @param marginRight    右边距
     * @param marginBottom   底部边距（iText坐标系，通常是页面底部位置的Y值）
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addMultipleImagesAutoLayout(byte[] pdfBytes, List<byte[]> imageBytesList, int pageNum,
                                                     float startX, float startY, float imageWidth, float imageHeight,
                                                     float horizontalGap, float verticalGap, float marginLeft,
                                                     float marginRight, float marginBottom) throws IOException {
        log.debug("开始在PDF页面 {} 添加多个图片并自动布局", pageNum);

        // 确保间距不为负数
        horizontalGap = Math.max(0, horizontalGap);
        verticalGap = Math.max(0, verticalGap);

        log.debug("使用参数: 起始坐标=({}, {}), 图片尺寸={}x{}, 水平间距={}, 垂直间距={}",
                startX, startY, imageWidth, imageHeight, horizontalGap, verticalGap);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }

            // 获取页面尺寸
            PdfPage page = pdfDocument.getPage(pageNum);
            Rectangle pageSize = page.getPageSize();
            float pageWidth = pageSize.getWidth();

            // 计算第一行可用宽度和可放置图片数量
            float firstRowWidth = pageWidth - startX - marginRight;
            int firstRowImages = (int) Math.floor((firstRowWidth + horizontalGap) / (imageWidth + horizontalGap));

            // 计算其他行可用宽度和可放置图片数量
            float otherRowWidth = pageWidth - marginLeft - marginRight;
            int otherRowImages = (int) Math.floor((otherRowWidth + horizontalGap) / (imageWidth + horizontalGap));

            if (firstRowImages <= 0 || otherRowImages <= 0) {
                log.warn("无法放置图片：行宽度不足");
                return pdfBytes;
            }

            // 为每个图片计算位置并放置
            for (int i = 0; i < imageBytesList.size(); i++) {
                // 计算当前图片的位置（从1开始）
                int position = i + 1;

                // 计算目标位置在哪一行以及行内位置
                int targetRow;    // 目标行号（0表示第一行）
                int positionInRow; // 行内位置（从0开始）

                if (position <= firstRowImages) {
                    // 目标位置在第一行
                    targetRow = 0;
                    positionInRow = position - 1;
                } else {
                    // 计算在第一行之后的位置
                    int remainingPosition = position - firstRowImages;
                    // 计算目标行号（减1是因为第一行已经单独处理）
                    targetRow = (remainingPosition - 1) / otherRowImages + 1;
                    // 计算行内位置
                    positionInRow = (remainingPosition - 1) % otherRowImages;
                }

                // 计算Y坐标（每行向下移动）
                float currentY;
                if (targetRow == 0) {
                    currentY = startY;
                } else {
                    // 重新设计行距计算逻辑
                    // 每行的位置 = 起始位置 - (当前行号 * (图片高度 + 垂直间距))
                    currentY = startY - targetRow * verticalGap;
                }

                // 检查是否超出底部边距
                if (currentY <= marginBottom) {
                    log.warn("图片位置 {} 超出底部边距，停止添加后续图片", position);
                    break;
                }

                // 计算X坐标
                float currentX;
                if (targetRow == 0) {
                    // 第一行
                    currentX = startX + positionInRow * (imageWidth + horizontalGap);
                } else {
                    // 其他行
                    currentX = marginLeft + positionInRow * (imageWidth + horizontalGap);
                }

                // 检查X坐标是否超出右边距
                if ((targetRow == 0 && currentX + imageWidth > pageWidth - marginRight) ||
                        (targetRow > 0 && currentX + imageWidth > pageWidth - marginRight)) {
                    log.warn("图片位置 {} 超出右边距，跳过此图片", position);
                    continue;
                }

                // 获取当前图片字节数组
                byte[] imageBytes = imageBytesList.get(i);

                // 在计算出的位置放置图片
                PdfCanvas pdfCanvas = new PdfCanvas(page);
                ImageData imageData = ImageDataFactory.create(imageBytes);
                Image image = new Image(imageData);

                // 设置图片位置和大小
                image.setFixedPosition(currentX, currentY);
                if (imageWidth > 0 && imageHeight > 0) {
                    image.scaleToFit(imageWidth, imageHeight);
                }

                // 添加图片到页面
                Canvas canvas = new Canvas(pdfCanvas, pdfDocument, page.getPageSize());
                canvas.add(image);
                canvas.close();

                log.debug("成功添加第 {} 个图片到位置 ({}, {})", position, currentX, currentY);
            }
        } catch (Exception e) {
            log.error("处理PDF添加多个图片时出错: {}", e.getMessage(), e);
            throw new IOException("处理PDF添加多个图片时出错: " + e.getMessage(), e);
        }

        log.debug("多个图片自动布局添加完成");
        return outputStream.toByteArray();
    }

    /**
     * 根据关键字定位，然后在其右侧添加多个自动布局的图片
     *
     * @param pdfBytes       PDF文件字节数组
     * @param searchText     要查找的关键字文本
     * @param keyWordIndex   第几个关键字
     * @param offsetX        图片起始位置相对于关键字的X轴偏移量
     * @param offsetY        图片起始位置相对于关键字的Y轴偏移量（在iText坐标系中，负值表示向下偏移，正值表示向上偏移）
     * @param imageBytesList 要添加的图片字节数组列表
     * @param imageWidth     每个图片的宽度
     * @param imageHeight    每个图片的高度
     * @param horizontalGap  图片之间的水平间距
     * @param verticalGap    行之间的垂直间距
     * @param marginLeft     左边距（除第一行外）
     * @param marginRight    右边距
     * @param marginBottom   底部边距
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addImagesAfterKeyword(byte[] pdfBytes, String searchText, int keyWordIndex, float offsetX, float offsetY,
                                               List<byte[]> imageBytesList, float imageWidth, float imageHeight,
                                               float horizontalGap, float verticalGap, float marginLeft,
                                               float marginRight, float marginBottom) throws IOException {
        log.debug("开始查找关键字'{}',并在其右侧添加多个图片", searchText);

        // 定位文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的关键字: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        // 获取指定索引位置的关键字（如果索引越界，则使用第一个匹配位置）
        TextLocation matchedLocation;
        try {
            matchedLocation = locations.get(keyWordIndex);
        } catch (IndexOutOfBoundsException e) {
            log.warn("关键字索引 {} 越界，使用第一个匹配位置", keyWordIndex);
            matchedLocation = locations.get(0);
        }

        int pageNum = matchedLocation.getPageNumber();
        Rectangle textRect = matchedLocation.getRectangle();

        // 计算图片起始位置（iText坐标系下）
        float startX = textRect.getX() + offsetX;
        float startY = textRect.getY() + offsetY;

        log.debug("找到关键字位置：页码={}, 位置=({}, {}), 将在偏移({}, {})后放置图片，起始坐标=({}, {})",
                pageNum, textRect.getX(), textRect.getY(), offsetX, offsetY, startX, startY);

        // 使用计算得到的起始位置调用自动布局方法
        return addMultipleImagesAutoLayout(pdfBytes, imageBytesList, pageNum, startX, startY,
                imageWidth, imageHeight, horizontalGap, verticalGap,
                marginLeft, marginRight, marginBottom);
    }

    /**
     * 根据关键字定位，然后在其右侧添加多个叠放的图片（所有图片放在同一位置）
     *
     * @param pdfBytes       PDF文件字节数组
     * @param searchText     要查找的关键字文本
     * @param keyWordIndex   第几个关键字
     * @param offsetX        图片起始位置相对于关键字的X轴偏移量
     * @param oX             循环偏移步长x
     * @param offsetY        图片起始位置相对于关键字的Y轴偏移量（在iText坐标系中，负值表示向下偏移，正值表示向上偏移）
     * @param oY             循环偏移步长y
     * @param imageBytesList 要添加的图片字节数组列表
     * @param imageWidth     每个图片的宽度
     * @param imageHeight    每个图片的高度
     * @return 添加图片后的PDF字节数组
     * @throws IOException IO异常
     */
    public static byte[] addImagesOverlayAfterKeyword(byte[] pdfBytes, String searchText, int keyWordIndex, float offsetX, float oX, float offsetY,
                                                      float oY, List<byte[]> imageBytesList, float imageWidth, float imageHeight) throws IOException {
        log.debug("开始查找关键字'{}',并在其右侧添加多个叠放的图片", searchText);

        // 定位文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的关键字: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        // 获取指定索引位置的关键字（如果索引越界，则使用第一个匹配位置）
        TextLocation matchedLocation;
        try {
            matchedLocation = locations.get(keyWordIndex);
        } catch (IndexOutOfBoundsException e) {
            log.warn("关键字索引 {} 越界，使用第一个匹配位置", keyWordIndex);
            matchedLocation = locations.get(0);
        }

        int pageNum = matchedLocation.getPageNumber();
        Rectangle textRect = matchedLocation.getRectangle();

        // 计算图片起始位置（iText坐标系下）
        float startX = textRect.getX() + offsetX;
        float startY = textRect.getY() + offsetY;

        log.debug("找到关键字位置：页码={}, 位置=({}, {}), 将在偏移({}, {})后放置叠放图片，起始坐标=({}, {})",
                pageNum, textRect.getX(), textRect.getY(), offsetX, offsetY, startX, startY);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }

            // 获取页面
            PdfPage page = pdfDocument.getPage(pageNum);

            // 为每个图片创建Canvas并添加到同一位置（叠放）
            for (int i = 0; i < imageBytesList.size(); i++) {
                // 获取当前图片字节数组
                byte[] imageBytes = imageBytesList.get(i);

                // 创建图片对象
                PdfCanvas pdfCanvas = new PdfCanvas(page);
                ImageData imageData = ImageDataFactory.create(imageBytes);
                Image image = new Image(imageData);

                // 设置图片位置和大小（所有图片放在同一位置）
                image.setFixedPosition(startX + i * oX, startY + i * oY);
                if (imageWidth > 0 && imageHeight > 0) {
                    image.scaleToFit(imageWidth, imageHeight);
                }

                // 添加图片到页面
                Canvas canvas = new Canvas(pdfCanvas, pdfDocument, page.getPageSize());
                canvas.add(image);
                canvas.close();

                log.debug("成功添加第 {} 个叠放图片到位置 ({}, {})", i + 1, startX, startY);
            }
        } catch (Exception e) {
            log.error("处理PDF添加叠放图片时出错: {}", e.getMessage(), e);
            throw new IOException("处理PDF添加叠放图片时出错: " + e.getMessage(), e);
        }

        log.debug("叠放图片添加完成");
        return outputStream.toByteArray();
    }

    public static byte[] addImagesOverlayAfterKeywordBatch(byte[] pdfBytes, String searchText, int keyWordIndex, float offsetX, float oX, float offsetY,
                                                           float oY, List<List<byte[]>> imageBytesList, float imageWidth, float imageHeight) throws IOException {
        log.debug("开始查找关键字'{}',并在其右侧添加多个叠放的图片", searchText);

        // 定位文本
        List<TextLocation> locations = locateTextInPdf(pdfBytes, searchText);

        if (locations.isEmpty()) {
            log.warn("未找到匹配的关键字: {}", searchText);
            return pdfBytes; // 如果没有找到文本，返回原始PDF
        }

        // 获取指定索引位置的关键字（如果索引越界，则使用第一个匹配位置）
        TextLocation matchedLocation;
        try {
            matchedLocation = locations.get(keyWordIndex);
        } catch (IndexOutOfBoundsException e) {
            log.warn("关键字索引 {} 越界，使用第一个匹配位置", keyWordIndex);
            matchedLocation = locations.get(0);
        }

        int pageNum = matchedLocation.getPageNumber();
        Rectangle textRect = matchedLocation.getRectangle();

        // 计算图片起始位置（iText坐标系下）
        float startX = textRect.getX() + offsetX;
        float startY = textRect.getY() + offsetY;

        log.debug("找到关键字位置：页码={}, 位置=({}, {}), 将在偏移({}, {})后放置叠放图片，起始坐标=({}, {})",
                pageNum, textRect.getX(), textRect.getY(), offsetX, offsetY, startX, startY);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            // 确保页码有效
            if (pageNum > pdfDocument.getNumberOfPages() || pageNum < 1) {
                throw new IllegalArgumentException("无效的页码: " + pageNum);
            }

            // 获取页面
            PdfPage page = pdfDocument.getPage(pageNum);

            // 为每个图片创建Canvas并添加到同一位置（叠放）
            for (int i = 0; i < imageBytesList.size(); i++) {
                // 获取当前图片字节数组
                for (int j = 0; j < imageBytesList.get(i).size(); j++) {
                    byte[] imageBytes = imageBytesList.get(i).get(j);

                    // 创建图片对象
                    PdfCanvas pdfCanvas = new PdfCanvas(page);
                    ImageData imageData = ImageDataFactory.create(imageBytes);
                    Image image = new Image(imageData);

                    // 设置图片位置和大小（所有图片放在同一位置）
                    image.setFixedPosition(startX + j * oX, startY - i * oY);
                    if (imageWidth > 0 && imageHeight > 0) {
                        image.scaleToFit(imageWidth, imageHeight);
                    }

                    // 添加图片到页面
                    Canvas canvas = new Canvas(pdfCanvas, pdfDocument, page.getPageSize());
                    canvas.add(image);
                    canvas.close();
                }
                log.debug("成功添加第 {} 组叠放图片", i + 1);
            }
        } catch (Exception e) {
            log.error("处理PDF添加叠放图片时出错: {}", e.getMessage(), e);
            throw new IOException("处理PDF添加叠放图片时出错: " + e.getMessage(), e);
        }

        log.debug("叠放图片添加完成");
        return outputStream.toByteArray();
    }

    /**
     * 在PDF文档的每一页（除了最后一页）的右下角添加图片
     *
     * @param pdfBytes   原PDF字节数组
     * @param imageBytes 要添加的图片字节数组
     * @param width      图片宽度
     * @param height     图片高度
     * @param margin     图片与页面边缘的距离
     * @return 处理后的PDF字节数组
     * @throws IOException 如果文件处理过程中发生错误
     */
    public static byte[] addImageToAllPagesExceptLast(byte[] pdfBytes, byte[] imageBytes,
                                                      float width, float height, float margin) throws IOException {
        log.debug("开始在所有页面（除最后一页）的右下角添加图片");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            int totalPages = pdfDocument.getNumberOfPages();

            if (totalPages <= 1) {
                log.debug("PDF只有一页或没有页面，不添加图片");
                return pdfBytes; // 如果只有一页，直接返回原始PDF
            }

            // 处理除了最后一页之外的所有页面
            for (int pageNum = 1; pageNum < totalPages; pageNum++) {
                PdfPage page = pdfDocument.getPage(pageNum);
                Rectangle pageSize = page.getPageSize();

                // 计算图片在右下角的位置
                float x = pageSize.getWidth() - width - margin;
                float y = margin; // 从底部算起的位置

                // 创建图片对象
                ImageData imageData = ImageDataFactory.create(imageBytes);
                Image image = new Image(imageData);

                // 设置图片位置和大小
                image.setFixedPosition(pageNum, x, y);
                image.setWidth(width);
                image.setHeight(height);

                // 添加图片到页面
                PdfCanvas pdfCanvas = new PdfCanvas(page);
                Canvas canvas = new Canvas(pdfCanvas, pageSize);
                canvas.add(image);
                canvas.close();

                log.debug("成功添加图片到第 {} 页右下角", pageNum);
            }
        } catch (Exception e) {
            log.error("处理PDF添加图片时出错: {}", e.getMessage(), e);
            throw new IOException("处理PDF添加图片时出错: " + e.getMessage(), e);
        }

        log.debug("所有页面（除最后一页）添加图片完成");
        return outputStream.toByteArray();
    }

    /**
     * 在PDF文档的每一页（除了最后一页）的右下角添加多张图片
     *
     * @param pdfBytes       原PDF字节数组
     * @param imageBytesList 要添加的多张图片字节数组列表
     * @param width          图片宽度
     * @param height         图片高度
     * @param margin         图片与页面边缘的距离
     * @param gap            图片之间的间隔
     * @return 处理后的PDF字节数组
     * @throws IOException 如果文件处理过程中发生错误
     */
    public static byte[] addMultipleImagesToAllPagesExceptLast(byte[] pdfBytes, List<byte[]> imageBytesList,
                                                               float width, float height, float margin, float gap) throws IOException {
        log.debug("开始在所有页面（除最后一页）的右下角添加多张图片");

        if (imageBytesList == null || imageBytesList.isEmpty()) {
            log.debug("图片列表为空，不添加图片");
            return pdfBytes;
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        try (PdfReader reader = new PdfReader(new ByteArrayInputStream(pdfBytes));
             PdfWriter writer = new PdfWriter(outputStream);
             PdfDocument pdfDocument = new PdfDocument(reader, writer)) {

            int totalPages = pdfDocument.getNumberOfPages();

            if (totalPages <= 1) {
                log.debug("PDF只有一页或没有页面，不添加图片");
                return pdfBytes; // 如果只有一页，直接返回原始PDF
            }

            // 处理除了最后一页之外的所有页面
            for (int pageNum = 1; pageNum < totalPages; pageNum++) {
                PdfPage page = pdfDocument.getPage(pageNum);
                Rectangle pageSize = page.getPageSize();

                // 为每个页面添加所有图片
                for (int i = 0; i < imageBytesList.size(); i++) {
                    // 计算图片在右下角的位置，水平排列
                    float x = pageSize.getWidth() - ((i + 1) * (width + gap)) + gap - 50;
                    float y = margin; // 从底部算起的位置

                    // 如果位置超出页面左边界，则不添加此图片
                    if (x < margin) {
                        log.debug("第{}张图片在第{}页超出左边界，跳过添加", i + 1, pageNum);
                        continue;
                    }

                    // 创建图片对象
                    ImageData imageData = ImageDataFactory.create(imageBytesList.get(i));
                    Image image = new Image(imageData);

                    // 设置图片位置和大小
                    image.setFixedPosition(pageNum, x, y);
                    image.setWidth(width);
                    image.setHeight(height);

                    // 添加图片到页面
                    PdfCanvas pdfCanvas = new PdfCanvas(page);
                    Canvas canvas = new Canvas(pdfCanvas, pageSize);
                    canvas.add(image);
                    canvas.close();

                    log.debug("成功添加第{}张图片到第{}页右下角", i + 1, pageNum);
                }
            }
        } catch (Exception e) {
            log.error("处理PDF添加多张图片时出错: {}", e.getMessage(), e);
            throw new IOException("处理PDF添加多张图片时出错: " + e.getMessage(), e);
        }

        log.debug("所有页面（除最后一页）添加多张图片完成");
        return outputStream.toByteArray();
    }

    public static void main(String[] args) {
        try {
            generateTextImage(200, 50, "测试", "/Users/<USER>/Downloads");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
