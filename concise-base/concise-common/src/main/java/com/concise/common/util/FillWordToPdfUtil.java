package com.concise.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfTextFormField;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.annot.PdfWidgetAnnotation;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import org.apache.poi.xwpf.usermodel.*;

import java.io.*;
import java.nio.file.Files;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/16
 * 填充word模版并转换为pdf工具类
 */
public class FillWordToPdfUtil {
    public static void templateMake() {
        try {
            // 读取模板
            FileInputStream templateInputStream = new FileInputStream("/Users/<USER>/Downloads/社区矫正人员重新违法犯罪通知书（回执社区矫正机构）模版.docx");

            // 创建填充数据
            Map<String, String> data = new HashMap<>();
            data.put("number", "社矫通回字[2021]");
            data.put("year", "3");
            data.put("month", "11");
            data.put("day", "8");
            data.put("userName", "张三4949849");
            data.put("orgName", "杭州市余杭区公安局五常派出所");

            // 填充模板
            XWPFDocument filledDoc = fillTemplate(templateInputStream, data);

            // 导出为 Word 文件
            String outputPath = "/Users/<USER>/Downloads/222.docx";
            exportToWord(filledDoc, outputPath);

            System.out.println("Word 文档已生成：" + outputPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 填充word模版
     *
     * @param templateInputStream
     * @param data
     * @return
     * @throws Exception
     */
    private static XWPFDocument fillTemplate(FileInputStream templateInputStream, Map<String, String> data) throws Exception {
        XWPFDocument templateDoc = new XWPFDocument(templateInputStream);

        // 处理段落中的占位符
        for (XWPFParagraph paragraph : templateDoc.getParagraphs()) {
            processParagraph(paragraph, data);
        }

        // 处理表格中的占位符
        for (XWPFTable table : templateDoc.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        processParagraphTable(paragraph, data);
                    }
                }
            }
        }

        return templateDoc;
    }

    /**
     * 替换word中的普通占位符，格式{{params}}
     *
     * @param paragraph
     * @param data
     */
    private static void processParagraph(XWPFParagraph paragraph, Map<String, String> data) {
        for (XWPFRun run : paragraph.getRuns()) {
            String text = run.getText(0);
            if (text != null && text.contains("{{") && text.contains("}}")) {
                // 替换占位符
                for (Map.Entry<String, String> entry : data.entrySet()) {
                    String placeholder = "{{" + entry.getKey() + "}}";
                    if (text.contains(placeholder)) {
                        text = text.replace(placeholder, entry.getValue());
                    }
                }
                run.setText(text, 0);
            }
        }
    }

    /**
     * 替换word中的表格占位符，此处的占位符不能使用#或者{}之类的符号，否则会自动换行，导致无法替换
     *
     * @param paragraph
     * @param data
     */
    private static void processParagraphTable(XWPFParagraph paragraph, Map<String, String> data) {
        for (XWPFRun run : paragraph.getRuns()) {
            String text = run.getText(0);
            if (text != null) {
                // 替换占位符
                for (Map.Entry<String, String> entry : data.entrySet()) {
                    if (text.contains(entry.getKey())) {
                        text = text.replace(entry.getKey(), entry.getValue());
                    }
                }
                run.setText(text, 0);
            }
        }
    }

    private static void exportToWord(XWPFDocument doc, String outputPath) throws Exception {
        FileOutputStream fos = new FileOutputStream(outputPath);
        doc.write(fos);
        fos.close();
    }

    public static String convertToBase64(String filePath) {
        try {
            // 读取本地 PDF 文件
            File pdfFile = new File(filePath);
            byte[] pdfBytes = Files.readAllBytes(pdfFile.toPath());

            // 将 PDF 数据转换为 Base64 字符串
            String base64String = Base64.getEncoder().encodeToString(pdfBytes);

            // 输出 Base64 字符串
            System.out.println("Base64 PDF 数据：" + base64String);

            return base64String;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void pdfModalFillDownload(String inputUrl, String outputUrl, Map<String, String> data) {


        try (PdfReader pdfReader = new PdfReader(inputUrl);
             PdfWriter pdfWriter = new PdfWriter(outputUrl);
             PdfDocument pdfDocument = new PdfDocument(pdfReader, pdfWriter)) {

            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, true);
            data.forEach((key, value) -> {
                PdfTextFormField nameField = (PdfTextFormField) form.getField(key);
                if (ObjectUtil.isNotNull(nameField)) {
                    PdfFont font = null;
                    try {
                        font = PdfFontFactory.createFont("/Users/<USER>/Downloads/仿宋_GB2312.ttf", "Identity-H", true);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    nameField.setFont(font).setFontSize(16);
                    nameField.setValue(value);

                }
            });
            // 更新表单
            form.flattenFields();

            System.out.println("PDF 表单填充成功：" + outputUrl);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static String file(String templateUrl, Map<String, String> data) {
        try {
            // 读取本地 PDF 模板文件
            PdfReader reader = new PdfReader(new FileInputStream(templateUrl));

            // 创建一个输出流来保存填充后的 PDF 数据
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 创建一个 PDF 文档，并将输出流与其关联
            PdfDocument pdfDocument = new PdfDocument(reader, new PdfWriter(outputStream));
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, true);
            data.forEach((key, value) -> {
                PdfTextFormField nameField = (PdfTextFormField) form.getField(key);
                if (ObjectUtil.isNotNull(nameField)) {
                    PdfFont font = null;
                    try {
                        font = PdfFontFactory.createFont("/Users/<USER>/Downloads/仿宋_GB2312.ttf", "Identity-H", true);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    nameField.setFont(font).setFontSize(16);
                    nameField.setValue(value);
                }
            });

            // 更新表单
            form.flattenFields();

            // 将填充后的 PDF 数据转换为字节数组
            byte[] pdfData = outputStream.toByteArray();

            // 将填充后的 PDF 数据进行 Base64 编码
            String base64Data = Base64.getEncoder().encodeToString(pdfData);

            // 打印 Base64 字符串
            System.out.println(base64Data);
            return base64Data;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String test(String path, Map<String, String> data) {
        try {
            // 读取本地 PDF 模板文件
            PdfReader reader = new PdfReader(new FileInputStream(path));

            // 创建一个输出流来保存填充后的 PDF 数据
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            // 创建一个 PDF 文档，并将输出流与其关联
            PdfDocument pdfDocument = new PdfDocument(reader, new PdfWriter(outputStream));

            // 获取 PDF 表单
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, true);
            data.forEach((key, value) -> {
                PdfTextFormField nameField = (PdfTextFormField) form.getField(key);
                if (ObjectUtil.isNotNull(nameField)) {
                    PdfFont font = null;
                    try {
                        font = PdfFontFactory.createFont("/Users/<USER>/Downloads/仿宋_GB2312.ttf", "Identity-H", true);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    nameField.setFont(font).setFontSize(16);
                    nameField.setValue(value);

                    // 获取字段的坐标和大小
                    for (PdfWidgetAnnotation widgetAnnotation : nameField.getWidgets()) {
                        float x = widgetAnnotation.getRectangle().toRectangle().getX();
                        float y = widgetAnnotation.getRectangle().toRectangle().getBottom();
                        float width = widgetAnnotation.getRectangle().toRectangle().getWidth();
                        float height = widgetAnnotation.getRectangle().toRectangle().getHeight();

                        // 在字段下面绘制下划线
                        PdfPage page = widgetAnnotation.getPage();

                        PdfCanvas canvas = new PdfCanvas(page);
                        // 获取文本宽度
                        float textWidth = (float) (font.getWidth(value) * 16) / 1000; // 16 是字体大小

                        // 计算下划线的长度
                        float underlineLength = Math.min(width, textWidth);

                        // 在字段下面绘制下划线
                        float lineWidth = 1f;
                        canvas.setLineWidth(lineWidth);
                        canvas.moveTo(x, y - lineWidth / 2);
                        canvas.lineTo(x + underlineLength, y - lineWidth / 2);
                        canvas.stroke();
                    }
                }
            });

            // 关闭 PDF 文档
            pdfDocument.close();

            // 将填充后的 PDF 数据写入字节数组
            byte[] pdfData = outputStream.toByteArray();

            // 将填充后的 PDF 数据进行 Base64 编码
            String base64Data = Base64.getEncoder().encodeToString(pdfData);
            return base64Data;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void saveBase64ToPdf(String base64, String savePath) {
        try {
            // 解码 Base64 字符串为字节数组
            byte[] pdfData = com.itextpdf.io.codec.Base64.decode(base64);

            // 创建一个 PDF 文档
            PdfDocument pdfDocument = new PdfDocument(new PdfWriter(new FileOutputStream(savePath)));

            // 将字节数组写入 PDF 文档
            pdfDocument.getWriter().getOutputStream().write(pdfData);

            // 关闭 PDF 文档
            pdfDocument.close();

            System.out.println("PDF 文件已保存到本地");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {

        Map<String, String> data = new HashMap<>();
        data.put("num", "社矫通回字[2021]");
        data.put("receiveDate", "2024年1月17日");
        data.put("xm", "张三");
        data.put("receiveOrg", "五常派出所：");
        data.put("jzjgName", "杭州市余杭区司法局");
        String inputFile = "/Users/<USER>/Downloads/新模版fill.pdf";
        String outputFile = "/Users/<USER>/Downloads/test89.pdf";
//        pdfModalFillDownload(inputFile,outputFile,data);
//        String test = test(inputFile, data);
//        JSONObject signPdf = ElectronicSignatureUtil.createSignPdf(test);
//        JSONObject jsonObject = signPdf.getJSONObject("data");
//        String base64signed = jsonObject.getString("signFileB64").replaceAll("\\n","");
//        TestUtil.base64ToPdf(base64signed,outputFile);

    }

}
