package com.concise.common.sql;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;

import java.util.*;

/**
 * Markdown to SQL
 * <p>
 *     主键默认为 id varchar(32)
 * 若解析失败请检查<br>
 * 1. md文件是否存在<br>
 * 2. md文件内容是否符合格式要求<br>
 * &nbsp 1) 表名使用`## `<br>
 * &nbsp 2) 字段名使用`- `<br>
 * &nbsp 3) 类型紧跟在字段后面,并且使用`  @`间隔开<br>
 * &nbsp 4) 注释紧跟在字段或者类型后面,并且使用`  #`间隔开<br>
 * 若表为二级标题更改 `level` 为 `##`,同理三级标题改为 `###`
 * 推荐搭配脑图使用, 脑图->导出为Markdown(也有一些脑图是导出标记)->将文件内容格式检查(去除大标题等等等..)->copy到BrainMap.md文件<br>
 * <p>
 * 之前画完脑图之后在建立数据库的过程中还需要在写一遍字段名和注释,索性写了这个工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class BrainMapToSql {

    static String tableName = "";
    static String tableNote = "";

    /**
     * 解析等级
     * 观察导出的Markdown文件中的表前缀
     * 若表为二级标题更改 `level` 为 `##`,同理三级标题改为 `###`
     */
    static String level = "##";


    public static void main(String[] args) {
        String content = null;
        String path = null;
        try {
            path = System.getProperty("user.dir") + "/concise-base/concise-common/src/main/java/com/concise/common/sql";
            content = FileUtil.readString(path + "/BrainMap.md", "utf-8");
        } catch (IORuntimeException e) {
            e.printStackTrace();
        }

        if (StrUtil.isNotBlank(content)) {
            String[] modelList = Objects.requireNonNull(content).split(level);
            StringBuilder resultBuffer = new StringBuilder();
            resultBuffer.append("/**\n" + " * <AUTHOR> + " * @version 1.0.0\n" + " * @createTime ")
                    .append(DateUtil.now()).append("\n")
                    .append(" * @Description 脑图转SQL工具类\n").append(" */\n");
            List<StringBuffer> stringBufferList = parsingMd(modelList);

            stringBufferList.forEach(resultBuffer::append);
            FileUtil.writeUtf8String(resultBuffer.toString(), path + "/generateSql.sql");
            System.out.println("SQLFile generated successfully! path: " + path + "/generateSql.sql");
        } else {
            System.out.println("no content read");
        }

    }

    private static List<StringBuffer> parsingMd(String[] modelList) {
        List<StringBuffer> stringBufferList = new ArrayList<>();
        for (int i1 = 1; i1 < modelList.length; i1++) {
            String testOne = modelList[i1];
            String[] modelOne = testOne.split("\n" +
                    "\n" +
                    "-");
            List<Map<String, String>> mapList = getSqlModelList(modelOne);
            tableName = String.valueOf(mapList.get(0).keySet().toArray()[0]);
            tableNote = String.valueOf(mapList.get(0).values().toArray()[0]);
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append("DROP TABLE IF EXISTS `").append(tableName).append("`;");
            sqlBuffer.append("\n CREATE TABLE `").append(tableName).append("` (");
            sqlBuffer.append("\n `id` varchar(32) not null primary key,");
            mapList.remove(0);
            mapList.forEach(o -> o.forEach((key, value) -> {
                if (value.contains("\n")) {
                    value = StrUtil.removeAllLineBreaks(value);
                }
                sqlSplice(sqlBuffer, key, value);
            }));
            sqlBuffer.deleteCharAt(sqlBuffer.length() -1);
            sqlBuffer.append("\n) COMMENT='")
                    .append(tableNote)
                    .append("';");
            stringBufferList.add(sqlBuffer);
        }
        return stringBufferList;
    }

    private static void sqlSplice(StringBuffer sqlBuffer, String key, String value) {

        System.out.println(key);
        System.out.println(value);

        String[] split = key.split("@");
        key = split[0].trim();
        if (split.length > 1) {
            String type = split[1].trim();
            switch (type) {
                case "文本":sqlBuffer.append("\n" + "  `").append(key).append("` varchar(100) NULL COMMENT '").append(value).append("',");break;
                case "长文本":sqlBuffer.append("\n" + "  `").append(key).append("` text NULL COMMENT '").append(value).append("',");break;
                case "单选值":sqlBuffer.append("\n" + "  `").append(key).append("` varchar(20) NULL COMMENT '").append(value).append("',");break;
                case "多选值":sqlBuffer.append("\n" + "  `").append(key).append("` varchar(100) NULL COMMENT '").append(value).append("',");break;
                case "日期":sqlBuffer.append("\n" + "  `").append(key).append("` datetime NULL COMMENT '").append(value).append("',");break;
                default:
                    System.out.println("error: unknown type " +type);
            }
        }

    }

    /**
     * 解析单个表
     * @param modelOne modelOne
     * @return List
     */
    private static List<Map<String, String>> getSqlModelList(String[] modelOne) {
        List<Map<String, String>> mapList = new ArrayList<>();
        for (String s : modelOne) {
            String[] split = s.split(" #");
            Map<String, String> stringStringMap = new HashMap<>(mapList.size());
            stringStringMap.put(split[0].trim(), split[1].trim());
            mapList.add(stringStringMap);
        }
        return mapList;
    }
}
