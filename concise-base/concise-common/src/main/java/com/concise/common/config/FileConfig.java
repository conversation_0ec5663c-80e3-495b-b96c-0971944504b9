package com.concise.common.config;

import com.concise.common.file.FileOperator;
import com.concise.common.file.modular.aliyun.AliyunFileOperator;
import com.concise.common.file.modular.aliyun.prop.AliyunOssProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * 文件存储的配置
 * <p>
 * 默认激活本地文件存储
 *
 * <AUTHOR>
 * @date 2020/6/6 22:27
 */
@Configuration
public class FileConfig {

    @Value("${aliyun.oss.endpoint}")
    private String endPoint;
    @Value("${aliyun.oss.accessKey}")
    private String accessKeyId;
    @Value("${aliyun.oss.secretKey}")
    private String accessKeySecret;
    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    /**
     * 默认文件存储的位置
     */
    public static final String DEFAULT_BUCKET = "defaultBucket";
    public static final String DEFAULT_TEMP = System.getProperty("user.dir") + File.separator + "upload" + File.separator + "temp";

    /**
     * 本地文件操作客户端
     *
     * <AUTHOR>
     * @date 2020/6/9 21:39
     */
    @Bean
    public FileOperator fileOperator() {
        AliyunOssProperties aliyunOssProperties = new AliyunOssProperties();
        aliyunOssProperties.setEndPoint(endPoint);
        aliyunOssProperties.setAccessKeyId(accessKeyId);
        aliyunOssProperties.setAccessKeySecret(accessKeySecret);
        return new AliyunFileOperator(aliyunOssProperties);

        //LocalFileProperties localFileProperties = new LocalFileProperties();
        //String fileUploadPathForWindows = ConstantContextHolder.getDefaultFileUploadPathForWindows();
        //if (ObjectUtil.isNotEmpty(fileUploadPathForWindows)) {
        //    localFileProperties.setLocalFileSavePathWin(fileUploadPathForWindows);
        //}
        //
        //String fileUploadPathForLinux = ConstantContextHolder.getDefaultFileUploadPathForLinux();
        //if (ObjectUtil.isNotEmpty(fileUploadPathForLinux)) {
        //    localFileProperties.setLocalFileSavePathLinux(fileUploadPathForLinux);
        //}
        //return new LocalFileOperator(localFileProperties);
    }

}
