package com.concise.gen.coordinatesupervisesubmit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinatesupervisesubmit.entity.CoordinateSuperviseSubmit;
import com.concise.gen.coordinatesupervisesubmit.enums.CoordinateSuperviseSubmitExceptionEnum;
import com.concise.gen.coordinatesupervisesubmit.mapper.CoordinateSuperviseSubmitMapper;
import com.concise.gen.coordinatesupervisesubmit.param.CoordinateSuperviseSubmitParam;
import com.concise.gen.coordinatesupervisesubmit.service.CoordinateSuperviseSubmitService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 提请外出监管协同service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
@Service
public class CoordinateSuperviseSubmitServiceImpl extends ServiceImpl<CoordinateSuperviseSubmitMapper, CoordinateSuperviseSubmit> implements CoordinateSuperviseSubmitService {

    @Override
    public PageResult<CoordinateSuperviseSubmit> page(CoordinateSuperviseSubmitParam param) {
        QueryWrapper<CoordinateSuperviseSubmit> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(CoordinateSuperviseSubmit::getJzdwId, param.getOrgs());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CoordinateSuperviseSubmit::getXm, param.getXm());
            }
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(CoordinateSuperviseSubmit::getZt, param.getZt());
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(CoordinateSuperviseSubmit::getSdwtsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(CoordinateSuperviseSubmit::getSdwtsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CoordinateSuperviseSubmit::getSdwtsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateSuperviseSubmit> list(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam) {
        return this.list();
    }

    @Override
    public void add(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam) {
        CoordinateSuperviseSubmit coordinateSuperviseSubmit = new CoordinateSuperviseSubmit();
        BeanUtil.copyProperties(coordinateSuperviseSubmitParam, coordinateSuperviseSubmit);
        this.save(coordinateSuperviseSubmit);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam) {
        this.removeById(coordinateSuperviseSubmitParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam) {
        CoordinateSuperviseSubmit coordinateSuperviseSubmit = this.queryCoordinateSuperviseSubmit(coordinateSuperviseSubmitParam);
        BeanUtil.copyProperties(coordinateSuperviseSubmitParam, coordinateSuperviseSubmit);
        this.updateById(coordinateSuperviseSubmit);
    }

    @Override
    public CoordinateSuperviseSubmit detail(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam) {
        return this.queryCoordinateSuperviseSubmit(coordinateSuperviseSubmitParam);
    }

    /**
     * 获取提请外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
    private CoordinateSuperviseSubmit queryCoordinateSuperviseSubmit(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam) {
        CoordinateSuperviseSubmit coordinateSuperviseSubmit = this.getById(coordinateSuperviseSubmitParam.getId());
        if (ObjectUtil.isNull(coordinateSuperviseSubmit)) {
            throw new ServiceException(CoordinateSuperviseSubmitExceptionEnum.NOT_EXIST);
        }
        return coordinateSuperviseSubmit;
    }
}
