package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP2Param;
import com.concise.gen.ciInvestigate. service.InvestigateP2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估_接收控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:15
 */
@Api(tags = "迁入调查评估_接收")
@RestController
public class InvestigateP2Controller {

    @Resource
    private InvestigateP2Service investigateP2Service;

    /**
     * 查询迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    @Permission
    @GetMapping("/investigateP2/page")
    @ApiOperation("迁入调查评估_接收_分页查询")
    @BusinessLog(title = "迁入调查评估_接收_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP2Param investigateP2Param) {
        return new SuccessResponseData(investigateP2Service.page(investigateP2Param));
    }

    /**
     * 添加迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    @Permission
    @PostMapping("/investigateP2/add")
    @ApiOperation("迁入调查评估_接收_增加")
    @BusinessLog(title = "迁入调查评估_接收_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP2Param.add.class) InvestigateP2Param investigateP2Param) {
        investigateP2Service.add(investigateP2Param);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    @Permission
    @PostMapping("/investigateP2/delete")
    @ApiOperation("迁入调查评估_接收_删除")
    @BusinessLog(title = "迁入调查评估_接收_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP2Param.delete.class) InvestigateP2Param investigateP2Param) {
        investigateP2Service.delete(investigateP2Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    @Permission
    @PostMapping("/investigateP2/edit")
    @ApiOperation("迁入调查评估_接收_编辑")
    @BusinessLog(title = "迁入调查评估_接收_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP2Param.edit.class) InvestigateP2Param investigateP2Param) {
        investigateP2Service.edit(investigateP2Param);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    @Permission
    @GetMapping("/investigateP2/detail")
    @ApiOperation("迁入调查评估_接收_查看")
    @BusinessLog(title = "迁入调查评估_接收_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP2Param.detail.class) InvestigateP2Param investigateP2Param) {
        return new SuccessResponseData(investigateP2Service.detail(investigateP2Param));
    }

    /**
     * 迁入调查评估_接收列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    @Permission
    @GetMapping("/investigateP2/list")
    @ApiOperation("迁入调查评估_接收_列表")
    @BusinessLog(title = "迁入调查评估_接收_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP2Param investigateP2Param) {
        return new SuccessResponseData(investigateP2Service.list(investigateP2Param));
    }

}
