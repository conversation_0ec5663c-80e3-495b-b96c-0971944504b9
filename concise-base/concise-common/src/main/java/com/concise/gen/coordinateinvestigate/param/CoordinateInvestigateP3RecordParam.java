package com.concise.gen.coordinateinvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 调查评估协查_调查_记录参数类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:55
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateInvestigateP3RecordParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 调查步骤id
     */
    @NotBlank(message = "调查步骤id不能为空，请检查stepId参数", groups = {add.class, edit.class})
    private String stepId;

    /**
     * 被调查人姓名
     */
    @NotBlank(message = "被调查人姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 与被告人（罪犯）关系
     */
    @NotBlank(message = "与被告人（罪犯）关系不能为空，请检查gx参数", groups = {add.class, edit.class})
    private String gx;

    /**
     * 调查时间
     */
    @NotNull(message = "调查时间不能为空，请检查dcsj参数", groups = {add.class, edit.class})
    private String dcsj;

    /**
     * 调查地点
     */
    @NotBlank(message = "调查地点不能为空，请检查dcdd参数", groups = {add.class, edit.class})
    private String dcdd;

    /**
     * 调查事项
     */
    @NotBlank(message = "调查事项不能为空，请检查dcsx参数", groups = {add.class, edit.class})
    private String dcsx;

    /**
     * 调查笔录
     */
    @NotBlank(message = "调查笔录不能为空，请检查dcbl参数", groups = {add.class, edit.class})
    private String dcbl;

    /**
     * 调查人(jsonarray)
     */
    @NotBlank(message = "调查人(jsonarray)不能为空，请检查dcr参数", groups = {add.class, edit.class})
    private String dcr;

}
