package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP3Param;
import com.concise.gen.ciInvestigate. service.InvestigateP3Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估_调查控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:16
 */
@Api(tags = "迁入调查评估_调查")
@RestController
public class InvestigateP3Controller {

    @Resource
    private InvestigateP3Service investigateP3Service;

    /**
     * 查询迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    @Permission
    @GetMapping("/investigateP3/page")
    @ApiOperation("迁入调查评估_调查_分页查询")
    @BusinessLog(title = "迁入调查评估_调查_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP3Param investigateP3Param) {
        return new SuccessResponseData(investigateP3Service.page(investigateP3Param));
    }

    /**
     * 添加迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    @Permission
    @PostMapping("/investigateP3/add")
    @ApiOperation("迁入调查评估_调查_增加")
    @BusinessLog(title = "迁入调查评估_调查_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP3Param.add.class) InvestigateP3Param investigateP3Param) {
        investigateP3Service.add(investigateP3Param);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    @Permission
    @PostMapping("/investigateP3/delete")
    @ApiOperation("迁入调查评估_调查_删除")
    @BusinessLog(title = "迁入调查评估_调查_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP3Param.delete.class) InvestigateP3Param investigateP3Param) {
        investigateP3Service.delete(investigateP3Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    @Permission
    @PostMapping("/investigateP3/edit")
    @ApiOperation("迁入调查评估_调查_编辑")
    @BusinessLog(title = "迁入调查评估_调查_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP3Param.edit.class) InvestigateP3Param investigateP3Param) {
        investigateP3Service.edit(investigateP3Param);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    @Permission
    @GetMapping("/investigateP3/detail")
    @ApiOperation("迁入调查评估_调查_查看")
    @BusinessLog(title = "迁入调查评估_调查_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP3Param.detail.class) InvestigateP3Param investigateP3Param) {
        return new SuccessResponseData(investigateP3Service.detail(investigateP3Param));
    }

    /**
     * 迁入调查评估_调查列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    @Permission
    @GetMapping("/investigateP3/list")
    @ApiOperation("迁入调查评估_调查_列表")
    @BusinessLog(title = "迁入调查评估_调查_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP3Param investigateP3Param) {
        return new SuccessResponseData(investigateP3Service.list(investigateP3Param));
    }

}
