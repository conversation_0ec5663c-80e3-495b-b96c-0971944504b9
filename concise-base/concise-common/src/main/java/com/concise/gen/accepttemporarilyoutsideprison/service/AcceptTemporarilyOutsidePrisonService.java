package com.concise.gen.accepttemporarilyoutsideprison.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptbaseinfo.param.AcceptBaseInfoParam;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.accepttemporarilyoutsideprison.entity.AcceptTemporarilyOutsidePrison;
import com.concise.gen.accepttemporarilyoutsideprison.param.AcceptTemporarilyOutsidePrisonParam;

import java.util.List;

/**
 * 暂予监外接收表service接口
 *
 * <AUTHOR>
 * @date 2022-12-01 10:33:30
 */
public interface AcceptTemporarilyOutsidePrisonService extends IService<AcceptTemporarilyOutsidePrison> {

    /**
     * 查询暂予监外接收表
     *
     * <AUTHOR>
     * @date 2022-12-01 10:33:30
     */
    PageResult<AcceptTemporarilyOutsidePrison> page(AcceptTemporarilyOutsidePrisonParam acceptTemporarilyOutsidePrisonParam);

    /**
     * 添加暂予监外接收表
     *
     * <AUTHOR>
     * @date 2022-12-01 10:33:30
     */
    List<AcceptCorrectionDocParam> add(AcceptTemporarilyOutsidePrisonParam acceptTemporarilyOutsidePrisonParam);
    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);
    /**
     * 编辑暂予监外接收表
     *
     * <AUTHOR>
     * @date 2022-12-01 10:33:30
     */
    void edit(AcceptBaseInfoParam param);

    /**
     * 查看暂予监外接收表
     *
     * <AUTHOR>
     * @date 2022-12-01 10:33:30
     */
     AcceptTemporarilyOutsidePrison detail(String id);

    /**
     *
     */
     void feedBack(AcceptTemporarilyOutsidePrisonParam data);
}
