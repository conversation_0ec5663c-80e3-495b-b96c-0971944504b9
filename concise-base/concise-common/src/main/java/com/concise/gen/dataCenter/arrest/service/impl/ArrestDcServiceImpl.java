package com.concise.gen.dataCenter.arrest.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.arrest.entity.ArrestDc;
import com.concise.gen.dataCenter.arrest.enums.ArrestDcExceptionEnum;
import com.concise.gen.dataCenter.arrest.mapper.ArrestDcMapper;
import com.concise.gen.dataCenter.arrest.param.ArrestDcParam;
import com.concise.gen.dataCenter.arrest.service.ArrestDcService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 提请逮捕service接口实现类
 *
 * <AUTHOR>
 * @date 2023-09-19 17:30:07
 */
@Service
@DS("dataCenter")
public class ArrestDcServiceImpl extends ServiceImpl<ArrestDcMapper, ArrestDc> implements ArrestDcService {

    @Override
    public PageResult<ArrestDc> page(ArrestDcParam arrestDcParam) {
        QueryWrapper<ArrestDc> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(arrestDcParam)) {

            // 根据矫正对象id 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getPid())) {
                queryWrapper.lambda().eq(ArrestDc::getPid, arrestDcParam.getPid());
            }
            // 根据矫正对象姓名 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getPname())) {
                queryWrapper.lambda().eq(ArrestDc::getPname, arrestDcParam.getPname());
            }
            // 根据提请理由 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getTqly())) {
                queryWrapper.lambda().eq(ArrestDc::getTqly, arrestDcParam.getTqly());
            }
            // 根据提请意见 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getTqyj())) {
                queryWrapper.lambda().eq(ArrestDc::getTqyj, arrestDcParam.getTqyj());
            }
            // 根据提请意见中文值 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getTqyjName())) {
                queryWrapper.lambda().eq(ArrestDc::getTqyjName, arrestDcParam.getTqyjName());
            }
            // 根据机构意见 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getJgyj())) {
                queryWrapper.lambda().eq(ArrestDc::getJgyj, arrestDcParam.getJgyj());
            }
            // 根据日期 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getSfssqsj())) {
                queryWrapper.lambda().eq(ArrestDc::getSfssqsj, arrestDcParam.getSfssqsj());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getNcsgajg())) {
                queryWrapper.lambda().eq(ArrestDc::getNcsgajg, arrestDcParam.getNcsgajg());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getNcsgajglx())) {
                queryWrapper.lambda().eq(ArrestDc::getNcsgajglx, arrestDcParam.getNcsgajglx());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getNcsgajglxName())) {
                queryWrapper.lambda().eq(ArrestDc::getNcsgajglxName, arrestDcParam.getNcsgajglxName());
            }
            // 根据地市id 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getDishiId())) {
                queryWrapper.lambda().eq(ArrestDc::getDishiId, arrestDcParam.getDishiId());
            }
            // 根据地市名称 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getDishiName())) {
                queryWrapper.lambda().eq(ArrestDc::getDishiName, arrestDcParam.getDishiName());
            }
            // 根据区县id 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getQuxianId())) {
                queryWrapper.lambda().eq(ArrestDc::getQuxianId, arrestDcParam.getQuxianId());
            }
            // 根据区县名称 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getQuxianName())) {
                queryWrapper.lambda().eq(ArrestDc::getQuxianName, arrestDcParam.getQuxianName());
            }
            // 根据街道id 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getJiedaoId())) {
                queryWrapper.lambda().eq(ArrestDc::getJiedaoId, arrestDcParam.getJiedaoId());
            }
            // 根据街道名称 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getJiedaoName())) {
                queryWrapper.lambda().eq(ArrestDc::getJiedaoName, arrestDcParam.getJiedaoName());
            }
            // 根据最后更新时间 查询
            if (ObjectUtil.isNotEmpty(arrestDcParam.getLastModifiedTime())) {
                queryWrapper.lambda().eq(ArrestDc::getLastModifiedTime, arrestDcParam.getLastModifiedTime());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ArrestDc> list(ArrestDcParam arrestDcParam) {
        return this.list();
    }

    @Override
    public void add(ArrestDcParam arrestDcParam) {
        ArrestDc arrestDc = new ArrestDc();
        BeanUtil.copyProperties(arrestDcParam, arrestDc);
        this.save(arrestDc);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ArrestDcParam arrestDcParam) {
        this.removeById(arrestDcParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ArrestDcParam arrestDcParam) {
        ArrestDc arrestDc = this.queryArrestDc(arrestDcParam);
        BeanUtil.copyProperties(arrestDcParam, arrestDc);
        this.updateById(arrestDc);
    }

    @Override
    public ArrestDc detail(ArrestDcParam arrestDcParam) {
        return this.queryArrestDc(arrestDcParam);
    }

    /**
     * 获取提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    private ArrestDc queryArrestDc(ArrestDcParam arrestDcParam) {
        ArrestDc arrestDc = this.getById(arrestDcParam.getId());
        if (ObjectUtil.isNull(arrestDc)) {
            throw new ServiceException(ArrestDcExceptionEnum.NOT_EXIST);
        }
        return arrestDc;
    }
}
