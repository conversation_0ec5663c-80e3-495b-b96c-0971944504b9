package com.concise.gen.acceptinvestinfo.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptbaseinfo.vo.StatVo;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptinvestinfo.entity.AcceptInvestInfo;
import com.concise.gen.acceptinvestinfo.param.AcceptInvestInfoParam;

import java.util.List;

/**
 * 调查评估信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
 */
public interface AcceptInvestInfoService extends IService<AcceptInvestInfo> {

    /**
     * 查询调查评估信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:07
     */
    PageResult<AcceptInvestInfo> page(AcceptInvestInfoParam acceptInvestInfoParam);

    /**
     * 添加调查评估信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:07
     */
    List<AcceptCorrectionDocParam> add(AcceptInvestInfoParam acceptInvestInfoParam);

    /**
     * 删除调查评估信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:07
     */
    void delete(AcceptInvestInfoParam acceptInvestInfoParam);
    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);
    /**
     * 编辑调查评估信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:07
     */
    void edit(AcceptInvestInfoParam acceptInvestInfoParam);
    boolean feedback(String id);

    /**
     * 查看调查评估信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:07
     */
     AcceptInvestInfo detail(String id);
    AcceptInvestInfo getWdResult(String id);

    /**
     * 流程记录
     * @param param AcceptInvestInfoParam
     * @return JSONArray
     */
     JSONArray step(AcceptInvestInfoParam param);


    void getNoSignPdf(AcceptInvestInfoParam param);
    void getSignedPdf(AcceptInvestInfoParam param);
    List<StatVo> statistics(String orgId,int level,String start,String end);
    void statisticsExport(String orgId,int level,String start,String end);
}
