package com.concise.gen.signatureauthorization.param;

import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 签章授权表参数类
 *
 * <AUTHOR>
 * @date 2025-07-31 11:04:05
*/
@Data
public class SignatureAuthorizationParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 印章id
     */
    @NotBlank(message = "印章id不能为空，请检查signatureId参数", groups = {add.class, edit.class})
    private String signatureId;

    /**
     * 授权使用人员id
     */
    @NotBlank(message = "授权使用人员id不能为空，请检查userIds参数", groups = {add.class, edit.class})
    private String userIds;

    /**
     * 授权使用人员姓名
     */
    @NotBlank(message = "授权使用人员姓名不能为空，请检查userNames参数", groups = {add.class, edit.class})
    private String userNames;

    /**
     * 授权开始时间
     */
    @NotNull(message = "授权开始时间不能为空，请检查beginTime参数", groups = {add.class, edit.class})
    private String beginTime;

    /**
     * 授权结束时间
     */
    @NotNull(message = "授权结束时间不能为空，请检查endTime参数", groups = {add.class, edit.class})
    private String endTime;

    /**
     * 授权证明方式（1-在线授权，2-线下授权）
     */
    @NotBlank(message = "授权证明方式（1-在线授权，2-线下授权）不能为空，请检查authorizationType参数", groups = {add.class, edit.class})
    private String authorizationType;

    /**
     * 授权状态（1-使用中；2-已过期）
     */
//    @NotBlank(message = "授权状态（1-使用中；2-已过期）不能为空，请检查authorizationStatus参数", groups = {add.class, edit.class})
    private String authorizationStatus;

    /**
     * 授权证明
     */

    private SysFileInfo sysFileInfo;

    /**
     * 当前日期
     */
    private String nowDate;

    /**
     * 签章编码
     */
    private String sealNo;

    /**
     * 当前用户姓名
     */
    private String userName;

}
