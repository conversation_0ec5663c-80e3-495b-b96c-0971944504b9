package com.concise.gen.areainfo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import com.concise.common.pojo.base.entity.BaseEntity;

/**
 * 省市县乡镇
 *
 * <AUTHOR>
 * @date 2024-01-03 17:33:22
 */
@Data
@TableName("area_info")
public class AreaInfo {

    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String areaid;

    /**
     *
     */
    private String areacode;

    /**
     *
     */
    private String areaname;

    /**
     *
     */
    private Integer arealevel;

    /**
     *
     */
    private Integer zxs;

    /**
     *
     */
    private Integer zgx;

    /**
     *
     */
    private String parentareaid;

    /**
     *
     */
    private String parentareacode;

}
