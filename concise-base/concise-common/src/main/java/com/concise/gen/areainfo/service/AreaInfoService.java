package com.concise.gen.areainfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.areainfo.entity.AreaInfo;
import com.concise.gen.areainfo.entity.AreaInfoVO;
import com.concise.gen.areainfo.param.AreaInfoParam;

import java.util.List;

/**
 * 省市县乡镇service接口
 *
 * <AUTHOR>
 * @date 2024-01-03 17:33:22
 */
public interface AreaInfoService extends IService<AreaInfo> {

    /**
     * 查询省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    PageResult<AreaInfo> page(AreaInfoParam areaInfoParam);

    /**
     * 省市县乡镇列表
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    List<AreaInfo> list(AreaInfoParam areaInfoParam);

    /**
     * 添加省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    void add(AreaInfoParam areaInfoParam);

    /**
     * 删除省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    void delete(AreaInfoParam areaInfoParam);

    /**
     * 编辑省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    void edit(AreaInfoParam areaInfoParam);

    /**
     * 查看省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
     AreaInfo detail(AreaInfoParam areaInfoParam);

    /**
     * 省市区级联树
     * @return
     */
    List<AreaInfoVO> tree();

    /**
     * 获取行政区划code
     * 街道返回上级code(area_level=4)
     * @param areaId id
     * @return code
     */
    String getAreaCode(String areaId);
    String transToAreaCode(String areaIds);
    String transToAreaIds(String areaCode);

    /**
     * 获取行政区划名称
     * @param areaId areaId
     * @return String
     */
    String getFullChinese(String areaId);
}
