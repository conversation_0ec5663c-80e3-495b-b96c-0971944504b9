package com.concise.gen.investigationotheruser.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 调查评估-其他用户参数类
 *
 * <AUTHOR>
 * @date 2025-08-01 11:02:13
*/
@Data
public class InvestigationOtherUserParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查name参数", groups = {add.class, edit.class})
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空，请检查phone参数", groups = {add.class, edit.class})
    private String phone;

}
