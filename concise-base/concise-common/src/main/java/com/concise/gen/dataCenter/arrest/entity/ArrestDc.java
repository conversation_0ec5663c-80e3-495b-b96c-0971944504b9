package com.concise.gen.dataCenter.arrest.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 提请逮捕
 *
 * <AUTHOR>
 * @date 2023-09-19 17:30:07
 */
@Data
@TableName("correction_arrest")
public class ArrestDc{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 矫正对象id
     */
    private String pid;

    /**
     * 矫正对象姓名
     */
    private String pname;

    /**
     * 提请理由
     */
    private String tqly;

    /**
     * 提请意见
     */
    private String tqyj;

    /**
     * 提请意见中文值
     */
    private String tqyjName;

    /**
     * 机构意见
     */
    private String jgyj;

    /**
     * 司法所申请时间
     */
    private Date sfssqsj;

    /**
     *
     */
    private String ncsgajg;

    /**
     *
     */
    private String ncsgajglx;

    /**
     *
     */
    private String ncsgajglxName;

    /**
     * 地市id
     */
    private String dishiId;

    /**
     * 地市名称
     */
    private String dishiName;

    /**
     * 区县id
     */
    private String quxianId;

    /**
     * 区县名称
     */
    private String quxianName;

    /**
     * 街道id
     */
    private String jiedaoId;

    /**
     * 街道名称
     */
    private String jiedaoName;

    /**
     * 最后更新时间
     */
    private Date lastModifiedTime;

}
