package com.concise.gen.correctionplacechangeimmigration.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 执行地变更_外省迁入参数类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CorrectionPlacechangeImmigrationParam extends BaseParam {


    private Set<String> orgs;
    /**
     * 受理时间
     */
    private String slsj;
    /**
     * 受理说明
     */
    private String slsm;
    /**
     * 受理文书
     */
    private String slws;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 委托编号
     */
    @NotBlank(message = "委托编号不能为空，请检查wtbh参数", groups = {add.class, edit.class})
    private String wtbh;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 拟适用社区矫正人员类型
     */
    @NotBlank(message = "拟适用社区矫正人员类型不能为空，请检查nsysqjzrylx参数", groups = {add.class, edit.class})
    private String nsysqjzrylx;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 居住地地址(省)
     */
    @NotBlank(message = "居住地地址(省)不能为空，请检查jzddzP参数", groups = {add.class, edit.class})
    private String jzddzP;

    /**
     * 居住地地址(市)
     */
    @NotBlank(message = "居住地地址(市)不能为空，请检查jzddzC参数", groups = {add.class, edit.class})
    private String jzddzC;

    /**
     * 居住地地址
     */
    @NotBlank(message = "居住地地址不能为空，请检查jzddz参数", groups = {add.class, edit.class})
    private String jzddz;

    /**
     * 是否有居住地地址明细
     */
    @NotBlank(message = "是否有居住地地址明细不能为空，请检查sfyjzddzmx参数", groups = {add.class, edit.class})
    private String sfyjzddzmx;

    /**
     * 居住地地址明细
     */
    @NotBlank(message = "居住地地址明细不能为空，请检查jzddzmx参数", groups = {add.class, edit.class})
    private String jzddzmx;

    /**
     * 户籍是否与居住地相同
     */
    @NotBlank(message = "户籍是否与居住地相同不能为空，请检查hjsfyjzdxt参数", groups = {add.class, edit.class})
    private String hjsfyjzdxt;

    /**
     * 户籍地址(省)
     */
    @NotBlank(message = "户籍地址(省)不能为空，请检查hjdzP参数", groups = {add.class, edit.class})
    private String hjdzP;

    /**
     * 户籍地址(市)
     */
    @NotBlank(message = "户籍地址(市)不能为空，请检查hjdzC参数", groups = {add.class, edit.class})
    private String hjdzC;

    /**
     * 户籍地址
     */
    @NotBlank(message = "户籍地址不能为空，请检查hjdz参数", groups = {add.class, edit.class})
    private String hjdz;

    /**
     * 是否有户籍地址明细
     */
    @NotBlank(message = "是否有户籍地址明细不能为空，请检查sfyhjdzmx参数", groups = {add.class, edit.class})
    private String sfyhjdzmx;

    /**
     * 户籍地址明细
     */
    @NotBlank(message = "户籍地址明细不能为空，请检查hjdzmx参数", groups = {add.class, edit.class})
    private String hjdzmx;

    /**
     * 工作单位
     */
    @NotBlank(message = "工作单位不能为空，请检查hzdw参数", groups = {add.class, edit.class})
    private String hzdw;

    /**
     * 拟适用矫正类别
     */
    @NotBlank(message = "拟适用矫正类别不能为空，请检查nsyjzlb参数", groups = {add.class, edit.class})
    private String nsyjzlb;

    /**
     * 是否有原判刑期
     */
    @NotBlank(message = "是否有原判刑期不能为空，请检查sfyypxq参数", groups = {add.class, edit.class})
    private String sfyypxq;

    /**
     * 原判刑期
     */
    @NotBlank(message = "原判刑期不能为空，请检查ypxq参数", groups = {add.class, edit.class})
    private String ypxq;

    /**
     * 原判刑期开始日期
     */
    @NotNull(message = "原判刑期开始日期不能为空，请检查ypxqksrq参数", groups = {add.class, edit.class})
    private String ypxqksrq;

    /**
     * 原判刑期结束日期
     */
    @NotNull(message = "原判刑期结束日期不能为空，请检查ypxqjsrq参数", groups = {add.class, edit.class})
    private String ypxqjsrq;

    /**
     * 原判刑罚
     */
    @NotBlank(message = "原判刑罚不能为空，请检查ypxf参数", groups = {add.class, edit.class})
    private String ypxf;

    /**
     * 附加刑
     */
    @NotBlank(message = "附加刑不能为空，请检查fjx参数", groups = {add.class, edit.class})
    private String fjx;

    /**
     * 判决机关
     */
    @NotBlank(message = "判决机关不能为空，请检查pjjg参数", groups = {add.class, edit.class})
    private String pjjg;

    /**
     * 判决日期
     */
    @NotNull(message = "判决日期不能为空，请检查pjrq参数", groups = {add.class, edit.class})
    private String pjrq;

    /**
     * 指派调查单位
     */
    @NotBlank(message = "指派调查单位不能为空，请检查zpdcdwmc参数", groups = {add.class, edit.class})
    private String zpdcdwmc;

    /**
     * 指派调查单位ID
     */
    @NotBlank(message = "指派调查单位ID不能为空，请检查zpdcdwId参数", groups = {add.class, edit.class})
    private String zpdcdwId;

    /**
     * 指派人
     */
    @NotBlank(message = "指派人不能为空，请检查zpr参数", groups = {add.class, edit.class})
    private String zpr;

    /**
     * 指派时间
     */
    @NotNull(message = "指派时间不能为空，请检查zpsj参数", groups = {add.class, edit.class})
    private String zpsj;

    /**
     * 指派备注
     */
    @NotBlank(message = "指派备注不能为空，请检查zpbz参数", groups = {add.class, edit.class})
    private String zpbz;

    /**
     * 家庭和社会关系
     */
    @NotBlank(message = "家庭和社会关系不能为空，请检查jthshgx参数", groups = {add.class, edit.class})
    private String jthshgx;

    /**
     * 社会危险性、对所居住社区的影响等情况
     */
    @NotBlank(message = "社会危险性、对所居住社区的影响等情况不能为空，请检查shwxxyx参数", groups = {add.class, edit.class})
    private String shwxxyx;

    /**
     * 拟禁止的事项
     */
    @NotBlank(message = "拟禁止的事项不能为空，请检查njzdsx参数", groups = {add.class, edit.class})
    private String njzdsx;

    /**
     * 犯罪行为的后果和影响
     */
    @NotBlank(message = "犯罪行为的后果和影响不能为空，请检查fzxwdhghyx参数", groups = {add.class, edit.class})
    private String fzxwdhghyx;

    /**
     * 居住地村（居）民委员会和被害人意见
     */
    @NotBlank(message = "居住地村（居）民委员会和被害人意见不能为空，请检查wyhhbhryj参数", groups = {add.class, edit.class})
    private String wyhhbhryj;

    /**
     * 调查评估意见
     */
    @NotBlank(message = "调查评估意见不能为空，请检查dcpgyj参数", groups = {add.class, edit.class})
    private String dcpgyj;

    /**
     * 调查人
     */
    @NotBlank(message = "调查人不能为空，请检查dcr参数", groups = {add.class, edit.class})
    private String dcr;

    /**
     * 调查单位
     */
    @NotBlank(message = "调查单位不能为空，请检查dcdw参数", groups = {add.class, edit.class})
    private String dcdw;

    /**
     * 调查单位ID
     */
    @NotBlank(message = "调查单位ID不能为空，请检查dcdwId参数", groups = {add.class, edit.class})
    private String dcdwId;

    /**
     * 指派矫正单位
     */
    @NotBlank(message = "指派矫正单位不能为空，请检查zpjzdw参数", groups = {add.class, edit.class})
    private String zpjzdw;

    private String jzdwId;
    /**
     * 指派矫正单位ID
     */
    @NotBlank(message = "指派矫正单位ID不能为空，请检查zpjzdwId参数", groups = {add.class, edit.class})
    private String zpjzdwId;

    /**
     * 调查意见审核人
     */
    @NotBlank(message = "调查意见审核人不能为空，请检查dcyjshr参数", groups = {add.class, edit.class})
    private String dcyjshr;

    /**
     * 附件1
     */
    @NotBlank(message = "附件1不能为空，请检查file1参数", groups = {add.class, edit.class})
    private String file1;

    /**
     * 是否按时报到
     */
    @NotBlank(message = "是否按时报到不能为空，请检查sfasbd参数", groups = {add.class, edit.class})
    private String sfasbd;

    /**
     * 报到时间
     */
    @NotNull(message = "报到时间不能为空，请检查bdsj参数", groups = {add.class, edit.class})
    private String bdsj;

    /**
     * 反馈时间
     */
    @NotNull(message = "反馈时间不能为空，请检查fksj参数", groups = {add.class, edit.class})
    private String fksj;

    /**
     * 反馈备注
     */
    @NotBlank(message = "反馈备注不能为空，请检查fkbz参数", groups = {add.class, edit.class})
    private String fkbz;
    /**
     * 迁入原因
     */
    private String qryy;
    /**
     * 迁出省（市）
     */
    private String qcss;
    /**
     * 申请时间
     */
    private String sqsj;
    /**
     * 调查状态
     */
    private String dczt;
    /**
     * 接收状态
     */
    private String jszt;

}
