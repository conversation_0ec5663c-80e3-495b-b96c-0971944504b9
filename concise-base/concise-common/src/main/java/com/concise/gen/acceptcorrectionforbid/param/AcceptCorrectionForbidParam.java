package com.concise.gen.acceptcorrectionforbid.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 矫正对象禁止令信息信息接收表参数类
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:19
*/
@Data
public class AcceptCorrectionForbidParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     *
     */
    private String contactId;

    /**
     * 禁止令类型
     */
    @NotBlank(message = "禁止令类型不能为空，请检查jzllx参数", groups = {add.class, edit.class})
    private String jzllx;

    /**
     * 禁止令内容
     */
    @NotBlank(message = "禁止令内容不能为空，请检查jzlnr参数", groups = {add.class, edit.class})
    private String jzlnr;

    /**
     * 禁止期限开始日期
     */
    @NotNull(message = "禁止期限开始日期不能为空，请检查jzqxksrq参数", groups = {add.class, edit.class})
    private String jzqxksrq;

    /**
     * 禁止期限结束日期
     */
    @NotNull(message = "禁止期限结束日期不能为空，请检查jzqxjsrq参数", groups = {add.class, edit.class})
    private String jzqxjsrq;

    /**
     * 是否被宣告禁止令
     */
    @NotBlank(message = "是否被宣告禁止令不能为空，请检查sfbxgjzl参数", groups = {add.class, edit.class})
    private String sfbxgjzl;

    /**
     * 特定区域坐标
     */
    @NotBlank(message = "特定区域坐标不能为空，请检查tdqyzb参数", groups = {add.class, edit.class})
    private String tdqyzb;

}
