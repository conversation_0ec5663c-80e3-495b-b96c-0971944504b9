package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP3Record;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP3RecordParam;

import java.util.List;

/**
 * 调查评估协查_调查_记录service接口
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:55
 */
public interface CoordinateInvestigateP3RecordService extends IService<CoordinateInvestigateP3Record> {

    /**
     * 查询调查评估协查_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
    PageResult<CoordinateInvestigateP3Record> page(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam);

    /**
     * 调查评估协查_调查_记录列表
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
    List<CoordinateInvestigateP3Record> list(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam);

    /**
     * 添加调查评估协查_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
    void add(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam);

    /**
     * 删除调查评估协查_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
    void delete(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam);

    /**
     * 编辑调查评估协查_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
    void edit(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam);

    /**
     * 查看调查评估协查_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
     CoordinateInvestigateP3Record detail(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam);
}
