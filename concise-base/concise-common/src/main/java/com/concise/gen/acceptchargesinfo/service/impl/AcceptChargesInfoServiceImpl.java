package com.concise.gen.acceptchargesinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.gen.acceptchargesinfo.entity.AcceptChargesInfo;
import com.concise.gen.acceptchargesinfo.mapper.AcceptChargesInfoMapper;
import com.concise.gen.acceptchargesinfo.service.AcceptChargesInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 具体罪名信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:09
 */
@Service
public class AcceptChargesInfoServiceImpl extends ServiceImpl<AcceptChargesInfoMapper, AcceptChargesInfo> implements AcceptChargesInfoService {

    @Override
    public List<AcceptChargesInfo> list(String contactId) {
        QueryWrapper<AcceptChargesInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcceptChargesInfo::getContactId, contactId);
        return this.list(queryWrapper);
    }
}
