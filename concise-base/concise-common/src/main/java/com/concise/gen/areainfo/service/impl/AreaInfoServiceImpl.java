package com.concise.gen.areainfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.areainfo.entity.AreaInfo;
import com.concise.gen.areainfo.entity.AreaInfoVO;
import com.concise.gen.areainfo.enums.AreaInfoExceptionEnum;
import com.concise.gen.areainfo.mapper.AreaInfoMapper;
import com.concise.gen.areainfo.param.AreaInfoParam;
import com.concise.gen.areainfo.service.AreaInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 省市县乡镇service接口实现类
 *
 * <AUTHOR>
 * @date 2024-01-03 17:33:22
 */
@Service
public class AreaInfoServiceImpl extends ServiceImpl<AreaInfoMapper, AreaInfo> implements AreaInfoService {

    @Override
    public PageResult<AreaInfo> page(AreaInfoParam areaInfoParam) {
        QueryWrapper<AreaInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(areaInfoParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getAreacode())) {
                queryWrapper.lambda().eq(AreaInfo::getAreacode, areaInfoParam.getAreacode());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getAreaname())) {
                queryWrapper.lambda().eq(AreaInfo::getAreaname, areaInfoParam.getAreaname());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getArealevel())) {
                queryWrapper.lambda().eq(AreaInfo::getArealevel, areaInfoParam.getArealevel());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getZxs())) {
                queryWrapper.lambda().eq(AreaInfo::getZxs, areaInfoParam.getZxs());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getZgx())) {
                queryWrapper.lambda().eq(AreaInfo::getZgx, areaInfoParam.getZgx());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getParentareaid())) {
                queryWrapper.lambda().eq(AreaInfo::getParentareaid, areaInfoParam.getParentareaid());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(areaInfoParam.getParentareacode())) {
                queryWrapper.lambda().eq(AreaInfo::getParentareacode, areaInfoParam.getParentareacode());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AreaInfo> list(AreaInfoParam areaInfoParam) {
        return this.list();
    }

    @Override
    public void add(AreaInfoParam areaInfoParam) {
        AreaInfo areaInfo = new AreaInfo();
        BeanUtil.copyProperties(areaInfoParam, areaInfo);
        this.save(areaInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AreaInfoParam areaInfoParam) {
        this.removeById(areaInfoParam.getAreaid());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AreaInfoParam areaInfoParam) {
        AreaInfo areaInfo = this.queryAreaInfo(areaInfoParam);
        BeanUtil.copyProperties(areaInfoParam, areaInfo);
        this.updateById(areaInfo);
    }

    @Override
    public AreaInfo detail(AreaInfoParam areaInfoParam) {
        return this.queryAreaInfo(areaInfoParam);
    }

    /**
     * 获取省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    private AreaInfo queryAreaInfo(AreaInfoParam areaInfoParam) {
        AreaInfo areaInfo = this.getById(areaInfoParam.getAreaid());
        if (ObjectUtil.isNull(areaInfo)) {
            throw new ServiceException(AreaInfoExceptionEnum.NOT_EXIST);
        }
        return areaInfo;
    }

    @Override
    public List<AreaInfoVO> tree() {
        List<AreaInfoVO> areaInfoVOList = new ArrayList<>();
        List<AreaInfo> list = this.list();
        List<AreaInfo> firstList = list.stream().filter(e -> e.getArealevel() == 1).collect(Collectors.toList());
        for (AreaInfo areaInfo : firstList) {
            AreaInfoVO areaInfoVO = new AreaInfoVO();
            areaInfoVO.setLabel(areaInfo.getAreaname());
            areaInfoVO.setValue(areaInfo.getAreaid());
            buildChildren(list, areaInfoVO, areaInfo);
            areaInfoVOList.add(areaInfoVO);
        }
        return areaInfoVOList;
    }

    @Override
    public String getAreaCode(String areaId) {
        AreaInfo byId = this.getById(areaId);
        if (byId == null) {
            return "";
        }
        if (4 == byId.getArealevel()) {
            return byId.getParentareacode();
        }
        return byId.getAreacode();
    }

    @Override
    public String getFullChinese(String areaId) {
        List<String> areaNames = new ArrayList<>();
        AreaInfo current = this.getById(areaId);
        while (current != null) {
            areaNames.add(current.getAreaname());
            if (current.getArealevel() == 1) {
                break;
            }
            current = this.getById(current.getParentareaid());
        }

        return String.join("", ListUtil.reverse(areaNames));
    }

    private void buildChildren(List<AreaInfo> list, AreaInfoVO areaInfoVO, AreaInfo areaInfo) {
        if (areaInfo.getArealevel() < 4) {
            List<AreaInfo> infoList = list.stream().filter(e -> areaInfoVO.getValue().equals(e.getParentareaid())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(infoList)) {
                List<AreaInfoVO> areaInfoVOList = new ArrayList<>();
                for (AreaInfo info : infoList) {
                    AreaInfoVO infoVO = new AreaInfoVO();
                    infoVO.setLabel(info.getAreaname());
                    infoVO.setValue(info.getAreaid());
                    buildChildren(list, infoVO, info);
                    areaInfoVOList.add(infoVO);
                }
                areaInfoVO.setChildren(areaInfoVOList);
            }
        }
    }


    @Override
    public String transToAreaCode(String areaIds) {
        if (ObjectUtil.isNotEmpty(areaIds)) {
            String[] areaCode = areaIds.split(",");
            return this.getAreaCode(areaCode[areaCode.length-1]);
        }
        return "";
    }

    @Override
    public String transToAreaIds(String areaCode) {
        List<String> areaIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(areaCode)) {
            AreaInfo areaInfo = this.lambdaQuery().eq(AreaInfo::getAreacode, areaCode).last("limit 1").one();
            getParentArea(areaInfo,areaIds);
        }
        if (areaIds.size()>0) {
            return String.join(",", ListUtil.reverse(areaIds));
        }else {
            return "";
        }
    }

    private void getParentArea(AreaInfo areaInfo,List<String> areaIds){
        if (areaInfo == null) {
            return;
        }
        areaIds.add(areaInfo.getAreaid());
        if (areaInfo.getArealevel()!=1) {
            AreaInfo parentArea = this.getById(areaInfo.getParentareaid());
            getParentArea(parentArea,areaIds);
        }
    }

}
