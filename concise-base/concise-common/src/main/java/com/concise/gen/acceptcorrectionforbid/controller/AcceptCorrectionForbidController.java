package com.concise.gen.acceptcorrectionforbid. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.acceptcorrectionforbid.param.AcceptCorrectionForbidParam;
import com.concise.gen.acceptcorrectionforbid.service.AcceptCorrectionForbidService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 矫正对象禁止令信息信息接收表控制器
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:19
 */
@Api(tags = "矫正对象禁止令信息信息接收表")
@RestController
public class AcceptCorrectionForbidController {

    @Resource
    private AcceptCorrectionForbidService acceptCorrectionForbidService;

    /**
     * 查询矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    @GetMapping("/acceptCorrectionForbid/page")
    @ApiOperation("矫正对象禁止令信息信息接收表_分页查询")
    public ResponseData page(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        return new SuccessResponseData(acceptCorrectionForbidService.page(acceptCorrectionForbidParam));
    }

    /**
     * 添加矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    @PostMapping("/acceptCorrectionForbid/add")
    @ApiOperation("矫正对象禁止令信息信息接收表_增加")
    @BusinessLog(title = "矫正对象禁止令信息信息接收表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AcceptCorrectionForbidParam.add.class) AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        acceptCorrectionForbidService.add(acceptCorrectionForbidParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    @PostMapping("/acceptCorrectionForbid/delete")
    @ApiOperation("矫正对象禁止令信息信息接收表_删除")
    @BusinessLog(title = "矫正对象禁止令信息信息接收表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AcceptCorrectionForbidParam.delete.class) AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        acceptCorrectionForbidService.delete(acceptCorrectionForbidParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    @PostMapping("/acceptCorrectionForbid/edit")
    @ApiOperation("矫正对象禁止令信息信息接收表_编辑")
    @BusinessLog(title = "矫正对象禁止令信息信息接收表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AcceptCorrectionForbidParam.edit.class) AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        acceptCorrectionForbidService.edit(acceptCorrectionForbidParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    @GetMapping("/acceptCorrectionForbid/detail")
    @ApiOperation("矫正对象禁止令信息信息接收表_查看")
    public ResponseData detail(@Validated(AcceptCorrectionForbidParam.detail.class) AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        return new SuccessResponseData(acceptCorrectionForbidService.detail(acceptCorrectionForbidParam));
    }

    /**
     * 矫正对象禁止令信息信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    @GetMapping("/acceptCorrectionForbid/list")
    @ApiOperation("矫正对象禁止令信息信息接收表_列表")
    public ResponseData list(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        return new SuccessResponseData(acceptCorrectionForbidService.list(acceptCorrectionForbidParam.getContactId()));
    }

}
