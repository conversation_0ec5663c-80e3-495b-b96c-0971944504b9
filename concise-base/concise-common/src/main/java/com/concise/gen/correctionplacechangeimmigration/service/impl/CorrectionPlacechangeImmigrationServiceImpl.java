package com.concise.gen.correctionplacechangeimmigration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionplacechangeimmigration.entity.CorrectionPlacechangeImmigration;
import com.concise.gen.correctionplacechangeimmigration.enums.CorrectionPlacechangeImmigrationExceptionEnum;
import com.concise.gen.correctionplacechangeimmigration.mapper.CorrectionPlacechangeImmigrationMapper;
import com.concise.gen.correctionplacechangeimmigration.param.CorrectionPlacechangeImmigrationParam;
import com.concise.gen.correctionplacechangeimmigration.service.CorrectionPlacechangeImmigrationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 执行地变更_外省迁入service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
@Service
public class CorrectionPlacechangeImmigrationServiceImpl extends ServiceImpl<CorrectionPlacechangeImmigrationMapper, CorrectionPlacechangeImmigration> implements CorrectionPlacechangeImmigrationService {

    @Override
    public PageResult<CorrectionPlacechangeImmigration> page(CorrectionPlacechangeImmigrationParam param) {
        QueryWrapper<CorrectionPlacechangeImmigration> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(CorrectionPlacechangeImmigration::getZpjzdwId, param.getOrgs());
            }

            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CorrectionPlacechangeImmigration::getXm, param.getXm());
            }
            // 根据接收状态
            if (ObjectUtil.isNotEmpty(param.getJszt())) {
                queryWrapper.lambda().eq(CorrectionPlacechangeImmigration::getJszt, param.getJszt());
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(CorrectionPlacechangeImmigration::getSqsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(CorrectionPlacechangeImmigration::getSqsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectionPlacechangeImmigration::getSqsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionPlacechangeImmigration> list(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam) {
        CorrectionPlacechangeImmigration correctionPlacechangeImmigration = new CorrectionPlacechangeImmigration();
        BeanUtil.copyProperties(correctionPlacechangeImmigrationParam, correctionPlacechangeImmigration);
        this.save(correctionPlacechangeImmigration);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam) {
        this.removeById(correctionPlacechangeImmigrationParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam) {
        CorrectionPlacechangeImmigration correctionPlacechangeImmigration = this.queryCorrectionPlacechangeImmigration(correctionPlacechangeImmigrationParam);
        BeanUtil.copyProperties(correctionPlacechangeImmigrationParam, correctionPlacechangeImmigration);
        this.updateById(correctionPlacechangeImmigration);
    }

    @Override
    public CorrectionPlacechangeImmigration detail(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam) {
        return this.queryCorrectionPlacechangeImmigration(correctionPlacechangeImmigrationParam);
    }

    /**
     * 获取执行地变更_外省迁入
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
    private CorrectionPlacechangeImmigration queryCorrectionPlacechangeImmigration(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam) {
        CorrectionPlacechangeImmigration correctionPlacechangeImmigration = this.getById(correctionPlacechangeImmigrationParam.getId());
        if (ObjectUtil.isNull(correctionPlacechangeImmigration)) {
            throw new ServiceException(CorrectionPlacechangeImmigrationExceptionEnum.NOT_EXIST);
        }
        return correctionPlacechangeImmigration;
    }
}
