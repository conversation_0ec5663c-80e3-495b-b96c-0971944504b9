package com.concise.gen.coordinateinvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP4;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateP4ExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateP4Mapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP4Param;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP4Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估协查_初审小组意见service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:57
 */
@Service
public class CoordinateInvestigateP4ServiceImpl extends ServiceImpl<CoordinateInvestigateP4Mapper, CoordinateInvestigateP4> implements CoordinateInvestigateP4Service {

    @Override
    public PageResult<CoordinateInvestigateP4> page(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        QueryWrapper<CoordinateInvestigateP4> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(coordinateInvestigateP4Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getPid())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getPid, coordinateInvestigateP4Param.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getZt())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getZt, coordinateInvestigateP4Param.getZt());
            }
            // 根据初审人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getCsr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getCsr, coordinateInvestigateP4Param.getCsr());
            }
            // 根据初审时间 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getCssj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getCssj, coordinateInvestigateP4Param.getCssj());
            }
            // 根据初审意见 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getCsyj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getCsyj, coordinateInvestigateP4Param.getCsyj());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getThly())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getThly, coordinateInvestigateP4Param.getThly());
            }
            // 根据合议事项 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getHysx())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getHysx, coordinateInvestigateP4Param.getHysx());
            }
            // 根据主持人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getZcr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getZcr, coordinateInvestigateP4Param.getZcr());
            }
            // 根据合议地点 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getHydd())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getHydd, coordinateInvestigateP4Param.getHydd());
            }
            // 根据合议时间 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getHysj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getHysj, coordinateInvestigateP4Param.getHysj());
            }
            // 根据合议人员(jsonarray) 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getHyry())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getHyry, coordinateInvestigateP4Param.getHyry());
            }
            // 根据记录人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getJlr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getJlr, coordinateInvestigateP4Param.getJlr());
            }
            // 根据合议情况 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getHyqk())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getHyqk, coordinateInvestigateP4Param.getHyqk());
            }
            // 根据合议意见 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getHyyj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getHyyj, coordinateInvestigateP4Param.getHyyj());
            }
            // 根据负责人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getFzr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getFzr, coordinateInvestigateP4Param.getFzr());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP4Param.getBz())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP4::getBz, coordinateInvestigateP4Param.getBz());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateP4> list(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        CoordinateInvestigateP4 coordinateInvestigateP4 = new CoordinateInvestigateP4();
        BeanUtil.copyProperties(coordinateInvestigateP4Param, coordinateInvestigateP4);
        this.save(coordinateInvestigateP4);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        this.removeById(coordinateInvestigateP4Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        CoordinateInvestigateP4 coordinateInvestigateP4 = this.queryCoordinateInvestigateP4(coordinateInvestigateP4Param);
        BeanUtil.copyProperties(coordinateInvestigateP4Param, coordinateInvestigateP4);
        this.updateById(coordinateInvestigateP4);
    }

    @Override
    public CoordinateInvestigateP4 detail(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        return this.queryCoordinateInvestigateP4(coordinateInvestigateP4Param);
    }

    /**
     * 获取调查评估协查_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
    private CoordinateInvestigateP4 queryCoordinateInvestigateP4(CoordinateInvestigateP4Param coordinateInvestigateP4Param) {
        CoordinateInvestigateP4 coordinateInvestigateP4 = this.getById(coordinateInvestigateP4Param.getId());
        if (ObjectUtil.isNull(coordinateInvestigateP4)) {
            throw new ServiceException(CoordinateInvestigateP4ExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateP4;
    }
}
