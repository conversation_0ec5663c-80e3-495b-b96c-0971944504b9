package com.concise.gen.ciInvestigate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 迁入调查评估_接收
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:15
 */
@Data
@TableName("correction_immigration_investigate_p2")
public class InvestigateP2 {

    private Date createTime;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 接收状态
     */
    private String zt;

    /**
     * 接收人
     */
    private String jsr;

    /**
     * 接收时间
     */
    @Excel(name = "接收时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jssj;

    /**
     * 备注
     */
    private String bz;

    /**
     * 退回理由
     */
    private String thly;

}
