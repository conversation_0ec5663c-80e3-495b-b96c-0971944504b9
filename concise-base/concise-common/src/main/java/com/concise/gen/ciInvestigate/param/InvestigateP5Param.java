package com.concise.gen.ciInvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 迁入调查评估_初审集体评议参数类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:21
*/
@Data
public class InvestigateP5Param extends BaseParam {
    private Date createTime;
    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 调查步骤id
     */
    @NotBlank(message = "调查步骤id不能为空，请检查zt参数", groups = {add.class, edit.class})
    private String zt;

    /**
     * 初审人
     */
    @NotBlank(message = "初审人不能为空，请检查csr参数", groups = {add.class, edit.class})
    private String csr;

    /**
     * 初审时间
     */
    @NotNull(message = "初审时间不能为空，请检查cssj参数", groups = {add.class, edit.class})
    private String cssj;

    /**
     * 初审意见
     */
    @NotBlank(message = "初审意见不能为空，请检查csyj参数", groups = {add.class, edit.class})
    private String csyj;

    /**
     * 退回理由
     */
    @NotBlank(message = "退回理由不能为空，请检查thly参数", groups = {add.class, edit.class})
    private String thly;

    /**
     * 评议审核事项
     */
    @NotBlank(message = "评议审核事项不能为空，请检查pyshsx参数", groups = {add.class, edit.class})
    private String pyshsx;

    /**
     * 主持人
     */
    @NotBlank(message = "主持人不能为空，请检查zcr参数", groups = {add.class, edit.class})
    private String zcr;

    /**
     * 评议审核地点
     */
    @NotBlank(message = "评议审核地点不能为空，请检查pyshdd参数", groups = {add.class, edit.class})
    private String pyshdd;

    /**
     * 评议审核时间
     */
    @NotNull(message = "评议审核时间不能为空，请检查pyshsj参数", groups = {add.class, edit.class})
    private String pyshsj;

    /**
     * 评议审核人员(jsonarray)
     */
    @NotBlank(message = "评议审核人员(jsonarray)不能为空，请检查pyshry参数", groups = {add.class, edit.class})
    private String pyshry;

    /**
     * 记录人
     */
    @NotBlank(message = "记录人不能为空，请检查jlr参数", groups = {add.class, edit.class})
    private String jlr;

    /**
     * 评议审核情况
     */
    @NotBlank(message = "评议审核情况不能为空，请检查pyshqk参数", groups = {add.class, edit.class})
    private String pyshqk;

    /**
     * 评议审核意见
     */
    @NotBlank(message = "评议审核意见不能为空，请检查pyshyj参数", groups = {add.class, edit.class})
    private String pyshyj;

    /**
     * 负责人
     */
    @NotBlank(message = "负责人不能为空，请检查fzr参数", groups = {add.class, edit.class})
    private String fzr;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

}
