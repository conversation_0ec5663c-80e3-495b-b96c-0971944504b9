package com.concise.gen.acceptrecommit.param;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.format.DateTimeFormat;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SendLogExport {
    @ExcelProperty(value = "姓名")
    private String xm;
    @ExcelProperty(value = "矫正单位")
    private String jzjgName;
    @ExcelProperty(value = "接收单位")
    private String jsdw;
    @DateTimeFormat(value="yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "发送时间")
    private String fasong<PERSON>jian;
}
