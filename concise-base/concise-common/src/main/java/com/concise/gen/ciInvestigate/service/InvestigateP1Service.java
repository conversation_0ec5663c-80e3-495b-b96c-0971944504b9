package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP1;
import com.concise.gen.ciInvestigate.param.InvestigateP1Param;
import java.util.List;

/**
 * 迁入调查评估service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:13
 */
public interface InvestigateP1Service extends IService<InvestigateP1> {

    /**
     * 查询迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    PageResult<InvestigateP1> page(InvestigateP1Param investigateP1Param);

    /**
     * 迁入调查评估列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    List<InvestigateP1> list(InvestigateP1Param investigateP1Param);

    /**
     * 添加迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    void add(InvestigateP1Param investigateP1Param);

    /**
     * 删除迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    void delete(InvestigateP1Param investigateP1Param);

    /**
     * 编辑迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    void edit(InvestigateP1Param investigateP1Param);

    /**
     * 查看迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
     InvestigateP1 detail(InvestigateP1Param investigateP1Param);
}
