package com.concise.gen.coordinateinvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 调查评估协查_初审小组意见参数类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:57
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateInvestigateP4Param extends BaseParam {

    private Date createTime;
    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 调查步骤id
     */
    @NotBlank(message = "调查步骤id不能为空，请检查zt参数", groups = {add.class, edit.class})
    private String zt;

    /**
     * 初审人
     */
    @NotBlank(message = "初审人不能为空，请检查csr参数", groups = {add.class, edit.class})
    private String csr;

    /**
     * 初审时间
     */
    @NotNull(message = "初审时间不能为空，请检查cssj参数", groups = {add.class, edit.class})
    private String cssj;

    /**
     * 初审意见
     */
    @NotBlank(message = "初审意见不能为空，请检查csyj参数", groups = {add.class, edit.class})
    private String csyj;

    /**
     * 退回理由
     */
    @NotBlank(message = "退回理由不能为空，请检查thly参数", groups = {add.class, edit.class})
    private String thly;

    /**
     * 合议事项
     */
    @NotBlank(message = "合议事项不能为空，请检查hysx参数", groups = {add.class, edit.class})
    private String hysx;

    /**
     * 主持人
     */
    @NotBlank(message = "主持人不能为空，请检查zcr参数", groups = {add.class, edit.class})
    private String zcr;

    /**
     * 合议地点
     */
    @NotBlank(message = "合议地点不能为空，请检查hydd参数", groups = {add.class, edit.class})
    private String hydd;

    /**
     * 合议时间
     */
    @NotNull(message = "合议时间不能为空，请检查hysj参数", groups = {add.class, edit.class})
    private String hysj;

    /**
     * 合议人员(jsonarray)
     */
    @NotBlank(message = "合议人员(jsonarray)不能为空，请检查hyry参数", groups = {add.class, edit.class})
    private String hyry;

    /**
     * 记录人
     */
    @NotBlank(message = "记录人不能为空，请检查jlr参数", groups = {add.class, edit.class})
    private String jlr;

    /**
     * 合议情况
     */
    @NotBlank(message = "合议情况不能为空，请检查hyqk参数", groups = {add.class, edit.class})
    private String hyqk;

    /**
     * 合议意见
     */
    @NotBlank(message = "合议意见不能为空，请检查hyyj参数", groups = {add.class, edit.class})
    private String hyyj;

    /**
     * 负责人
     */
    @NotBlank(message = "负责人不能为空，请检查fzr参数", groups = {add.class, edit.class})
    private String fzr;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

}
