package com.concise.gen.acceptbaseinfo.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.acceptcorrectionobject.param.AcceptCorrectionObjectParam;
import com.concise.gen.accepttemporarilyoutsideprison.param.AcceptTemporarilyOutsidePrisonParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
* 交付衔接协同参数类
 *
 * <AUTHOR>
 * @date 2023-02-03 16:46:06
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptBaseInfoParam extends BaseParam {
    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    private AcceptCorrectionObjectParam obj;
    private AcceptTemporarilyOutsidePrisonParam top;

    private String taskId;
    /**
     * 发送万达选项
     * all 完整信息
     * [万达人员id] 仅发送文书到对应人员下
     */
    private String sendWd;

    /**
     * 协同编号
     */
    private String xtbh;

    /**
     * 数据状态
     */
    private String zt;

    /**
     * 数据来源(机构类型)
     */
    private String sjlylx;
    /**
     * 数据来源名称
     */
    private String sjlylxmc;

    /**
     * 数据来源
     * 1：省内
     * 2：省外
     * */
    private String dataFrom;
    /**
     * 协同类型
     * 01 判处管制
     * 02 宣告缓刑
     * 03 裁定假释
     * 04 决定暂外
     * 05 矫正人员接收
     */
    private String xtlx;
    /**
     * 协同类型名称
     */
    private String xtlxmc;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 接收单位
     */
    private String jsdw;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 接收单位名称
     */
    private String jsdwmc;

    /**
     * 推送单位
     */
    private String tsdw;

    /**
     * 推送单位名称
     */
    private String tsdwmc;

    /**
     * 送达时间
     */
    private Date sdsj;

    /**
     * 反馈时间
     */
    private Date fksj;


}
