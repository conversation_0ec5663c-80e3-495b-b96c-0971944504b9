package com.concise.gen.acceptcorrectionforbid.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionforbid.entity.AcceptCorrectionForbid;
import com.concise.gen.acceptcorrectionforbid.param.AcceptCorrectionForbidParam;
import java.util.List;

/**
 * 矫正对象禁止令信息信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:19
 */
public interface AcceptCorrectionForbidService extends IService<AcceptCorrectionForbid> {

    /**
     * 查询矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    PageResult<AcceptCorrectionForbid> page(AcceptCorrectionForbidParam acceptCorrectionForbidParam);

    /**
     * 矫正对象禁止令信息信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    List<AcceptCorrectionForbid> list(String contactId);

    /**
     * 添加矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    void add(AcceptCorrectionForbidParam acceptCorrectionForbidParam);

    /**
     * 删除矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    void delete(AcceptCorrectionForbidParam acceptCorrectionForbidParam);

    /**
     * 编辑矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    void edit(AcceptCorrectionForbidParam acceptCorrectionForbidParam);

    /**
     * 查看矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
     AcceptCorrectionForbid detail(AcceptCorrectionForbidParam acceptCorrectionForbidParam);
}
