package com.concise.gen.acceptcriminalrecord.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcriminalrecord.entity.AcceptCriminalRecord;
import com.concise.gen.acceptcriminalrecord.param.AcceptCriminalRecordParam;
import java.util.List;

/**
 * 前科信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:43
 */
public interface AcceptCriminalRecordService extends IService<AcceptCriminalRecord> {

    /**
     * 查询前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    PageResult<AcceptCriminalRecord> page(AcceptCriminalRecordParam acceptCriminalRecordParam);

    /**
     * 前科信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    List<AcceptCriminalRecord> list(String contactId);

    /**
     * 添加前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    void add(AcceptCriminalRecordParam acceptCriminalRecordParam);

    /**
     * 删除前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    void delete(AcceptCriminalRecordParam acceptCriminalRecordParam);

    /**
     * 编辑前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    void edit(AcceptCriminalRecordParam acceptCriminalRecordParam);

    /**
     * 查看前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
     AcceptCriminalRecord detail(AcceptCriminalRecordParam acceptCriminalRecordParam);
}
