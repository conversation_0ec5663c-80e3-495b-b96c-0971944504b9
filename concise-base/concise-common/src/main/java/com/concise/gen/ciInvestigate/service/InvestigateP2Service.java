package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP2;
import com.concise.gen.ciInvestigate.param.InvestigateP2Param;
import java.util.List;

/**
 * 迁入调查评估_接收service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:15
 */
public interface InvestigateP2Service extends IService<InvestigateP2> {

    /**
     * 查询迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    PageResult<InvestigateP2> page(InvestigateP2Param investigateP2Param);

    /**
     * 迁入调查评估_接收列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    List<InvestigateP2> list(InvestigateP2Param investigateP2Param);

    /**
     * 添加迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    void add(InvestigateP2Param investigateP2Param);

    /**
     * 删除迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    void delete(InvestigateP2Param investigateP2Param);

    /**
     * 编辑迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    void edit(InvestigateP2Param investigateP2Param);

    /**
     * 查看迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
     InvestigateP2 detail(InvestigateP2Param investigateP2Param);
}
