package com.concise.gen.acceptcorrectionobject.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptbaseinfo.param.AcceptBaseInfoParam;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectionobject.entity.AcceptCorrectionObject;
import com.concise.gen.acceptcorrectionobject.param.AcceptCorrectionObjectParam;

import java.util.List;

/**
 * 矫正对象信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:05
 */
public interface AcceptCorrectionObjectService extends IService<AcceptCorrectionObject> {

    /**
     * 查询矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     */
    PageResult<AcceptCorrectionObject> page(AcceptCorrectionObjectParam acceptCorrectionObjectParam);

    /**
     * 添加矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     */
    List<AcceptCorrectionDocParam> add(AcceptCorrectionObjectParam acceptCorrectionObjectParam);

    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);

    /**
     * 编辑矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     * @return
     */
    boolean feedback(AcceptCorrectionObjectParam acceptCorrectionObjectParam);
    void edit(AcceptBaseInfoParam param);

    /**
     * 查看矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     */
     AcceptCorrectionObject detail(String id);

     void sendWd(String id);

     void getNoSignPdf(AcceptCorrectionObjectParam param);
     void getSignedPdf(AcceptCorrectionObjectParam param);
}
