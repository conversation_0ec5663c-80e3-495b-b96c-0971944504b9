package com.concise.gen.signatureauthorization.entity;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.gen.signatureauthorization.param.SignatureAuthorizationParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;

import java.util.Date;
import java.util.Date;
import java.util.Date;
import java.util.Date;

/**
 * 签章授权表
 *
 * <AUTHOR>
 * @date 2025-07-31 11:04:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("signature_authorization")
public class SignatureAuthorization extends BaseEntity {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 印章id
     */
    private String signatureId;

    /**
     * 授权使用人员id
     */
    private String userIds;

    /**
     * 授权使用人员姓名
     */
    private String userNames;

    /**
     * 授权开始时间
     */
    private Date beginTime;

    /**
     * 授权结束时间
     */
    private Date endTime;

    /**
     * 授权证明方式（1-在线授权，2-线下授权）
     */
    private String authorizationType;

    /**
     * 授权状态（1-使用中；2-已过期）
     */
    private String authorizationStatus;

    public String getAuthorizationStatus() {
        if (ObjectUtil.isAllNotEmpty(beginTime, endTime)) {
            boolean in = DateUtil.isIn(DateUtil.date(), beginTime, endTime);
            if (in) {
                authorizationStatus = "1";
            } else {
                authorizationStatus = "2";
            }
        }
        return authorizationStatus;
    }

    /**
     * 授权书文件
     */
    @TableField(exist = false)
    private SysFileInfo sysFileInfo;

    /**
     * 操作人
     */
    private String operator;

}
