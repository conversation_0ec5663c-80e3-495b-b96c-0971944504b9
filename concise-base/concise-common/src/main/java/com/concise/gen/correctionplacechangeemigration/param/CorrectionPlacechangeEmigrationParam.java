package com.concise.gen.correctionplacechangeemigration.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 执行地变更_跨省迁出参数类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CorrectionPlacechangeEmigrationParam extends BaseParam {

    private Set<String> orgs;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 社区矫正人员ID
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 执行地变更申请时间
     */
    private String sqsj;

    /**
     * 迁入地
     */
    private String qrd;

    /**
     * 迁入地明细
     */
    private String qrdmx;

    /**
     * 拟迁入矫正单位
     */
    //@NotBlank(message = "拟迁入矫正单位不能为空，请检查qrjzdw参数", groups = {add.class, edit.class})
    private String qrjzdw;

    /**
     * 拟迁入矫正单位ID
     */
    private String qrjzdwId;

    /**
     * 变更理由
     */
    private String bgly;

    /**
     * 附件1
     */
    private String file1;

    /**
     * 附件2
     */
    private String file2;

    /**
     * 报到状态
     */
    private String bdzt;

    /**
     * 调查评估意见
     */
    private String dcpgyj;
    /**
     * 报道时间
     */
    private String bdsj;
    /**
     * 矫正单位
     */
    private String jzdwId;
    /**
     * 矫正单位
     */
    private String jzdwmc;

}
