package com.concise.gen.acceptcorrectionforbid.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

/**
 * 矫正对象禁止令信息信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:19
 */
@Data
@TableName("accept_correction_forbid")
public class AcceptCorrectionForbid{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String contactId;

    /**
     * 禁止令类型
     */
    private String jzllx;

    /**
     * 禁止令内容
     */
    private String jzlnr;

    /**
     * 禁止期限开始日期
     */
    @Excel(name = "禁止期限开始日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jzqxksrq;

    /**
     * 禁止期限结束日期
     */
    @Excel(name = "禁止期限结束日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jzqxjsrq;

    /**
     * 是否被宣告禁止令
     */
    private String sfbxgjzl;

    /**
     * 特定区域坐标
     */
    private String tdqyzb;

}
