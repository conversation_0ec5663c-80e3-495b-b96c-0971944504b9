package com.concise.gen.coordinateinvestigate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 调查评估协查_审批
 *
 * <AUTHOR>
 * @date 2023-06-12 20:48:00
 */
@Data
@TableName("coordinate_investigate_p6")
public class CoordinateInvestigateP6{

    private Date createTime;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 调查步骤id
     */
    private String zt;

    /**
     * 审批人
     */
    private String spr;

    /**
     * 审批时间
     */
    private Date spsj;

    /**
     * 审批意见
     */
    private String spyj;

    /**
     * 退回理由
     */
    private String thly;

    /**
     * 评估意见
     * 字典值 dcpgyj
     * 是否适宜社区矫正
     */
    private String pgyj;


    /**
     * 调查结束时间
     */
    private Date dcjssj;


    /**
     * 调查评估意见
     */
    private String dcpgyj;

    /**
     * 文书字号
     */
    private String zh;

}
