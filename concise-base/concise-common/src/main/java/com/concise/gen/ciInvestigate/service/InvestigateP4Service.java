package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP4;
import com.concise.gen.ciInvestigate.param.InvestigateP4Param;
import java.util.List;

/**
 * 迁入调查评估_初审小组意见service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:19
 */
public interface InvestigateP4Service extends IService<InvestigateP4> {

    /**
     * 查询迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    PageResult<InvestigateP4> page(InvestigateP4Param investigateP4Param);

    /**
     * 迁入调查评估_初审小组意见列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    List<InvestigateP4> list(InvestigateP4Param investigateP4Param);

    /**
     * 添加迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    void add(InvestigateP4Param investigateP4Param);

    /**
     * 删除迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    void delete(InvestigateP4Param investigateP4Param);

    /**
     * 编辑迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    void edit(InvestigateP4Param investigateP4Param);

    /**
     * 查看迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
     InvestigateP4 detail(InvestigateP4Param investigateP4Param);
}
