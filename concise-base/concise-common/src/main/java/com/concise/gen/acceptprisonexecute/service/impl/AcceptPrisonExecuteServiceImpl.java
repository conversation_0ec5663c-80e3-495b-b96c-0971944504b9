package com.concise.gen.acceptprisonexecute.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptprisonexecute.entity.AcceptPrisonExecute;
import com.concise.gen.acceptprisonexecute.enums.AcceptPrisonExecuteExceptionEnum;
import com.concise.gen.acceptprisonexecute.mapper.AcceptPrisonExecuteMapper;
import com.concise.gen.acceptprisonexecute.param.AcceptPrisonExecuteParam;
import com.concise.gen.acceptprisonexecute.service.AcceptPrisonExecuteService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 收监执行接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
@Service
public class AcceptPrisonExecuteServiceImpl extends ServiceImpl<AcceptPrisonExecuteMapper, AcceptPrisonExecute> implements AcceptPrisonExecuteService {


    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private OrgCommonService orgCommonService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;

    @Override
    public PageResult<AcceptPrisonExecute> page(AcceptPrisonExecuteParam param) {
        QueryWrapper<AcceptPrisonExecute> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getTaskId())) {
                queryWrapper.lambda().eq(AcceptPrisonExecute::getTaskId, param.getTaskId());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(AcceptPrisonExecute::getXm, param.getXm());
            }
            // 根据数据状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(AcceptPrisonExecute::getZt, param.getZt());
            }
            // 根据数据来源(机构类型) 查询
            if (ObjectUtil.isNotEmpty(param.getSjlylx())) {
                queryWrapper.lambda().eq(AcceptPrisonExecute::getSjlylx, param.getSjlylx());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(param.getJsdw())) {
                queryWrapper.lambda().and(i -> i.eq(AcceptPrisonExecute::getJsdwId, param.getJsdw()).or().like(AcceptPrisonExecute::getJsdwPids, param.getJsdw()));
            }
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.apply("date_format (sdsj,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime() + "','%Y-%m-%d')")
                        .apply("date_format (sdsj,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime() + "','%Y-%m-%d')");
            }
            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(param.getTyfh())) {
                queryWrapper.lambda().eq(AcceptPrisonExecute::getTyfh, param.getTyfh());
            }
        }
        queryWrapper.lambda().orderByDesc(AcceptPrisonExecute::getSdsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptPrisonExecute> list(AcceptPrisonExecuteParam acceptPrisonExecuteParam) {
        return this.list();
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptPrisonExecuteParam param) {
        OrgCommon org = orgCommonService.getOrgByCode(param.getJsdw());
        param.setJsdwmc(org.getName());
        param.setJsdwId(org.getId());
        param.setJsdwPids(org.getPids());
        param.setTsdwmc(extOrgInfoService.getExtOrgName(param.getSjlylx(),param.getTsdw()));

        AcceptPrisonExecute acceptPrisonExecute = new AcceptPrisonExecute();
        BeanUtil.copyProperties(param, acceptPrisonExecute);
        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));
        this.save(acceptPrisonExecute);
        return param.getDocList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptPrisonExecuteParam acceptPrisonExecuteParam) {
        this.removeById(acceptPrisonExecuteParam.getId());
    }

    @Override
    public void delete(String taskId) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptPrisonExecuteParam acceptPrisonExecuteParam) {
        AcceptPrisonExecute acceptPrisonExecute = this.queryAcceptPrisonExecute(acceptPrisonExecuteParam);
        BeanUtil.copyProperties(acceptPrisonExecuteParam, acceptPrisonExecute);
        this.updateById(acceptPrisonExecute);
    }

    @Override
    public AcceptPrisonExecute detail(AcceptPrisonExecuteParam acceptPrisonExecuteParam) {
        return this.queryAcceptPrisonExecute(acceptPrisonExecuteParam);
    }

    /**
     * 获取收监执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
    private AcceptPrisonExecute queryAcceptPrisonExecute(AcceptPrisonExecuteParam acceptPrisonExecuteParam) {
        AcceptPrisonExecute acceptPrisonExecute = this.getById(acceptPrisonExecuteParam.getId());
        if (ObjectUtil.isNull(acceptPrisonExecute)) {
            throw new ServiceException(AcceptPrisonExecuteExceptionEnum.NOT_EXIST);
        }
        return acceptPrisonExecute;
    }
}
