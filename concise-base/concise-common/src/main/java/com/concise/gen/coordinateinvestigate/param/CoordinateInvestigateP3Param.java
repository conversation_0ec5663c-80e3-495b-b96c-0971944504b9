package com.concise.gen.coordinateinvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
* 调查评估协查_调查参数类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:53
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateInvestigateP3Param extends BaseParam {

    private List<CoordinateInvestigateP3RecordParam> records;

    private Date createTime;
    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 是否开展调查
     */
    @NotBlank(message = "是否开展调查不能为空，请检查zt参数", groups = {add.class, edit.class})
    private String zt;

    /**
     * 调查人
     */
    @NotBlank(message = "调查人不能为空，请检查dcr参数", groups = {add.class, edit.class})
    private String dcr;

    /**
     * 调查时间
     */
    @NotNull(message = "调查时间不能为空，请检查dcsj参数", groups = {add.class, edit.class})
    private String dcsj;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

    /**
     * 退回理由
     */
    @NotBlank(message = "退回理由不能为空，请检查thly参数", groups = {add.class, edit.class})
    private String thly;

    /**
     * 调查单位（县区局）
     */
    @NotBlank(message = "调查单位（县区局）不能为空，请检查dcdw1参数", groups = {add.class, edit.class})
    private String dcdw1;

    /**
     * 调查单位（司法所）
     */
    @NotBlank(message = "调查单位（司法所）不能为空，请检查dcdw2参数", groups = {add.class, edit.class})
    private String dcdw2;

    /**
     * 委托调查材料
     */
    @NotBlank(message = "委托调查材料不能为空，请检查wtdccl参数", groups = {add.class, edit.class})
    private String wtdccl;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空，请检查mz参数", groups = {add.class, edit.class})
    private String mz;

    /**
     * 别名
     */
    @NotBlank(message = "别名不能为空，请检查bm参数", groups = {add.class, edit.class})
    private String bm;

    /**
     * 曾用名
     */
    @NotBlank(message = "曾用名不能为空，请检查cym参数", groups = {add.class, edit.class})
    private String cym;

    /**
     * 籍贯
     */
    @NotBlank(message = "籍贯不能为空，请检查jg参数", groups = {add.class, edit.class})
    private String jg;

    /**
     * 家庭住址
     */
    @NotBlank(message = "家庭住址不能为空，请检查jtzz参数", groups = {add.class, edit.class})
    private String jtzz;

    /**
     * 家庭住址明细
     */
    @NotBlank(message = "家庭住址明细不能为空，请检查jtzzmx参数", groups = {add.class, edit.class})
    private String jtzzmx;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空，请检查lxdh参数", groups = {add.class, edit.class})
    private String lxdh;

    /**
     * 职业
     */
    @NotBlank(message = "职业不能为空，请检查zy参数", groups = {add.class, edit.class})
    private String zy;

    /**
     * 有无家庭成员
     */
    @NotBlank(message = "有无家庭成员不能为空，请检查ywjtcy参数", groups = {add.class, edit.class})
    private String ywjtcy;

    /**
     * 社会交往情况
     */
    @NotBlank(message = "社会交往情况不能为空，请检查shjwqk参数", groups = {add.class, edit.class})
    private String shjwqk;

    /**
     * 主要社会关系
     */
    @NotBlank(message = "主要社会关系不能为空，请检查zyshgx参数", groups = {add.class, edit.class})
    private String zyshgx;

    /**
     * 未成年对象的其他情况
     */
    @NotBlank(message = "未成年对象的其他情况不能为空，请检查wcn参数", groups = {add.class, edit.class})
    private String wcn;

    /**
     * 家庭成员(jsonarray)
     */
    @NotBlank(message = "家庭成员(jsonarray)不能为空，请检查jtcy参数", groups = {add.class, edit.class})
    private String jtcy;

    /**
     * 生理状况
     */
    @NotBlank(message = "生理状况不能为空，请检查slzk参数", groups = {add.class, edit.class})
    private String slzk;

    /**
     * 心理特征
     */
    @NotBlank(message = "心理特征不能为空，请检查xltz参数", groups = {add.class, edit.class})
    private String xltz;

    /**
     * 性格类型
     */
    @NotBlank(message = "性格类型不能为空，请检查xglx参数", groups = {add.class, edit.class})
    private String xglx;

    /**
     * 爱好特长
     */
    @NotBlank(message = "爱好特长不能为空，请检查ahtc参数", groups = {add.class, edit.class})
    private String ahtc;

    /**
     * 工作（学习）表现
     */
    @NotBlank(message = "工作（学习）表现不能为空，请检查gzxxbx参数", groups = {add.class, edit.class})
    private String gzxxbx;

    /**
     * 遵纪守法情况
     */
    @NotBlank(message = "遵纪守法情况不能为空，请检查zjsfqk参数", groups = {add.class, edit.class})
    private String zjsfqk;

    /**
     * 有无不良嗜好、行为恶习
     */
    @NotBlank(message = "有无不良嗜好、行为恶习不能为空，请检查blsh参数", groups = {add.class, edit.class})
    private String blsh;

    /**
     * 犯罪原因
     */
    @NotBlank(message = "犯罪原因不能为空，请检查fzyy参数", groups = {add.class, edit.class})
    private String fzyy;

    /**
     * 主观恶性
     */
    @NotBlank(message = "主观恶性不能为空，请检查zgex参数", groups = {add.class, edit.class})
    private String zgex;

    /**
     * 是否有犯罪前科
     */
    @NotBlank(message = "是否有犯罪前科不能为空，请检查sfyfzqk参数", groups = {add.class, edit.class})
    private String sfyfzqk;

    /**
     * 认罪悔罪态度
     */
    @NotBlank(message = "认罪悔罪态度不能为空，请检查rzhztd参数", groups = {add.class, edit.class})
    private String rzhztd;

    /**
     * 被害人或其亲属态度
     */
    @NotBlank(message = "被害人或其亲属态度不能为空，请检查bhrhqqstd参数", groups = {add.class, edit.class})
    private String bhrhqqstd;

    /**
     * 社会公众态度
     */
    @NotBlank(message = "社会公众态度不能为空，请检查shgztd参数", groups = {add.class, edit.class})
    private String shgztd;

    /**
     * 家庭成员态度
     */
    @NotBlank(message = "家庭成员态度不能为空，请检查jtcytd参数", groups = {add.class, edit.class})
    private String jtcytd;

    /**
     * 经济生活状况和环境
     */
    @NotBlank(message = "经济生活状况和环境不能为空，请检查jjshzkhhj参数", groups = {add.class, edit.class})
    private String jjshzkhhj;

    /**
     * 工作单位、就读学校和村（社区）基层组织意见
     */
    @NotBlank(message = "工作单位、就读学校和村（社区）基层组织意见不能为空，请检查zzyj参数", groups = {add.class, edit.class})
    private String zzyj;

    /**
     * 辖区公安派出所意见
     */
    @NotBlank(message = "辖区公安派出所意见不能为空，请检查gayj参数", groups = {add.class, edit.class})
    private String gayj;

}
