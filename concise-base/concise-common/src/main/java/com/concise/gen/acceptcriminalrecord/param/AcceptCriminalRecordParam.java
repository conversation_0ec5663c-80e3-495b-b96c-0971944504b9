package com.concise.gen.acceptcriminalrecord.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 前科信息接收表参数类
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:43
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptCriminalRecordParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查contactId参数", groups = {add.class, edit.class})
    private String contactId;

    /**
     * 刑种
     */
    @NotBlank(message = "刑种不能为空，请检查xz参数", groups = {add.class, edit.class})
    private String xz;

    /**
     * 判决日期
     */
    @NotBlank(message = "判决日期不能为空，请检查pjrq参数", groups = {add.class, edit.class})
    private String pjrq;

    /**
     * 判决法院
     */
    @NotBlank(message = "判决法院不能为空，请检查pjfy参数", groups = {add.class, edit.class})
    private String pjfy;

    /**
     * 罪名
     */
    @NotBlank(message = "罪名不能为空，请检查zm参数", groups = {add.class, edit.class})
    private String zm;

    /**
     * 原判刑期
     */
    @NotBlank(message = "原判刑期不能为空，请检查ypxq参数", groups = {add.class, edit.class})
    private String ypxq;

    /**
     * 执行机关
     */
    @NotBlank(message = "执行机关不能为空，请检查zxjg参数", groups = {add.class, edit.class})
    private String zxjg;

    /**
     * 执行刑期
     */
    @NotBlank(message = "执行刑期不能为空，请检查zxxq参数", groups = {add.class, edit.class})
    private String zxxq;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

}
