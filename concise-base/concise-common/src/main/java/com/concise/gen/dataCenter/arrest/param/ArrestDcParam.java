package com.concise.gen.dataCenter.arrest.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 提请逮捕参数类
 *
 * <AUTHOR>
 * @date 2023-09-19 17:30:07
*/
@Data
public class ArrestDcParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 矫正对象id
     */
    @NotBlank(message = "矫正对象id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 矫正对象姓名
     */
    @NotBlank(message = "矫正对象姓名不能为空，请检查pname参数", groups = {add.class, edit.class})
    private String pname;

    /**
     * 提请理由
     */
    @NotNull(message = "提请理由不能为空，请检查tqly参数", groups = {add.class, edit.class})
    private String tqly;

    /**
     * 提请意见
     */
    @NotBlank(message = "提请意见不能为空，请检查tqyj参数", groups = {add.class, edit.class})
    private String tqyj;

    /**
     * 提请意见中文值
     */
    @NotNull(message = "提请意见中文值不能为空，请检查tqyjName参数", groups = {add.class, edit.class})
    private String tqyjName;

    /**
     * 机构意见
     */
    @NotNull(message = "机构意见不能为空，请检查jgyj参数", groups = {add.class, edit.class})
    private String jgyj;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空，请检查sfssqsj参数", groups = {add.class, edit.class})
    private String sfssqsj;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查ncsgajg参数", groups = {add.class, edit.class})
    private String ncsgajg;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查ncsgajglx参数", groups = {add.class, edit.class})
    private String ncsgajglx;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查ncsgajglxName参数", groups = {add.class, edit.class})
    private String ncsgajglxName;

    /**
     * 地市id
     */
    @NotBlank(message = "地市id不能为空，请检查dishiId参数", groups = {add.class, edit.class})
    private String dishiId;

    /**
     * 地市名称
     */
    @NotBlank(message = "地市名称不能为空，请检查dishiName参数", groups = {add.class, edit.class})
    private String dishiName;

    /**
     * 区县id
     */
    @NotBlank(message = "区县id不能为空，请检查quxianId参数", groups = {add.class, edit.class})
    private String quxianId;

    /**
     * 区县名称
     */
    @NotBlank(message = "区县名称不能为空，请检查quxianName参数", groups = {add.class, edit.class})
    private String quxianName;

    /**
     * 街道id
     */
    @NotBlank(message = "街道id不能为空，请检查jiedaoId参数", groups = {add.class, edit.class})
    private String jiedaoId;

    /**
     * 街道名称
     */
    @NotBlank(message = "街道名称不能为空，请检查jiedaoName参数", groups = {add.class, edit.class})
    private String jiedaoName;

    /**
     * 最后更新时间
     */
    @NotNull(message = "最后更新时间不能为空，请检查lastModifiedTime参数", groups = {add.class, edit.class})
    private String lastModifiedTime;

}
