package com.concise.gen.correctionterminatext.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
* 解矫协同信息参数类
 *
 * <AUTHOR>
 * @date 2022-06-17 15:08:39
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CorrectionTerminateXtParam extends BaseParam {

    private String jzjgId;
    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 统一赋号
     */
    @NotBlank(message = "统一赋号不能为空，请检查tyfh参数", groups = {add.class, edit.class})
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    @NotBlank(message = "社区矫正案件编号不能为空，请检查sqjzajbh参数", groups = {add.class, edit.class})
    private String sqjzajbh;

    /**
     * 罪犯编号
     */
    @NotBlank(message = "罪犯编号不能为空，请检查zfbh参数", groups = {add.class, edit.class})
    private String zfbh;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空，请检查zjlx参数", groups = {add.class, edit.class})
    private String zjlx;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空，请检查zjhm参数", groups = {add.class, edit.class})
    private String zjhm;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 户籍地
     */
    @NotBlank(message = "户籍地不能为空，请检查hjd参数", groups = {add.class, edit.class})
    private String hjd;

    /**
     * 户籍地详址
     */
    @NotBlank(message = "户籍地详址不能为空，请检查hjdxz参数", groups = {add.class, edit.class})
    private String hjdxz;

    /**
     * 现住地
     */
    @NotBlank(message = "现住地不能为空，请检查xzd参数", groups = {add.class, edit.class})
    private String xzd;

    /**
     * 现住地详址
     */
    @NotBlank(message = "现住地详址不能为空，请检查xzdxz参数", groups = {add.class, edit.class})
    private String xzdxz;

    /**
     * 生效判决书字号
     */
    @NotBlank(message = "生效判决书字号不能为空，请检查sxpjszh参数", groups = {add.class, edit.class})
    private String sxpjszh;

    /**
     * 判决文书生效日期
     */
    @NotNull(message = "判决文书生效日期不能为空，请检查pjwssxrq参数", groups = {add.class, edit.class})
    private String pjwssxrq;

    /**
     * 犯罪类型
     */
    @NotBlank(message = "犯罪类型不能为空，请检查fzlx参数", groups = {add.class, edit.class})
    private String fzlx;

    /**
     * 具体罪名
     */
    @NotBlank(message = "具体罪名不能为空，请检查jtzm参数", groups = {add.class, edit.class})
    private String jtzm;

    /**
     * 管制期限
     */
    @NotBlank(message = "管制期限不能为空，请检查gzqx参数", groups = {add.class, edit.class})
    private String gzqx;

    /**
     * 缓刑考验期限
     */
    @NotBlank(message = "缓刑考验期限不能为空，请检查hxkyqx参数", groups = {add.class, edit.class})
    private String hxkyqx;

    /**
     * 是否数罪并罚
     */
    @NotBlank(message = "是否数罪并罚不能为空，请检查sfszbf参数", groups = {add.class, edit.class})
    private String sfszbf;

    /**
     * 判决刑种
     */
    @NotBlank(message = "判决刑种不能为空，请检查pjxz参数", groups = {add.class, edit.class})
    private String pjxz;

    /**
     * 判决刑期开始日期
     */
    @NotNull(message = "判决刑期开始日期不能为空，请检查pjxqksrq参数", groups = {add.class, edit.class})
    private String pjxqksrq;

    /**
     * 判决刑期结束日期
     */
    @NotNull(message = "判决刑期结束日期不能为空，请检查pjxqjsrq参数", groups = {add.class, edit.class})
    private String pjxqjsrq;

    /**
     * 有期徒刑期限
     */
    @NotBlank(message = "有期徒刑期限不能为空，请检查yqtxqx参数", groups = {add.class, edit.class})
    private String yqtxqx;

    /**
     * 附加刑
     */
    @NotBlank(message = "附加刑不能为空，请检查fjx参数", groups = {add.class, edit.class})
    private String fjx;

    /**
     * 审判机关名称
     */
    @NotBlank(message = "审判机关名称不能为空，请检查spjgmc参数", groups = {add.class, edit.class})
    private String spjgmc;

    /**
     * 剥夺政治权利年限
     */
    @NotBlank(message = "剥夺政治权利年限不能为空，请检查bdzzqlnx参数", groups = {add.class, edit.class})
    private String bdzzqlnx;

    /**
     * 具体罚款金额
     */
    @NotBlank(message = "具体罚款金额不能为空，请检查jtfkje参数", groups = {add.class, edit.class})
    private String jtfkje;

    /**
     * 驱逐出境
     */
    @NotBlank(message = "驱逐出境不能为空，请检查qzcj参数", groups = {add.class, edit.class})
    private String qzcj;

    /**
     * 没收财产
     */
    @NotBlank(message = "没收财产不能为空，请检查mscc参数", groups = {add.class, edit.class})
    private String mscc;

    /**
     * 是否“五独”
     */
    @NotBlank(message = "是否“五独”不能为空，请检查sfwd参数", groups = {add.class, edit.class})
    private String sfwd;

    /**
     * 是否“五涉”
     */
    @NotBlank(message = "是否“五涉”不能为空，请检查sfws参数", groups = {add.class, edit.class})
    private String sfws;

    /**
     * 是否有“四史”
     */
    @NotBlank(message = "是否有“四史”不能为空，请检查sfyss参数", groups = {add.class, edit.class})
    private String sfyss;

    /**
     * 假释裁定日期
     */
    @NotNull(message = "假释裁定日期不能为空，请检查jscdrq参数", groups = {add.class, edit.class})
    private String jscdrq;

    /**
     * 假释考验期
     */
    @NotBlank(message = "假释考验期不能为空，请检查jskyq参数", groups = {add.class, edit.class})
    private String jskyq;

    /**
     * 假释考验期起日
     */
    @NotNull(message = "假释考验期起日不能为空，请检查jskyqqr参数", groups = {add.class, edit.class})
    private String jskyqqr;

    /**
     * 假释考验期止日
     */
    @NotNull(message = "假释考验期止日不能为空，请检查jskyqzr参数", groups = {add.class, edit.class})
    private String jskyqzr;

    /**
     * 假释裁定书文号
     */
    @NotBlank(message = "假释裁定书文号不能为空，请检查jscdswh参数", groups = {add.class, edit.class})
    private String jscdswh;

    /**
     * 假释裁定机关
     */
    @NotBlank(message = "假释裁定机关不能为空，请检查jscdjg参数", groups = {add.class, edit.class})
    private String jscdjg;

    /**
     * 矫正机构
     */
    @NotBlank(message = "矫正机构不能为空，请检查jzjg参数", groups = {add.class, edit.class})
    private String jzjg;

    /**
     * 交付执行日期
     */
    @NotNull(message = "交付执行日期不能为空，请检查jfzxrq参数", groups = {add.class, edit.class})
    private String jfzxrq;

    /**
     * 执行通知书日期
     */
    @NotNull(message = "执行通知书日期不能为空，请检查zxtzsrq参数", groups = {add.class, edit.class})
    private String zxtzsrq;

    /**
     * 移交罪犯机关类型
     */
    @NotBlank(message = "移交罪犯机关类型不能为空，请检查yjzfjglx参数", groups = {add.class, edit.class})
    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */
    @NotBlank(message = "移交罪犯机关名称不能为空，请检查yjzfjgmc参数", groups = {add.class, edit.class})
    private String yjzfjgmc;

    /**
     * 暂予监外执行决定日期
     */
    @NotNull(message = "暂予监外执行决定日期不能为空，请检查zyjwzxjdrq参数", groups = {add.class, edit.class})
    private String zyjwzxjdrq;

    /**
     * 暂予监外执行决定书文号
     */
    @NotBlank(message = "暂予监外执行决定书文号不能为空，请检查zyjwzxjdswh参数", groups = {add.class, edit.class})
    private String zyjwzxjdswh;

    /**
     * 暂予监外执行起日
     */
    @NotNull(message = "暂予监外执行起日不能为空，请检查zyjwzxqr参数", groups = {add.class, edit.class})
    private String zyjwzxqr;

    /**
     * 暂予监外执行止日
     */
    @NotNull(message = "暂予监外执行止日不能为空，请检查zyjwzxzr参数", groups = {add.class, edit.class})
    private String zyjwzxzr;

    /**
     * 决定书文号
     */
    @NotBlank(message = "决定书文号不能为空，请检查jdswh参数", groups = {add.class, edit.class})
    private String jdswh;

    /**
     * 撤销假释决定日期
     */
    @NotNull(message = "撤销假释决定日期不能为空，请检查cxjsjdrq参数", groups = {add.class, edit.class})
    private String cxjsjdrq;

    /**
     * 撤销假释原因
     */
    @NotBlank(message = "撤销假释原因不能为空，请检查cxjsyy参数", groups = {add.class, edit.class})
    private String cxjsyy;

    /**
     * 撤销假释决定机关
     */
    @NotBlank(message = "撤销假释决定机关不能为空，请检查cxjsjdjg参数", groups = {add.class, edit.class})
    private String cxjsjdjg;

    /**
     * 撤销缓刑决定日期
     */
    @NotBlank(message = "撤销缓刑决定日期不能为空，请检查cxhxjdrq参数", groups = {add.class, edit.class})
    private String cxhxjdrq;

    /**
     * 撤销缓刑原因
     */
    @NotBlank(message = "撤销缓刑原因不能为空，请检查cxhxyy参数", groups = {add.class, edit.class})
    private String cxhxyy;

    /**
     * 撤销缓刑决定机关
     */
    @NotBlank(message = "撤销缓刑决定机关不能为空，请检查cxhxjdjg参数", groups = {add.class, edit.class})
    private String cxhxjdjg;

    /**
     * 收监决定日期
     */
    @NotNull(message = "收监决定日期不能为空，请检查sjjdrq参数", groups = {add.class, edit.class})
    private String sjjdrq;

    /**
     * 收监原因
     */
    @NotBlank(message = "收监原因不能为空，请检查sjyy参数", groups = {add.class, edit.class})
    private String sjyy;

    /**
     * 收监决定机关
     */
    @NotBlank(message = "收监决定机关不能为空，请检查sjjdjg参数", groups = {add.class, edit.class})
    private String sjjdjg;

    /**
     * 矫正类别
     */
    @NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class})
    private String jzlb;

    /**
     * 决定机关
     */
    @NotBlank(message = "决定机关不能为空，请检查jdjg参数", groups = {add.class, edit.class})
    private String jdjg;

    /**
     * 司法所
     */
    @NotBlank(message = "司法所不能为空，请检查sfs参数", groups = {add.class, edit.class})
    private String sfs;

    /**
     * 社区矫正开始日期
     */
    @NotNull(message = "社区矫正开始日期不能为空，请检查sqjzksrq参数", groups = {add.class, edit.class})
    private String sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    @NotNull(message = "社区矫正结束日期不能为空，请检查sqjzjsrq参数", groups = {add.class, edit.class})
    private String sqjzjsrq;

    /**
     * 矫正期限
     */
    @NotBlank(message = "矫正期限不能为空，请检查jzqx参数", groups = {add.class, edit.class})
    private String jzqx;

    /**
     * 社区矫正执行地
     */
    @NotBlank(message = "社区矫正执行地不能为空，请检查sqjzzxd参数", groups = {add.class, edit.class})
    private String sqjzzxd;

    /**
     * 解除社矫/终止社矫类型
     */
    @NotBlank(message = "解除社矫/终止社矫类型不能为空，请检查jcsjzzsjlx参数", groups = {add.class, edit.class})
    private String jcsjzzsjlx;

    /**
     * 解除社矫/终止社矫原因
     */
    @NotBlank(message = "解除社矫/终止社矫原因不能为空，请检查jcsjzzsjyy参数", groups = {add.class, edit.class})
    private String jcsjzzsjyy;

    /**
     * 通知书文号
     */
    @NotBlank(message = "通知书文号不能为空，请检查tzswh参数", groups = {add.class, edit.class})
    private String tzswh;

    /**
     * 通知书日期
     */
    @NotNull(message = "通知书日期不能为空，请检查tzsrq参数", groups = {add.class, edit.class})
    private String tzsrq;

    /**
     * 入矫日期
     */
    @NotNull(message = "入矫日期不能为空，请检查rjrq参数", groups = {add.class, edit.class})
    private String rjrq;

    /**
     * 解除社矫/终止社矫日期
     */
    @NotNull(message = "解除社矫/终止社矫日期不能为空，请检查jcsjzzsjrq参数", groups = {add.class, edit.class})
    private String jcsjzzsjrq;

    /**
     * 是否删除
     */
    @NotBlank(message = "是否删除不能为空，请检查sfsc参数", groups = {add.class, edit.class})
    private String sfsc;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空，请检查cjsj参数", groups = {add.class, edit.class})
    private String cjsj;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空，请检查gxsj参数", groups = {add.class, edit.class})
    private String gxsj;

    /**
     * 司法局所在区划的检察院代码
     */
    @NotBlank(message = "司法局所在区划的检察院代码不能为空，请检查jcydm参数", groups = {add.class, edit.class})
    private String jcydm;

    /**
     * 司法局所在区划的法院代码
     */
    @NotBlank(message = "司法局所在区划的法院代码不能为空，请检查fydm参数", groups = {add.class, edit.class})
    private String fydm;

}
