package com.concise.gen.chargeinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.chargeinfo.entity.ChargeInfo;
import com.concise.gen.chargeinfo.enums.ChargeInfoExceptionEnum;
import com.concise.gen.chargeinfo.mapper.ChargeInfoMapper;
import com.concise.gen.chargeinfo.param.ChargeInfoParam;
import com.concise.gen.chargeinfo.service.ChargeInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 罪名表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-16 15:30:23
 */
@Service
public class ChargeInfoServiceImpl extends ServiceImpl<ChargeInfoMapper, ChargeInfo> implements ChargeInfoService {

    @Override
    public PageResult<ChargeInfo> page(ChargeInfoParam chargeInfoParam) {
        QueryWrapper<ChargeInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(chargeInfoParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(chargeInfoParam.getCharge())) {
                queryWrapper.lambda().eq(ChargeInfo::getCharge, chargeInfoParam.getCharge());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(chargeInfoParam.getShift())) {
                queryWrapper.lambda().eq(ChargeInfo::getShift, chargeInfoParam.getShift());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<ChargeInfo> list(ChargeInfoParam chargeInfoParam) {

        return this.baseMapper.getChargeInfoList(chargeInfoParam.getChargeCode(),chargeInfoParam.getCharge());
    }

    @Override
    public void add(ChargeInfoParam chargeInfoParam) {
        ChargeInfo chargeInfo = new ChargeInfo();
        BeanUtil.copyProperties(chargeInfoParam, chargeInfo);
        this.save(chargeInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(ChargeInfoParam chargeInfoParam) {
        this.removeById(chargeInfoParam.getChargeCode());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(ChargeInfoParam chargeInfoParam) {
        ChargeInfo chargeInfo = this.queryChargeInfo(chargeInfoParam);
        BeanUtil.copyProperties(chargeInfoParam, chargeInfo);
        this.updateById(chargeInfo);
    }

    @Override
    public ChargeInfo detail(ChargeInfoParam chargeInfoParam) {
        return this.queryChargeInfo(chargeInfoParam);
    }

    @Override
    public String toChinese(List<String> codeList) {
        if (codeList == null || codeList.size() == 0) {
            return null;
        }
        try {
            return this.baseMapper.toChinese(codeList);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    private ChargeInfo queryChargeInfo(ChargeInfoParam chargeInfoParam) {
        ChargeInfo chargeInfo = this.getById(chargeInfoParam.getChargeCode());
        if (ObjectUtil.isNull(chargeInfo)) {
            throw new ServiceException(ChargeInfoExceptionEnum.NOT_EXIST);
        }
        return chargeInfo;
    }
}
