package com.concise.gen.acceptcorrectiondoc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;

import java.util.List;

/**
 * 矫正对象法律文书信息信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
 */
public interface AcceptCorrectionDocService extends IService<AcceptCorrectionDoc> {

    /**
     * 查询矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    PageResult<AcceptCorrectionDoc> page(AcceptCorrectionDocParam acceptCorrectionDocParam);

    /**
     * 矫正对象法律文书信息信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    List<AcceptCorrectionDoc> list(String contactId);

    /**
     * 矫正对象法律文书信息信息接收表列表
     * @param taskId taskId
     * @return SysFileInfoParam
     */
    List<SysFileInfoVO> getSysFileInfoParamList(String taskId);
    List<SysFileInfoVO> getSysFileInfoList(String id, String serverNum);

    /**
     * 添加矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    void add(AcceptCorrectionDocParam acceptCorrectionDocParam);
    void addBatch(List<AcceptCorrectionDocParam> acceptCorrectionDocParam);

    /**
     * 删除矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    void delete(AcceptCorrectionDocParam acceptCorrectionDocParam);

    /**
     * 编辑矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    void edit(AcceptCorrectionDocParam acceptCorrectionDocParam);

    /**
     * 查看矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
     AcceptCorrectionDoc detail(AcceptCorrectionDocParam acceptCorrectionDocParam);

    /**
     * 重新下载文件
     * @param id id
     * @return AcceptCorrectionDoc
     */
    AcceptCorrectionDoc reLoadFileFromOss(String id);

    List<AcceptCorrectionDoc> reLoadFileListFromOss(String contactId);

    void sendDocToWd(String contactId);

    /**
     * 发送文书到万达
     * @param contactId 关联文书id
     * @param psnId 指定人员(万达人员id)
     */
    void sendDocToWd(String contactId,String psnId);

    void batchDownLoad(String contactId, String xm);
}
