package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP4;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP4Param;

import java.util.List;

/**
 * 调查评估协查_初审小组意见service接口
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:57
 */
public interface CoordinateInvestigateP4Service extends IService<CoordinateInvestigateP4> {

    /**
     * 查询调查评估协查_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
    PageResult<CoordinateInvestigateP4> page(CoordinateInvestigateP4Param coordinateInvestigateP4Param);

    /**
     * 调查评估协查_初审小组意见列表
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
    List<CoordinateInvestigateP4> list(CoordinateInvestigateP4Param coordinateInvestigateP4Param);

    /**
     * 添加调查评估协查_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
    void add(CoordinateInvestigateP4Param coordinateInvestigateP4Param);

    /**
     * 删除调查评估协查_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
    void delete(CoordinateInvestigateP4Param coordinateInvestigateP4Param);

    /**
     * 编辑调查评估协查_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
    void edit(CoordinateInvestigateP4Param coordinateInvestigateP4Param);

    /**
     * 查看调查评估协查_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:57
     */
     CoordinateInvestigateP4 detail(CoordinateInvestigateP4Param coordinateInvestigateP4Param);
}
