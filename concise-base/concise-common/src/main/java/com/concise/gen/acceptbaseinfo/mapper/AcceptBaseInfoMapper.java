package com.concise.gen.acceptbaseinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.acceptbaseinfo.entity.AcceptBaseInfo;
import com.concise.gen.acceptbaseinfo.vo.ExtOrgStatisticsVo;
import com.concise.gen.acceptbaseinfo.vo.StatVo;
import com.concise.gen.acceptbaseinfo.vo.StatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 交付衔接协同
 *
 * <AUTHOR>
 * @date 2023-02-03 16:46:06
 */
public interface AcceptBaseInfoMapper extends BaseMapper<AcceptBaseInfo> {
    /**
     * 获取行政区划名称
     * @param dist dist
     * @return String
     */
    String distName(String dist);

    /**
     * 转换字典
     * @param type   类别
     * @param dict   字典
     * @param source 原值
     * @return String 目标值
     */
    String extDictTrans(String type, String dict, String source);

    /**
     * 协同指标统计
     * @param orgId orgId
     * @param month month
     * @return StatisticsVo
     */
    List<StatisticsVo> indicatorCollaboration(@Param("orgId") String orgId, @Param("month") String month);

    /**
     * 发送单位统计结果保存
     */
    void insertExtOrgStatistics();
    /**
     * 统计后填充机构名称
     */
    void updateExtOrgStatisticsOrgName();

    /**
     * 协同排名
     * @param type 协同单位类别 现在可选项 监狱(61)、法院(20)、看守所(41)
     * @param orgSet 查询机构范围
     * @return List
     */
    List<ExtOrgStatisticsVo> rankExtOrgStatistics(int type, Set<String> orgSet);

    /**
     * 协同趋势
     * @param orgId orgId
     * @param level 层级
     * @param time 时间
     * @return List
     */
    List<StatisticsVo> trend(@Param("orgId") String orgId, @Param("level") int level, @Param("time") String time);

    /**
     * 统计 省
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel1(@Param("start") String start, @Param("end") String end);

    /**
     * 统计 市
     * @param orgId orgId
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel2(@Param("orgId") String orgId, @Param("start") String start, @Param("end") String end);
    /**
     * 统计 区县
     * @param orgId orgId
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel3(@Param("orgId") String orgId, @Param("start") String start, @Param("end") String end);
    /**
     * 统计 司法所
     * @param orgId orgId
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel4(@Param("orgId") String orgId, @Param("start") String start, @Param("end") String end);
}
