package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP4;
import com.concise.gen.ciInvestigate.enums.InvestigateP4ExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP4Mapper;
import com.concise.gen.ciInvestigate.param.InvestigateP4Param;
import com.concise.gen.ciInvestigate.service.InvestigateP4Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 迁入调查评估_初审小组意见service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:19
 */
@Service
public class InvestigateP4ServiceImpl extends ServiceImpl<InvestigateP4Mapper, InvestigateP4> implements InvestigateP4Service {

    @Override
    public PageResult<InvestigateP4> page(InvestigateP4Param investigateP4Param) {
        QueryWrapper<InvestigateP4> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP4Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getPid())) {
                queryWrapper.lambda().eq(InvestigateP4::getPid, investigateP4Param.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getZt())) {
                queryWrapper.lambda().eq(InvestigateP4::getZt, investigateP4Param.getZt());
            }
            // 根据初审人 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getCsr())) {
                queryWrapper.lambda().eq(InvestigateP4::getCsr, investigateP4Param.getCsr());
            }
            // 根据初审时间 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getCssj())) {
                queryWrapper.lambda().eq(InvestigateP4::getCssj, investigateP4Param.getCssj());
            }
            // 根据初审意见 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getCsyj())) {
                queryWrapper.lambda().eq(InvestigateP4::getCsyj, investigateP4Param.getCsyj());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getThly())) {
                queryWrapper.lambda().eq(InvestigateP4::getThly, investigateP4Param.getThly());
            }
            // 根据合议事项 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getHysx())) {
                queryWrapper.lambda().eq(InvestigateP4::getHysx, investigateP4Param.getHysx());
            }
            // 根据主持人 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getZcr())) {
                queryWrapper.lambda().eq(InvestigateP4::getZcr, investigateP4Param.getZcr());
            }
            // 根据合议地点 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getHydd())) {
                queryWrapper.lambda().eq(InvestigateP4::getHydd, investigateP4Param.getHydd());
            }
            // 根据合议时间 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getHysj())) {
                queryWrapper.lambda().eq(InvestigateP4::getHysj, investigateP4Param.getHysj());
            }
            // 根据合议人员(jsonarray) 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getHyry())) {
                queryWrapper.lambda().eq(InvestigateP4::getHyry, investigateP4Param.getHyry());
            }
            // 根据记录人 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getJlr())) {
                queryWrapper.lambda().eq(InvestigateP4::getJlr, investigateP4Param.getJlr());
            }
            // 根据合议情况 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getHyqk())) {
                queryWrapper.lambda().eq(InvestigateP4::getHyqk, investigateP4Param.getHyqk());
            }
            // 根据合议意见 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getHyyj())) {
                queryWrapper.lambda().eq(InvestigateP4::getHyyj, investigateP4Param.getHyyj());
            }
            // 根据负责人 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getFzr())) {
                queryWrapper.lambda().eq(InvestigateP4::getFzr, investigateP4Param.getFzr());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(investigateP4Param.getBz())) {
                queryWrapper.lambda().eq(InvestigateP4::getBz, investigateP4Param.getBz());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP4> list(InvestigateP4Param investigateP4Param) {
        return this.list();
    }

    @Override
    public void add(InvestigateP4Param investigateP4Param) {
        InvestigateP4 investigateP4 = new InvestigateP4();
        BeanUtil.copyProperties(investigateP4Param, investigateP4);
        this.save(investigateP4);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP4Param investigateP4Param) {
        this.removeById(investigateP4Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP4Param investigateP4Param) {
        InvestigateP4 investigateP4 = this.queryInvestigateP4(investigateP4Param);
        BeanUtil.copyProperties(investigateP4Param, investigateP4);
        this.updateById(investigateP4);
    }

    @Override
    public InvestigateP4 detail(InvestigateP4Param investigateP4Param) {
        return this.queryInvestigateP4(investigateP4Param);
    }

    /**
     * 获取迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    private InvestigateP4 queryInvestigateP4(InvestigateP4Param investigateP4Param) {
        InvestigateP4 investigateP4 = this.getById(investigateP4Param.getId());
        if (ObjectUtil.isNull(investigateP4)) {
            throw new ServiceException(InvestigateP4ExceptionEnum.NOT_EXIST);
        }
        return investigateP4;
    }
}
