package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP3Record;
import com.concise.gen.ciInvestigate.enums.InvestigateP3RecordExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP3RecordMapper;
import com.concise.gen.ciInvestigate.param.InvestigateP3RecordParam;
import com.concise.gen.ciInvestigate.service.InvestigateP3RecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 迁入调查评估_调查_记录service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:18
 */
@Service
public class InvestigateP3RecordServiceImpl extends ServiceImpl<InvestigateP3RecordMapper, InvestigateP3Record> implements InvestigateP3RecordService {

    @Override
    public PageResult<InvestigateP3Record> page(InvestigateP3RecordParam investigateP3RecordParam) {
        QueryWrapper<InvestigateP3Record> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP3RecordParam)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getPid())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getPid, investigateP3RecordParam.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getStepId())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getStepId, investigateP3RecordParam.getStepId());
            }
            // 根据被调查人姓名 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getXm())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getXm, investigateP3RecordParam.getXm());
            }
            // 根据与被告人（罪犯）关系 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getGx())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getGx, investigateP3RecordParam.getGx());
            }
            // 根据调查时间 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getDcsj())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getDcsj, investigateP3RecordParam.getDcsj());
            }
            // 根据调查地点 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getDcdd())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getDcdd, investigateP3RecordParam.getDcdd());
            }
            // 根据调查事项 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getDcsx())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getDcsx, investigateP3RecordParam.getDcsx());
            }
            // 根据调查笔录 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getDcbl())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getDcbl, investigateP3RecordParam.getDcbl());
            }
            // 根据调查人(jsonarray) 查询
            if (ObjectUtil.isNotEmpty(investigateP3RecordParam.getDcr())) {
                queryWrapper.lambda().eq(InvestigateP3Record::getDcr, investigateP3RecordParam.getDcr());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP3Record> list(InvestigateP3RecordParam investigateP3RecordParam) {
        return this.list();
    }

    @Override
    public void add(InvestigateP3RecordParam investigateP3RecordParam) {
        InvestigateP3Record investigateP3Record = new InvestigateP3Record();
        BeanUtil.copyProperties(investigateP3RecordParam, investigateP3Record);
        this.save(investigateP3Record);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP3RecordParam investigateP3RecordParam) {
        this.removeById(investigateP3RecordParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP3RecordParam investigateP3RecordParam) {
        InvestigateP3Record investigateP3Record = this.queryInvestigateP3Record(investigateP3RecordParam);
        BeanUtil.copyProperties(investigateP3RecordParam, investigateP3Record);
        this.updateById(investigateP3Record);
    }

    @Override
    public InvestigateP3Record detail(InvestigateP3RecordParam investigateP3RecordParam) {
        return this.queryInvestigateP3Record(investigateP3RecordParam);
    }

    /**
     * 获取迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    private InvestigateP3Record queryInvestigateP3Record(InvestigateP3RecordParam investigateP3RecordParam) {
        InvestigateP3Record investigateP3Record = this.getById(investigateP3RecordParam.getId());
        if (ObjectUtil.isNull(investigateP3Record)) {
            throw new ServiceException(InvestigateP3RecordExceptionEnum.NOT_EXIST);
        }
        return investigateP3Record;
    }
}
