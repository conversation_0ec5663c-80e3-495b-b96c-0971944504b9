package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP3;
import com.concise.gen.ciInvestigate.enums.InvestigateP3ExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP3Mapper;
import com.concise.gen.ciInvestigate.param.InvestigateP3Param;
import com.concise.gen.ciInvestigate.service.InvestigateP3RecordService;
import com.concise.gen.ciInvestigate.service.InvestigateP3Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 迁入调查评估_调查service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:16
 */
@Service
public class InvestigateP3ServiceImpl extends ServiceImpl<InvestigateP3Mapper, InvestigateP3> implements InvestigateP3Service {

    @Resource
    private InvestigateP3RecordService p3RecordService;
    @Override
    public PageResult<InvestigateP3> page(InvestigateP3Param investigateP3Param) {
        QueryWrapper<InvestigateP3> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP3Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigateP3Param.getPid())) {
                queryWrapper.lambda().eq(InvestigateP3::getPid, investigateP3Param.getPid());
            }
            // 根据是否开展调查 查询
            if (ObjectUtil.isNotEmpty(investigateP3Param.getZt())) {
                queryWrapper.lambda().eq(InvestigateP3::getZt, investigateP3Param.getZt());
            }

        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP3> list(InvestigateP3Param investigateP3Param) {
        return this.list();
    }

    @Override
    public void add(InvestigateP3Param investigateP3Param) {
        InvestigateP3 investigateP3 = new InvestigateP3();
        BeanUtil.copyProperties(investigateP3Param, investigateP3);
        this.save(investigateP3);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP3Param investigateP3Param) {
        this.removeById(investigateP3Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP3Param param) {
        InvestigateP3 investigateP3 = this.queryInvestigateP3(param);
        BeanUtil.copyProperties(param, investigateP3);
        if (param.getRecords() !=null) {
            param.getRecords().forEach(record -> {
                record.setStepId(param.getId());
                record.setPid(param.getPid());
                p3RecordService.add(record);
            });
        }

        this.updateById(investigateP3);
    }

    @Override
    public InvestigateP3 detail(InvestigateP3Param investigateP3Param) {
        return this.queryInvestigateP3(investigateP3Param);
    }

    /**
     * 获取迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    private InvestigateP3 queryInvestigateP3(InvestigateP3Param investigateP3Param) {
        InvestigateP3 investigateP3 = this.getById(investigateP3Param.getId());
        if (ObjectUtil.isNull(investigateP3)) {
            throw new ServiceException(InvestigateP3ExceptionEnum.NOT_EXIST);
        }
        return investigateP3;
    }
}
