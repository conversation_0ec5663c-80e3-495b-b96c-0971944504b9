package com.concise.gen.coordinatesupervisereceive.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 接收外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("coordinate_supervise_receive")
public class CoordinateSuperviseReceive extends BaseEntity {

    /**
     * 受理时间
     */
    private Date slsj;
    /**
     * 受理说明
     */
    private String slsm;
    /**
     * 受理文书
     */
    private String slws;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 委托单位
     */
    private String wtdwmc;

    /**
     * 委托单位id
     */
    private String wtdwId;

    /**
     * 收到委托时间
     */
    @Excel(name = "收到委托时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date sdwtsj;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 社区矫正人员ID
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正单位
     */
    private String jzdw;

    /**
     * 矫正单位ID
     */
    private String jzdwId;

    /**
     * 外出申请编号
     */
    private String wcsqbh;

    /**
     * 外出申请时间
     */
    @Excel(name = "外出申请时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date wcsqsj;

    /**
     * 外出目的地
     */
    private String wcmdd;

    /**
     * 外出开始时间
     */
    @Excel(name = "外出开始时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date wckssj;

    /**
     * 外出结束时间
     */
    @Excel(name = "外出结束时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date wcjssj;

    /**
     * 外出原因
     */
    private String wcyy;

    /**
     * 外出天数
     */
    private Integer wcts;

    /**
     * 是否提请监管
     */
    private String sftqjg;

    /**
     * 个别教育主题
     */
    private String gbjyzt;

    /**
     * 个别教育内容
     */
    private String gbjynr;

    /**
     * 个别教育地点
     */
    private String gbjydd;

    /**
     * 个别教育日期
     */
    @Excel(name = "个别教育日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date gbjyrq;

    /**
     * 个别教育机构
     */
    private String gbjyjg;

    /**
     * 报到日期
     */
    @Excel(name = "报到日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date bdrq;

    /**
     * 报到地点
     */
    private String bddd;

    /**
     * 报到方式
     */
    private String bdfs;

    /**
     * 报到机构
     */
    private String bdjg;


    /**
     * 0 待受理
     * 1
     */
    private String zt;

}
