package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP5;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP5Param;

import java.util.List;

/**
 * 调查评估协查_初审集体评议service接口
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:59
 */
public interface CoordinateInvestigateP5Service extends IService<CoordinateInvestigateP5> {

    /**
     * 查询调查评估协查_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
    PageResult<CoordinateInvestigateP5> page(CoordinateInvestigateP5Param coordinateInvestigateP5Param);

    /**
     * 调查评估协查_初审集体评议列表
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
    List<CoordinateInvestigateP5> list(CoordinateInvestigateP5Param coordinateInvestigateP5Param);

    /**
     * 添加调查评估协查_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
    void add(CoordinateInvestigateP5Param coordinateInvestigateP5Param);

    /**
     * 删除调查评估协查_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
    void delete(CoordinateInvestigateP5Param coordinateInvestigateP5Param);

    /**
     * 编辑调查评估协查_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
    void edit(CoordinateInvestigateP5Param coordinateInvestigateP5Param);

    /**
     * 查看调查评估协查_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
     CoordinateInvestigateP5 detail(CoordinateInvestigateP5Param coordinateInvestigateP5Param);
}
