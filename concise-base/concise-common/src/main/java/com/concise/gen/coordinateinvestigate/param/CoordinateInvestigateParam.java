package com.concise.gen.coordinateinvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 调查评估协查参数类
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateInvestigateParam extends BaseParam {

    private Set<String> orgs;
    /**
     * 受理时间
     */
    private String slsj;
    /**
     * 受理说明
     */
    private String slsm;
    /**
     * 受理文书
     */
    private String slws;

    private String zt;

    /**
     * 反馈时间
     */
    private String fksj;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 委托单位
     */
    private String wtdwmc;

    /**
     * 委托单位id
     */
    private String wtdwId;

    /**
     * 收到委托时间
     */
    private String sdwtsj;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 委托调查书
     */
    private String wtdcs;

    /**
     * 委托文书附件
     */
    private String wtwsfj;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 拟适用社区矫正人员类型
     */
    private String nsysqjzrylx;

    /**
     * 性别
     */
    private String xb;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 居住地地址(省)
     */
    private String jzddzP;

    /**
     * 居住地地址(市)
     */
    private String jzddzC;

    /**
     * 居住地地址
     */
    private String jzddz;

    /**
     * 是否有居住地地址明细
     */
    private String sfyjzddzmx;

    /**
     * 居住地地址明细
     */
    private String jzddzmx;

    /**
     * 户籍是否与居住地相同
     */
    private String hjsfyjzdxt;

    /**
     * 户籍地址(省)
     */
    private String hjdzP;

    /**
     * 户籍地址(市)
     */
    private String hjdzC;

    /**
     * 户籍地址
     */
    private String hjdz;

    /**
     * 是否有户籍地址明细
     */
    private String sfyhjdzmx;

    /**
     * 户籍地址明细
     */
    private String hjdzmx;

    /**
     * 工作单位
     */
    private String hzdw;

    /**
     * 罪名
     */
    private String zm;

    /**
     * 拟适用矫正类别
     */
    private String nsyjzlb;

    /**
     * 是否有原判刑期
     */
    private String sfyypxq;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 原判刑期开始日期
     */
    private String ypxqksrq;

    /**
     * 原判刑期结束日期
     */
    private String ypxqjsrq;

    /**
     * 原判刑罚
     */
    private String ypxf;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 判决机关
     */
    private String pjjg;

    /**
     * 判决日期
     */
    private String pjrq;

    /**
     * 指派单位
     */
    private String zpdwmc;

    /**
     * 指派单位ID
     */
    private String zpdwId;

    /**
     * 指派人
     */
    private String zpr;

    /**
     * 指派时间
     */
    private String zpsj;

    /**
     * 指派备注
     */
    private String zpbz;


    /**
     *     家庭和社会关系
     **/
    private String jthshgx;
    /**
     *     社会危险性、对所居住社区的影响等情况
     **/
    private String shyx;
    /**
     *     拟禁止的事项
     **/
    private String njzdsx;
    /**
     *     犯罪行为的后果和影响
     **/
    private String fzyx;
    /**
     *     居住地村（居）民委员会和被害人意见
     **/
    private String bhryj;
    /**
     *     调查评估意见
     **/
    private String dcpgyj;
    /**
     *     调查人
     **/
    private String dcr;
    /**
     *     调查意见审核人
     **/
    private String dcyjshr;

}
