package com.concise.gen.correctionplacechangeemigration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionplacechangeemigration.entity.CorrectionPlacechangeEmigration;
import com.concise.gen.correctionplacechangeemigration.param.CorrectionPlacechangeEmigrationParam;
import java.util.List;

/**
 * 执行地变更_跨省迁出service接口
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
public interface CorrectionPlacechangeEmigrationService extends IService<CorrectionPlacechangeEmigration> {

    /**
     * 查询执行地变更_跨省迁出
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
    PageResult<CorrectionPlacechangeEmigration> page(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam);

    /**
     * 执行地变更_跨省迁出列表
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
    List<CorrectionPlacechangeEmigration> list(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam);

    /**
     * 添加执行地变更_跨省迁出
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
    void add(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam);

    /**
     * 删除执行地变更_跨省迁出
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
    void delete(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam);

    /**
     * 编辑执行地变更_跨省迁出
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
    void edit(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam);

    /**
     * 查看执行地变更_跨省迁出
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
     CorrectionPlacechangeEmigration detail(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam);
}
