package com.concise.gen.coordinateinvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP6;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateP6ExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateP6Mapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP6Param;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP6Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估协查_审批service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:48:00
 */
@Service
public class CoordinateInvestigateP6ServiceImpl extends ServiceImpl<CoordinateInvestigateP6Mapper, CoordinateInvestigateP6> implements CoordinateInvestigateP6Service {

    @Override
    public PageResult<CoordinateInvestigateP6> page(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        QueryWrapper<CoordinateInvestigateP6> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(coordinateInvestigateP6Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP6Param.getPid())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP6::getPid, coordinateInvestigateP6Param.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP6Param.getZt())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP6::getZt, coordinateInvestigateP6Param.getZt());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateP6> list(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        CoordinateInvestigateP6 coordinateInvestigateP6 = new CoordinateInvestigateP6();
        BeanUtil.copyProperties(coordinateInvestigateP6Param, coordinateInvestigateP6);
        this.save(coordinateInvestigateP6);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        this.removeById(coordinateInvestigateP6Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        CoordinateInvestigateP6 coordinateInvestigateP6 = this.queryCoordinateInvestigateP6(coordinateInvestigateP6Param);
        BeanUtil.copyProperties(coordinateInvestigateP6Param, coordinateInvestigateP6);
        this.updateById(coordinateInvestigateP6);
    }

    @Override
    public CoordinateInvestigateP6 detail(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        return this.queryCoordinateInvestigateP6(coordinateInvestigateP6Param);
    }

    /**
     * 获取调查评估协查_审批
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
    private CoordinateInvestigateP6 queryCoordinateInvestigateP6(CoordinateInvestigateP6Param coordinateInvestigateP6Param) {
        CoordinateInvestigateP6 coordinateInvestigateP6 = this.getById(coordinateInvestigateP6Param.getId());
        if (ObjectUtil.isNull(coordinateInvestigateP6)) {
            throw new ServiceException(CoordinateInvestigateP6ExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateP6;
    }
}
