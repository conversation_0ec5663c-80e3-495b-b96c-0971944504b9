package com.concise.gen.acceptcorrectionfamily.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionfamily.entity.AcceptCorrectionFamily;
import com.concise.gen.acceptcorrectionfamily.enums.AcceptCorrectionFamilyExceptionEnum;
import com.concise.gen.acceptcorrectionfamily.mapper.AcceptCorrectionFamilyMapper;
import com.concise.gen.acceptcorrectionfamily.param.AcceptCorrectionFamilyParam;
import com.concise.gen.acceptcorrectionfamily.service.AcceptCorrectionFamilyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 矫正对象家庭及社会关系信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-08 16:36:09
 */
@Service
public class AcceptCorrectionFamilyServiceImpl extends ServiceImpl<AcceptCorrectionFamilyMapper, AcceptCorrectionFamily> implements AcceptCorrectionFamilyService {

    @Override
    public PageResult<AcceptCorrectionFamily> page(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        QueryWrapper<AcceptCorrectionFamily> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptCorrectionFamilyParam)) {

            queryWrapper.lambda().eq(AcceptCorrectionFamily::getContactId, acceptCorrectionFamilyParam.getContactId());
            // 根据关系 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getGx())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getGx, acceptCorrectionFamilyParam.getGx());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getXm())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getXm, acceptCorrectionFamilyParam.getXm());
            }
            // 根据性别 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getXb())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getXb, acceptCorrectionFamilyParam.getXb());
            }
            // 根据出生日期 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getCsrq())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getCsrq, acceptCorrectionFamilyParam.getCsrq());
            }
            // 根据证件类型 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getLx())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getLx, acceptCorrectionFamilyParam.getLx());
            }
            // 根据证件号码 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getZjhm())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getZjhm, acceptCorrectionFamilyParam.getZjhm());
            }
            // 根据所在单位 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getSzdw())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getSzdw, acceptCorrectionFamilyParam.getSzdw());
            }
            // 根据职务 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getZw())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getZw, acceptCorrectionFamilyParam.getZw());
            }
            // 根据家庭住址 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getJtzz())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getJtzz, acceptCorrectionFamilyParam.getJtzz());
            }
            // 根据联系电话 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionFamilyParam.getLxdh())) {
                queryWrapper.lambda().eq(AcceptCorrectionFamily::getLxdh, acceptCorrectionFamilyParam.getLxdh());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionFamily> list(String contactId) {
        QueryWrapper<AcceptCorrectionFamily> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcceptCorrectionFamily::getContactId, contactId);
        return this.list(queryWrapper);
    }

    @Override
    public void add(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        AcceptCorrectionFamily acceptCorrectionFamily = new AcceptCorrectionFamily();
        BeanUtil.copyProperties(acceptCorrectionFamilyParam, acceptCorrectionFamily);
        this.save(acceptCorrectionFamily);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        this.removeById(acceptCorrectionFamilyParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        AcceptCorrectionFamily acceptCorrectionFamily = this.queryAcceptCorrectionFamily(acceptCorrectionFamilyParam);
        BeanUtil.copyProperties(acceptCorrectionFamilyParam, acceptCorrectionFamily);
        this.updateById(acceptCorrectionFamily);
    }

    @Override
    public AcceptCorrectionFamily detail(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        return this.queryAcceptCorrectionFamily(acceptCorrectionFamilyParam);
    }

    /**
     * 获取矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    private AcceptCorrectionFamily queryAcceptCorrectionFamily(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        AcceptCorrectionFamily acceptCorrectionFamily = this.getById(acceptCorrectionFamilyParam.getId());
        if (ObjectUtil.isNull(acceptCorrectionFamily)) {
            throw new ServiceException(AcceptCorrectionFamilyExceptionEnum.NOT_EXIST);
        }
        return acceptCorrectionFamily;
    }
}
