package com.concise.gen.acceptparoleexecute.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseAcceptEntity;
import lombok.Data;

import java.util.Date;

/**
 * 假释执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
@Data
@TableName("accept_parole_execute")
public class AcceptParoleExecute extends BaseAcceptEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;



    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 法院案件标识
     */
    private String fyajbs;

    /**
     * 案件名称
     */
    private String ajmc;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xb;

    /**
     * 民族
     */
    private String mz;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date csrq;

    /**
     * 犯罪时是否未成年
     */
    private String fzssfwcn;

    /**
     * 未成年
     */
    private String wcn;

    /**
     * 是否有精神病
     */
    private String sfyjsb;

    /**
     * 鉴定机构
     */
    private String jdjg;

    /**
     * 是否有传染病
     */
    private String sfycrb;

    /**
     * 传染病类型
     */
    private String crblx;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 婚姻状况
     */
    private String hyzk;

    /**
     * 职业
     */
    private String zy;

    /**
     * 就业就学情况
     */
    private String jyjxqk;

    /**
     * 现政治面貌
     */
    private String xzzmm;

    /**
     * 原政治面貌
     */
    private String yzzmm;

    /**
     * 原工作单位
     */
    private String ygzdw;

    /**
     * 单位联系电话
     */
    private String dwlxdh;

    /**
     * 个人联系电话
     */
    private String grlxdh;

    /**
     * 国籍
     */
    private String gj;

    /**
     * 有无家庭成员及主要社会关系
     */
    private String ywjtcyjzyshgx;

    /**
     * 照片
     */
    private String zp;

    /**
     * 户籍地是否与居住地相同
     */
    private String hjdsfyjzdxt;

    /**
     * 住所地
     */
    private String zsd;

    /**
     * 住所地详细地址
     */
    private String zsdxxdz;

    /**
     * 户籍所在地
     */
    private String hjszd;

    /**
     * 户籍地址明细
     */
    private String hjdzmx;

    /**
     * 是否有前科
     */
    private String sfyqk;

    /**
     * 是否累犯
     */
    private String sflf;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 调查评估日期
     */
    @Excel(name = "调查评估日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date dcpgrq;

    /**
     * 调查评估意见
     */
    private String dcpgyj;

    /**
     * 生效判决书字号
     */
    private String sxpjszh;

    /**
     * 判决文书生效日期
     */
    @Excel(name = "判决文书生效日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pjwssxrq;

    /**
     * 犯罪类型
     */
    private String fzlx;

    /**
     * 具体罪名
     */
    private String jtzm;

    /**
     * 是否数罪并罚
     */
    private String sfszbf;

    /**
     * 判决刑种
     */
    private String pjxz;

    /**
     * 判决刑期开始日期
     */
    @Excel(name = "判决刑期开始日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pjxqksrq;

    /**
     * 判决刑期结束日期
     */
    @Excel(name = "判决刑期结束日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pjxqjsrq;

    /**
     * 有期徒刑期限
     */
    private String yqtxqx;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 审判机关名称
     */
    private String spjgmc;

    /**
     * 剥夺政治权利年限
     */
    private String bdzzqlnx;

    /**
     * 具体罚款金额
     */
    private String jtfkje;

    /**
     * 驱逐出境
     */
    private String qzcj;

    /**
     * 没收财产
     */
    private String mscc;

    /**
     * 是否“五独”
     */
    private String sfwd;

    /**
     * 是否“五涉”
     */
    private String sfws;

    /**
     * 是否有“四史”
     */
    private String sfyss;

    /**
     * 假释裁定日期
     */
    @Excel(name = "假释裁定日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jscdrq;

    /**
     * 假释考验期
     */
    private String jskyq;

    /**
     * 假释考验期起日
     */
    @Excel(name = "假释考验期起日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jskyqqr;

    /**
     * 假释考验期止日
     */
    @Excel(name = "假释考验期止日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jskyqzr;

    /**
     * 假释裁定书文号
     */
    private String jscdswh;

    /**
     * 假释裁定机关
     */
    private String jscdjg;

    /**
     * 矫正机构
     */
    private String jzjg;

    /**
     * 交付执行日期
     */
    @Excel(name = "交付执行日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jfzxrq;

    /**
     * 执行通知书日期
     */
    @Excel(name = "执行通知书日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date zxtzsrq;

    /**
     * 移交罪犯机关类型
     */
    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */
    private String yjzfjgmc;

}
