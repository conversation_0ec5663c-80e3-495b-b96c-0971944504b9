package com.concise.gen.acceptparoleexecute.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptparoleexecute.entity.AcceptParoleExecute;
import com.concise.gen.acceptparoleexecute.param.AcceptParoleExecuteParam;

import java.util.List;

/**
 * 假释执行接收表service接口
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
public interface AcceptParoleExecuteService extends IService<AcceptParoleExecute> {

    /**
     * 查询假释执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
    PageResult<AcceptParoleExecute> page(AcceptParoleExecuteParam acceptParoleExecuteParam);

    /**
     * 假释执行接收表列表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
    List<AcceptParoleExecute> list(AcceptParoleExecuteParam acceptParoleExecuteParam);

    /**
     * 添加假释执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
    List<AcceptCorrectionDocParam> add(AcceptParoleExecuteParam acceptParoleExecuteParam);

    /**
     * 删除假释执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
    void delete(AcceptParoleExecuteParam acceptParoleExecuteParam);
    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);
    /**
     * 编辑假释执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
    void edit(AcceptParoleExecuteParam acceptParoleExecuteParam);

    /**
     * 查看假释执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
     AcceptParoleExecute detail(AcceptParoleExecuteParam acceptParoleExecuteParam);
}
