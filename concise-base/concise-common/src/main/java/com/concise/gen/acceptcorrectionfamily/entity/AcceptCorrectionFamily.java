package com.concise.gen.acceptcorrectionfamily.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 矫正对象家庭及社会关系信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-08 16:36:09
 */
@Data
@TableName("accept_correction_family")
public class AcceptCorrectionFamily  {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String contactId;
    /**
     * 关系
     * 使用万达字典值 3.25称谓 dictId=SQJZ_CW2
     */
    private String gx;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     * 万达字典值 3.2性别
     * 一体化字典相同
     */
    private String xb;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 证件类型
     * 一体化字典
     */
    private String lx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 所在单位
     */
    private String szdw;

    /**
     * 职务
     */
    private String zw;

    /**
     * 家庭住址
     */
    private String jtzz;

    /**
     * 联系电话
     */
    private String lxdh;

}
