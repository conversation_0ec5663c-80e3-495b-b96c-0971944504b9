package com.concise.gen.chargeinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.chargeinfo.entity.ChargeInfo;
import com.concise.gen.chargeinfo.param.ChargeInfoParam;
import java.util.List;

/**
 * 罪名表service接口
 *
 * <AUTHOR>
 * @date 2023-06-16 15:30:23
 */
public interface ChargeInfoService extends IService<ChargeInfo> {

    /**
     * 查询罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    PageResult<ChargeInfo> page(ChargeInfoParam chargeInfoParam);

    /**
     * 罪名表列表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    List<ChargeInfo> list(ChargeInfoParam chargeInfoParam);

    /**
     * 添加罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    void add(ChargeInfoParam chargeInfoParam);

    /**
     * 删除罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    void delete(ChargeInfoParam chargeInfoParam);

    /**
     * 编辑罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    void edit(ChargeInfoParam chargeInfoParam);

    /**
     * 查看罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
     ChargeInfo detail(ChargeInfoParam chargeInfoParam);


    /**
     * 案由转中文
     * s_case_reason
     * @param codeList 案由code
     * @return String
     */
    String toChinese(List<String> codeList);
}
