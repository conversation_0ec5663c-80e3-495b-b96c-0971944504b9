package com.concise.gen.correctionterminatext.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 解矫协同信息
 *
 * <AUTHOR>
 * @date 2022-06-17 15:08:39
 */
@Data
@TableName("correction_terminate_xt")
public class CorrectionTerminateXt {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 发送时间
     */
    private Date sendTime;
    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    private String sqjzajbh;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 生效判决书字号
     */
    private String sxpjszh;

    /**
     * 判决文书生效日期
     */
    private Date pjwssxrq;

    /**
     * 犯罪类型
     */
    private String fzlx;

    /**
     * 具体罪名
     */
    private String jtzm;

    /**
     * 管制期限
     */
    private String gzqx;

    /**
     * 缓刑考验期限
     */
    private String hxkyqx;

    /**
     * 是否数罪并罚
     */
    private String sfszbf;

    /**
     * 判决刑种
     */
    private String pjxz;

    /**
     * 判决刑期开始日期
     */
    private Date pjxqksrq;

    /**
     * 判决刑期结束日期
     */
    private Date pjxqjsrq;

    /**
     * 有期徒刑期限
     */
    private String yqtxqx;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 审判机关名称
     */
    private String spjgmc;

    /**
     * 剥夺政治权利年限
     */
    private String bdzzqlnx;

    /**
     * 具体罚款金额
     */
    private String jtfkje;

    /**
     * 驱逐出境
     */
    private String qzcj;

    /**
     * 没收财产
     */
    private String mscc;

    /**
     * 是否“五独”
     */
    private String sfwd;

    /**
     * 是否“五涉”
     */
    private String sfws;

    /**
     * 是否有“四史”
     */
    private String sfyss;

    /**
     * 假释裁定日期
     */
    private Date jscdrq;

    /**
     * 假释考验期
     */
    private String jskyq;

    /**
     * 假释考验期起日
     */
    private Date jskyqqr;

    /**
     * 假释考验期止日
     */
    private Date jskyqzr;

    /**
     * 假释裁定书文号
     */
    private String jscdswh;

    /**
     * 假释裁定机关
     */
    private String jscdjg;

    /**
     * 矫正机构
     */
    private String jzjg;
    private String jzjgName;
    private String jzjgId;
    private String jzjgPids;

    /**
     * 交付执行日期
     */
    private Date jfzxrq;

    /**
     * 执行通知书日期
     */
    private Date zxtzsrq;

    /**
     * 移交罪犯机关类型
     */
    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */
    private String yjzfjgmc;

    /**
     * 暂予监外执行决定日期
     */
    private Date zyjwzxjdrq;

    /**
     * 暂予监外执行决定书文号
     */
    private String zyjwzxjdswh;

    /**
     * 暂予监外执行起日
     */
    private Date zyjwzxqr;

    /**
     * 暂予监外执行止日
     */
    private Date zyjwzxzr;

    /**
     * 决定书文号
     */
    private String jdswh;

    /**
     * 撤销假释决定日期
     */
    private Date cxjsjdrq;

    /**
     * 撤销假释原因
     */
    private String cxjsyy;

    /**
     * 撤销假释决定机关
     */
    private String cxjsjdjg;

    /**
     * 撤销缓刑决定日期
     */
    private Date cxhxjdrq;

    /**
     * 撤销缓刑原因
     */
    private String cxhxyy;

    /**
     * 撤销缓刑决定机关
     */
    private String cxhxjdjg;

    /**
     * 收监决定日期
     */
    private Date sjjdrq;

    /**
     * 收监原因
     */
    private String sjyy;

    /**
     * 收监决定机关
     */
    private String sjjdjg;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 决定机关
     */
    private String jdjg;

    /**
     * 司法所
     */
    private String sfs;

    /**
     * 社区矫正开始日期
     */
    private Date sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    private Date sqjzjsrq;

    /**
     * 矫正期限
     */
    private String jzqx;

    /**
     * 社区矫正执行地
     */
    private String sqjzzxd;

    /**
     * 解除社矫/终止社矫类型
     * 1：解除矫正
     * 2：终止社矫
     */
    private String jcsjzzsjlx;

    /**
     * 解除社矫/终止社矫原因
     * 1	社矫期满
     * 2	撤销缓刑
     * 3	撤销假释
     * 4	收监执行
     * 5	矫正对象死亡
     */
    private String jcsjzzsjyy;

    /**
     * 通知书文号
     */
    private String tzswh;

    /**
     * 通知书日期
     */
    private Date tzsrq;

    /**
     * 入矫日期
     */
    private Date rjrq;

    /**
     * 解除社矫/终止社矫日期
     */
    private Date jcsjzzsjrq;

    /**
     * 是否删除
     */
    private String sfsc;

    /**
     * 创建时间
     */
    private Date cjsj;

    /**
     * 更新时间
     */
    private Date gxsj;

    /**
     * 司法局所在区划的检察院代码
     */
    private String jcydm;

    /**
     * 司法局所在区划的法院代码
     */
    private String fydm;

    /**
     * 协同状态
     * 0：待发送
     * 1：协同信息推送成功
     * 2：协同信息推送失败
     */
    private String xtzt;

}
