package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP4Param;
import com.concise.gen.ciInvestigate. service.InvestigateP4Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估_初审小组意见控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:19
 */
@Api(tags = "迁入调查评估_初审小组意见")
@RestController
public class InvestigateP4Controller {

    @Resource
    private InvestigateP4Service investigateP4Service;

    /**
     * 查询迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    @Permission
    @GetMapping("/investigateP4/page")
    @ApiOperation("迁入调查评估_初审小组意见_分页查询")
    @BusinessLog(title = "迁入调查评估_初审小组意见_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP4Param investigateP4Param) {
        return new SuccessResponseData(investigateP4Service.page(investigateP4Param));
    }

    /**
     * 添加迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    @Permission
    @PostMapping("/investigateP4/add")
    @ApiOperation("迁入调查评估_初审小组意见_增加")
    @BusinessLog(title = "迁入调查评估_初审小组意见_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP4Param.add.class) InvestigateP4Param investigateP4Param) {
        investigateP4Service.add(investigateP4Param);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    @Permission
    @PostMapping("/investigateP4/delete")
    @ApiOperation("迁入调查评估_初审小组意见_删除")
    @BusinessLog(title = "迁入调查评估_初审小组意见_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP4Param.delete.class) InvestigateP4Param investigateP4Param) {
        investigateP4Service.delete(investigateP4Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    @Permission
    @PostMapping("/investigateP4/edit")
    @ApiOperation("迁入调查评估_初审小组意见_编辑")
    @BusinessLog(title = "迁入调查评估_初审小组意见_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP4Param.edit.class) InvestigateP4Param investigateP4Param) {
        investigateP4Service.edit(investigateP4Param);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估_初审小组意见
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    @Permission
    @GetMapping("/investigateP4/detail")
    @ApiOperation("迁入调查评估_初审小组意见_查看")
    @BusinessLog(title = "迁入调查评估_初审小组意见_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP4Param.detail.class) InvestigateP4Param investigateP4Param) {
        return new SuccessResponseData(investigateP4Service.detail(investigateP4Param));
    }

    /**
     * 迁入调查评估_初审小组意见列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:19
     */
    @Permission
    @GetMapping("/investigateP4/list")
    @ApiOperation("迁入调查评估_初审小组意见_列表")
    @BusinessLog(title = "迁入调查评估_初审小组意见_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP4Param investigateP4Param) {
        return new SuccessResponseData(investigateP4Service.list(investigateP4Param));
    }

}
