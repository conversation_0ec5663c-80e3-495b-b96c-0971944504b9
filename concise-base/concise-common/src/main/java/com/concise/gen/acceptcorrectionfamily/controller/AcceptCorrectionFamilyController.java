package com.concise.gen.acceptcorrectionfamily. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.acceptcorrectionfamily.param.AcceptCorrectionFamilyParam;
import com.concise.gen.acceptcorrectionfamily.service.AcceptCorrectionFamilyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 矫正对象家庭及社会关系信息接收表控制器
 *
 * <AUTHOR>
 * @date 2022-07-08 16:36:09
 */
@Api(tags = "矫正对象家庭及社会关系信息接收表")
@RestController
public class AcceptCorrectionFamilyController {

    @Resource
    private AcceptCorrectionFamilyService acceptCorrectionFamilyService;

    /**
     * 查询矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    @GetMapping("/acceptCorrectionFamily/page")
    @ApiOperation("矫正对象家庭及社会关系信息接收表_分页查询")
    public ResponseData page(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        return new SuccessResponseData(acceptCorrectionFamilyService.page(acceptCorrectionFamilyParam));
    }

    /**
     * 添加矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    @PostMapping("/acceptCorrectionFamily/add")
    @ApiOperation("矫正对象家庭及社会关系信息接收表_增加")
    @BusinessLog(title = "矫正对象家庭及社会关系信息接收表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AcceptCorrectionFamilyParam.add.class) AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        acceptCorrectionFamilyService.add(acceptCorrectionFamilyParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    @PostMapping("/acceptCorrectionFamily/delete")
    @ApiOperation("矫正对象家庭及社会关系信息接收表_删除")
    @BusinessLog(title = "矫正对象家庭及社会关系信息接收表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AcceptCorrectionFamilyParam.delete.class) AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        acceptCorrectionFamilyService.delete(acceptCorrectionFamilyParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    @PostMapping("/acceptCorrectionFamily/edit")
    @ApiOperation("矫正对象家庭及社会关系信息接收表_编辑")
    @BusinessLog(title = "矫正对象家庭及社会关系信息接收表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AcceptCorrectionFamilyParam.edit.class) AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        acceptCorrectionFamilyService.edit(acceptCorrectionFamilyParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    @GetMapping("/acceptCorrectionFamily/detail")
    @ApiOperation("矫正对象家庭及社会关系信息接收表_查看")
    public ResponseData detail(@Validated(AcceptCorrectionFamilyParam.detail.class) AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        return new SuccessResponseData(acceptCorrectionFamilyService.detail(acceptCorrectionFamilyParam));
    }

    /**
     * 矫正对象家庭及社会关系信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    @GetMapping("/acceptCorrectionFamily/list")
    @ApiOperation("矫正对象家庭及社会关系信息接收表_列表")
    public ResponseData list(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam) {
        return new SuccessResponseData(acceptCorrectionFamilyService.list(acceptCorrectionFamilyParam.getContactId()));
    }

}
