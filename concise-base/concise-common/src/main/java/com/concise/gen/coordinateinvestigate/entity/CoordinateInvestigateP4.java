package com.concise.gen.coordinateinvestigate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
/**
 * 调查评估协查_初审小组意见
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:57
 */
@Data
@TableName("coordinate_investigate_p4")
public class CoordinateInvestigateP4{

    private Date createTime;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 调查步骤id
     */
    private String zt;

    /**
     * 初审人
     */
    private String csr;

    /**
     * 初审时间
     */
    @Excel(name = "初审时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date cssj;

    /**
     * 初审意见
     */
    private String csyj;

    /**
     * 退回理由
     */
    private String thly;

    /**
     * 合议事项
     */
    private String hysx;

    /**
     * 主持人
     */
    private String zcr;

    /**
     * 合议地点
     */
    private String hydd;

    /**
     * 合议时间
     */
    @Excel(name = "合议时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date hysj;

    /**
     * 合议人员(jsonarray)
     */
    private String hyry;

    /**
     * 记录人
     */
    private String jlr;

    /**
     * 合议情况
     */
    private String hyqk;

    /**
     * 合议意见
     */
    private String hyyj;

    /**
     * 负责人
     */
    private String fzr;

    /**
     * 备注
     */
    private String bz;

}
