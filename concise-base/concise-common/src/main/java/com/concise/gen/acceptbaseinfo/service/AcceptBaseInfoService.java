package com.concise.gen.acceptbaseinfo.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptbaseinfo.entity.AcceptBaseInfo;
import com.concise.gen.acceptbaseinfo.param.AcceptBaseInfoParam;
import com.concise.gen.acceptbaseinfo.vo.ExtOrgStatisticsVo;
import com.concise.gen.acceptbaseinfo.vo.IndicatorCollaborationVo;
import com.concise.gen.acceptbaseinfo.vo.StatVo;
import com.concise.gen.acceptbaseinfo.vo.StatisticsVo;

import java.util.List;
import java.util.Set;

/**
 * 交付衔接协同service接口
 *
 * <AUTHOR>
 * @date 2023-02-03 16:46:06
 */
public interface AcceptBaseInfoService extends IService<AcceptBaseInfo> {

    /**
     * 查询交付衔接协同
     *
     * <AUTHOR>
     * @date 2023-02-03 16:46:06
     */
    PageResult<AcceptBaseInfo> page(AcceptBaseInfoParam acceptBaseInfoParam);

    /**
     * 交付衔接协同列表
     *
     * <AUTHOR>
     * @date 2023-02-03 16:46:06
     */
    List<AcceptBaseInfo> list(AcceptBaseInfoParam acceptBaseInfoParam);

    /**
     * 添加交付衔接协同
     *
     * <AUTHOR>
     * @date 2023-02-03 16:46:06
     */
    void add(AcceptBaseInfoParam acceptBaseInfoParam);

    /**
     * 删除交付衔接协同
     *
     * <AUTHOR>
     * @date 2023-02-03 16:46:06
     */
    void delete(AcceptBaseInfoParam acceptBaseInfoParam);

    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);

    /**
     * 编辑交付衔接协同
     *
     * <AUTHOR>
     * @date 2023-02-03 16:46:06
     */
    void edit(AcceptBaseInfoParam acceptBaseInfoParam);
    void setStatus(AcceptBaseInfoParam acceptBaseInfoParam);

    /**
     * 查看交付衔接协同
     *
     * <AUTHOR>
     * @date 2023-02-03 16:46:06
     */
     Object detail(AcceptBaseInfoParam acceptBaseInfoParam);

     String distName(String dist);

    /**
     * 转换字典
     * @param type 类别
     * @param dict 字典
     * @param source 原值
     * @return String 目标值
     */
    String extDictTrans(String type,String dict,String source);

    /**
     * 指标协同
     * @param orgId orgId
     * @return r
     */
    List<IndicatorCollaborationVo> indicatorCollaboration(String orgId);

    /**
     * 步骤
     * @param acceptBaseInfoParam acceptBaseInfoParam
     * @return List
     */
    JSONArray step(AcceptBaseInfoParam acceptBaseInfoParam);

    /**
     * 发送单位统计结果保存
     */
    void insertExtOrgStatistics();

    /**
     * 协同排名
     * @param type 协同单位类别 现在可选项 监狱(61)、法院(20)、看守所(41)
     * @param orgSet 查询机构范围
     * @return List
     */
    List<ExtOrgStatisticsVo> rankExtOrgStatistics(int type, Set<String> orgSet);


    /**
     * 协同趋势
     * @param orgId orgId
     * @param level 层级
     * @param time time
     * @return List
     */
    List<StatisticsVo> trend(String orgId, int level , String time);
    List<StatVo> statistics(String orgId, int level, String start, String end);
    void statisticsExport(String orgId, int level, String start, String end);
}
