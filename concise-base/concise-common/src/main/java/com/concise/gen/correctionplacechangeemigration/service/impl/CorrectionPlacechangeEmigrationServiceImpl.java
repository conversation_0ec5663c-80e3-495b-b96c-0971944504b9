package com.concise.gen.correctionplacechangeemigration.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionplacechangeemigration.entity.CorrectionPlacechangeEmigration;
import com.concise.gen.correctionplacechangeemigration.enums.CorrectionPlacechangeEmigrationExceptionEnum;
import com.concise.gen.correctionplacechangeemigration.mapper.CorrectionPlacechangeEmigrationMapper;
import com.concise.gen.correctionplacechangeemigration.param.CorrectionPlacechangeEmigrationParam;
import com.concise.gen.correctionplacechangeemigration.service.CorrectionPlacechangeEmigrationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 执行地变更_跨省迁出service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
@Service
public class CorrectionPlacechangeEmigrationServiceImpl extends ServiceImpl<CorrectionPlacechangeEmigrationMapper, CorrectionPlacechangeEmigration> implements CorrectionPlacechangeEmigrationService {

    @Resource
    private SysFileInfoService sysFileInfoService;
    @Override
    public PageResult<CorrectionPlacechangeEmigration> page(CorrectionPlacechangeEmigrationParam param) {
        QueryWrapper<CorrectionPlacechangeEmigration> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(CorrectionPlacechangeEmigration::getJzdwId, param.getOrgs());
            }
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CorrectionPlacechangeEmigration::getXm, param.getXm());
            }
            // 根据报到状态
            if (ObjectUtil.isNotEmpty(param.getBdzt())) {
                queryWrapper.lambda().eq(CorrectionPlacechangeEmigration::getBdzt, param.getBdzt());
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(CorrectionPlacechangeEmigration::getSqsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(CorrectionPlacechangeEmigration::getSqsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectionPlacechangeEmigration::getSqsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CorrectionPlacechangeEmigration> list(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam) {
        return this.list();
    }

    @Override
    public void add(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam) {
        CorrectionPlacechangeEmigration correctionPlacechangeEmigration = new CorrectionPlacechangeEmigration();
        BeanUtil.copyProperties(correctionPlacechangeEmigrationParam, correctionPlacechangeEmigration);
        this.save(correctionPlacechangeEmigration);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam) {
        this.removeById(correctionPlacechangeEmigrationParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam) {
        CorrectionPlacechangeEmigration correctionPlacechangeEmigration = this.queryCorrectionPlacechangeEmigration(correctionPlacechangeEmigrationParam);
        BeanUtil.copyProperties(correctionPlacechangeEmigrationParam, correctionPlacechangeEmigration);
        this.updateById(correctionPlacechangeEmigration);
    }

    @Override
    public CorrectionPlacechangeEmigration detail(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam) {
        return this.queryCorrectionPlacechangeEmigration(correctionPlacechangeEmigrationParam);
    }

    /**
     * 获取执行地变更_跨省迁出
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:51
     */
    private CorrectionPlacechangeEmigration queryCorrectionPlacechangeEmigration(CorrectionPlacechangeEmigrationParam correctionPlacechangeEmigrationParam) {
        CorrectionPlacechangeEmigration correctionPlacechangeEmigration = this.getById(correctionPlacechangeEmigrationParam.getId());
        if (ObjectUtil.isNull(correctionPlacechangeEmigration)) {
            throw new ServiceException(CorrectionPlacechangeEmigrationExceptionEnum.NOT_EXIST);
        }
        correctionPlacechangeEmigration.setFileList(sysFileInfoService.getDetailByIds(correctionPlacechangeEmigration.getFile1()));
        return correctionPlacechangeEmigration;
    }
}
