package com.concise.gen.coordinateinvestigatesend.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 调查评估协查_发起跨省委托参数类
 *
 * <AUTHOR>
 * @date 2023-06-13 16:42:02
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateInvestigateSendParam extends BaseParam {

    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 协同状态
     */
    @NotBlank(message = "协同状态不能为空，请检查xtzt参数", groups = {add.class, edit.class})
    private String xtzt;

    /**
     * 委托编号
     */
    @NotBlank(message = "委托编号不能为空，请检查wtbh参数", groups = {add.class, edit.class})
    private String wtbh;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 矫正单位
     */
    @NotBlank(message = "矫正单位不能为空，请检查jzdw参数", groups = {add.class, edit.class})
    private String jzdw;
    private String jzdwId;
    private String zpdwId;
    private Set<String> orgs;

    /**
     * 拟适用社区矫正人员类型
     */
    @NotBlank(message = "拟适用社区矫正人员类型不能为空，请检查nsylx参数", groups = {add.class, edit.class})
    private String nsylx;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 居住地地址
     */
    @NotBlank(message = "居住地地址不能为空，请检查jzddz参数", groups = {add.class, edit.class})
    private String jzddz;

    /**
     * 工作单位
     */
    @NotBlank(message = "工作单位不能为空，请检查gzdw参数", groups = {add.class, edit.class})
    private String gzdw;

    /**
     * 协查意见
     */
    @NotBlank(message = "协查意见不能为空，请检查xcyj参数", groups = {add.class, edit.class})
    private String xcyj;

    /**
     * 受理结果
     */
    @NotBlank(message = "受理结果不能为空，请检查sljg参数", groups = {add.class, edit.class})
    private String sljg;

    /**
     * 申请时间
     */
    @NotNull(message = "申请时间不能为空，请检查sqsj参数", groups = {add.class, edit.class})
    private String sqsj;

    /**
     * 罪名
     */
    @NotBlank(message = "罪名不能为空，请检查zm参数", groups = {add.class, edit.class})
    private String zm;

    /**
     * 原判刑期
     */
    @NotBlank(message = "原判刑期不能为空，请检查ypxq参数", groups = {add.class, edit.class})
    private String ypxq;

    /**
     * 原判刑期开始日期
     */
    @NotNull(message = "原判刑期开始日期不能为空，请检查ypxqksrq参数", groups = {add.class, edit.class})
    private String ypxqksrq;

    /**
     * 原判刑期结束日期
     */
    @NotNull(message = "原判刑期结束日期不能为空，请检查ypxqjsrq参数", groups = {add.class, edit.class})
    private String ypxqjsrq;

    /**
     * 原判刑罚
     */
    @NotBlank(message = "原判刑罚不能为空，请检查ypxf参数", groups = {add.class, edit.class})
    private String ypxf;

    /**
     * 附加刑
     */
    @NotBlank(message = "附加刑不能为空，请检查fjx参数", groups = {add.class, edit.class})
    private String fjx;

    /**
     * 判决机关
     */
    @NotBlank(message = "判决机关不能为空，请检查pjjg参数", groups = {add.class, edit.class})
    private String pjjg;

    /**
     * 判决日期
     */
    @NotBlank(message = "判决日期不能为空，请检查pjrq参数", groups = {add.class, edit.class})
    private String pjrq;

    /**
     * 委托调查书(附件)
     */
    @NotBlank(message = "委托调查书(附件)不能为空，请检查wtdcs参数", groups = {add.class, edit.class})
    private String wtdcs;

    /**
     * 委托省(市)
     */
    @NotBlank(message = "委托省(市)不能为空，请检查wtss参数", groups = {add.class, edit.class})
    private String wtss;

    /**
     * 委托协查单位
     */
    @NotBlank(message = "委托协查单位不能为空，请检查wtxcdw参数", groups = {add.class, edit.class})
    private String wtxcdw;

    /**
     * 建议协查完成返回期限
     */
    @NotNull(message = "建议协查完成返回期限不能为空，请检查jyfhqx参数", groups = {add.class, edit.class})
    private String jyfhqx;

}
