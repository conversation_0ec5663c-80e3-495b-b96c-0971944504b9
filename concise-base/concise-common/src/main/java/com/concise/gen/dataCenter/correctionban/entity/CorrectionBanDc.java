package com.concise.gen.dataCenter.correctionban.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;

/**
 * 禁止令
 *
 * <AUTHOR>
 * @date 2023-09-14 13:47:51
 */
@Data
@TableName("correction_ban")
public class CorrectionBanDc{

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 社区矫正人员标识
     */
    private String pid;

    /**
     * 禁止期限开始日期
     */
    private Date jzqxksrq;

    /**
     * 禁止期限结束日期
     */
    private Date jzqxjsrq;

    /**
     * 禁止令类型
     */
    private String jzllx;

    /**
     * 禁止令类型name
     */
    private String jzllxName;

    /**
     * 联系特定区域坐标
     */
    private String tdqyzb;

    /**
     * 是否删除（0：未删除，1删除）
     */
    private Integer delFlag;

}
