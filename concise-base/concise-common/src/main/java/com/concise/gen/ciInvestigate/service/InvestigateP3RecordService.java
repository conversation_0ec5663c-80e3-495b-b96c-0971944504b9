package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP3Record;
import com.concise.gen.ciInvestigate.param.InvestigateP3RecordParam;
import java.util.List;

/**
 * 迁入调查评估_调查_记录service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:18
 */
public interface InvestigateP3RecordService extends IService<InvestigateP3Record> {

    /**
     * 查询迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    PageResult<InvestigateP3Record> page(InvestigateP3RecordParam investigateP3RecordParam);

    /**
     * 迁入调查评估_调查_记录列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    List<InvestigateP3Record> list(InvestigateP3RecordParam investigateP3RecordParam);

    /**
     * 添加迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    void add(InvestigateP3RecordParam investigateP3RecordParam);

    /**
     * 删除迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    void delete(InvestigateP3RecordParam investigateP3RecordParam);

    /**
     * 编辑迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    void edit(InvestigateP3RecordParam investigateP3RecordParam);

    /**
     * 查看迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
     InvestigateP3Record detail(InvestigateP3RecordParam investigateP3RecordParam);
}
