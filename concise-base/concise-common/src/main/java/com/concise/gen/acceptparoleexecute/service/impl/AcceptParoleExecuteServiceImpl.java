package com.concise.gen.acceptparoleexecute.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionaccomplice.service.AcceptCorrectionAccompliceService;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptcorrectionfamily.service.AcceptCorrectionFamilyService;
import com.concise.gen.acceptcorrectionforbid.service.AcceptCorrectionForbidService;
import com.concise.gen.acceptparoleexecute.entity.AcceptParoleExecute;
import com.concise.gen.acceptparoleexecute.enums.AcceptParoleExecuteExceptionEnum;
import com.concise.gen.acceptparoleexecute.mapper.AcceptParoleExecuteMapper;
import com.concise.gen.acceptparoleexecute.param.AcceptParoleExecuteParam;
import com.concise.gen.acceptparoleexecute.service.AcceptParoleExecuteService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 假释执行接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-08-01 10:16:32
 */
@Service
public class AcceptParoleExecuteServiceImpl extends ServiceImpl<AcceptParoleExecuteMapper, AcceptParoleExecute> implements AcceptParoleExecuteService {

    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private OrgCommonService orgCommonService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private AcceptCorrectionAccompliceService acceptCorrectionAccompliceService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private AcceptCorrectionFamilyService acceptCorrectionFamilyService;
    @Resource
    private AcceptCorrectionForbidService acceptCorrectionForbidService;
    @Override
    public PageResult<AcceptParoleExecute> page(AcceptParoleExecuteParam acceptParoleExecuteParam) {
        QueryWrapper<AcceptParoleExecute> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptParoleExecuteParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(acceptParoleExecuteParam.getTaskId())) {
                queryWrapper.lambda().eq(AcceptParoleExecute::getTaskId, acceptParoleExecuteParam.getTaskId());
            }
            // 根据数据状态 查询
            if (ObjectUtil.isNotEmpty(acceptParoleExecuteParam.getZt())) {
                queryWrapper.lambda().eq(AcceptParoleExecute::getZt, acceptParoleExecuteParam.getZt());
            }
            // 根据数据来源(机构类型) 查询
            if (ObjectUtil.isNotEmpty(acceptParoleExecuteParam.getSjlylx())) {
                queryWrapper.lambda().eq(AcceptParoleExecute::getSjlylx, acceptParoleExecuteParam.getSjlylx());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(acceptParoleExecuteParam.getJsdw())) {
                queryWrapper.lambda().and(i -> i.eq(AcceptParoleExecute::getJsdwId, acceptParoleExecuteParam.getJsdw()).or().like(AcceptParoleExecute::getJsdwPids, acceptParoleExecuteParam.getJsdw()));
            }
            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(acceptParoleExecuteParam.getTyfh())) {
                queryWrapper.lambda().eq(AcceptParoleExecute::getTyfh, acceptParoleExecuteParam.getTyfh());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(acceptParoleExecuteParam.getXm())) {
                queryWrapper.lambda().like(AcceptParoleExecute::getXm, acceptParoleExecuteParam.getXm());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(acceptParoleExecuteParam.getSearchBeginTime(), acceptParoleExecuteParam.getSearchEndTime())) {
                queryWrapper.lambda().ge(AcceptParoleExecute::getSdsj, acceptParoleExecuteParam.getSearchBeginTime());
                queryWrapper.lambda().le(AcceptParoleExecute::getSdsj, acceptParoleExecuteParam.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(AcceptParoleExecute::getSdsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptParoleExecute> list(AcceptParoleExecuteParam acceptParoleExecuteParam) {
        return this.list();
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptParoleExecuteParam param) {
        OrgCommon org = orgCommonService.getOrgByCode(param.getJsdw());
        param.setJsdwmc(org.getName());
        param.setJsdwId(org.getId());
        param.setJsdwPids(org.getPids());
        param.setTsdwmc(extOrgInfoService.getExtOrgName(param.getSjlylx(),param.getTsdw()));

        AcceptParoleExecute acceptParoleExecute = new AcceptParoleExecute();
        BeanUtil.copyProperties(param, acceptParoleExecute);
        param.getAccompliceList().forEach(accomplice -> acceptCorrectionAccompliceService.add(accomplice));
        param.getFamilyList().forEach(family -> acceptCorrectionFamilyService.add(family));
        param.getForbidList().forEach(forbid -> acceptCorrectionForbidService.add(forbid));
        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));
        this.save(acceptParoleExecute);
        return param.getDocList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptParoleExecuteParam acceptParoleExecuteParam) {
        this.removeById(acceptParoleExecuteParam.getId());
    }

    @Override
    public void delete(String taskId) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptParoleExecuteParam acceptParoleExecuteParam) {
        AcceptParoleExecute acceptParoleExecute = this.queryAcceptParoleExecute(acceptParoleExecuteParam);
        BeanUtil.copyProperties(acceptParoleExecuteParam, acceptParoleExecute);
        this.updateById(acceptParoleExecute);
    }

    @Override
    public AcceptParoleExecute detail(AcceptParoleExecuteParam acceptParoleExecuteParam) {
        return this.queryAcceptParoleExecute(acceptParoleExecuteParam);
    }

    /**
     * 获取假释执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 10:16:32
     */
    private AcceptParoleExecute queryAcceptParoleExecute(AcceptParoleExecuteParam acceptParoleExecuteParam) {
        AcceptParoleExecute acceptParoleExecute = this.getById(acceptParoleExecuteParam.getId());
        if (ObjectUtil.isNull(acceptParoleExecute)) {
            throw new ServiceException(AcceptParoleExecuteExceptionEnum.NOT_EXIST);
        }
        return acceptParoleExecute;
    }
}
