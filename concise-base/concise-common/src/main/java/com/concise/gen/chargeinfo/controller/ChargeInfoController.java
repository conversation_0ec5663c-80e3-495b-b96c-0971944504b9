package com.concise.gen.chargeinfo. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.chargeinfo.param.ChargeInfoParam;
import com.concise.gen.chargeinfo.service.ChargeInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 罪名表控制器
 *
 * <AUTHOR>
 * @date 2023-06-16 15:30:23
 */
@Api(tags = "罪名表")
@RestController
public class ChargeInfoController {

    @Resource
    private ChargeInfoService chargeInfoService;

    /**
     * 添加罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    @PostMapping("/chargeInfo/add")
    @BusinessLog(title = "罪名表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody ChargeInfoParam chargeInfoParam) {
        chargeInfoService.add(chargeInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 删除罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    @PostMapping("/chargeInfo/delete")
    @BusinessLog(title = "罪名表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody ChargeInfoParam chargeInfoParam) {
        chargeInfoService.delete(chargeInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    @PostMapping("/chargeInfo/edit")
    @BusinessLog(title = "罪名表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody ChargeInfoParam chargeInfoParam) {
        chargeInfoService.edit(chargeInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 查看罪名表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    @GetMapping("/chargeInfo/detail")
    public ResponseData detail(ChargeInfoParam chargeInfoParam) {
        return new SuccessResponseData(chargeInfoService.detail(chargeInfoParam));
    }

    /**
     * 罪名表列表
     *
     * <AUTHOR>
     * @date 2023-06-16 15:30:23
     */
    @GetMapping("/chargeInfo/list")
    @ApiOperation("罪名表_列表")
    public ResponseData list(ChargeInfoParam chargeInfoParam) {
        return new SuccessResponseData(chargeInfoService.list(chargeInfoParam));
    }

}
