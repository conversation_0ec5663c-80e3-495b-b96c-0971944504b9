package com.concise.gen.acceptprisonexecute.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 收监执行接收表参数类
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptPrisonExecuteParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    private List<AcceptCorrectionDocParam> docList;
    /**
     *
     */

    private String taskId;

    /**
     * 数据状态
     */

    private String zt;

    /**
     * 数据来源(机构类型)
     */

    private String sjlylx;

    /**
     * 接收单位
     */

    private String jsdw;

    /**
     * 接收单位名称
     */

    private String jsdwmc;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 推送单位
     */

    private String tsdw;

    /**
     * 推送单位名称
     */

    private String tsdwmc;

    /**
     * 送达时间
     */

    private String sdsj;

    /**
     * 统一赋号
     */

    private String tyfh;

    /**
     * 社区矫正案件编号
     */

    private String sqjzajbh;

    /**
     * （法院｜监狱）案件标识
     */

    private String ajbs;

    /**
     * 罪犯编号
     */

    private String zfbh;

    /**
     * 姓名
     */

    private String xm;

    /**
     * 曾用名
     */

    private String cym;

    /**
     * 性别
     */

    private String xb;

    /**
     * 证件类型
     */

    private String zjlx;

    /**
     * 证件号码
     */

    private String zjhm;

    /**
     * 决定书文号
     */

    private String jdswh;

    /**
     * 是否决定收监
     */

    private String sfjdsj;

    /**
     * （不）收监决定日期
     */

    private String sjjdrq;

    /**
     * （不）收监原因
     */

    private String sjyy;

    /**
     * （不）收监决定机关
     */

    private String sjjdjg;

}
