package com.concise.gen.investigationotheruser.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import java.util.Date;
import java.util.Date;

/**
 * 调查评估-其他用户
 *
 * <AUTHOR>
 * @date 2025-08-01 11:02:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("investigation_other_user")
public class InvestigationOtherUser extends BaseEntity {

    /**  id */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**  姓名 */
    private String name;

    /**  手机号 */
    private String phone;

}
