<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateMapper">


    <select id="getAmountByDate" resultType="com.concise.gen.acceptbaseinfo.vo.StatisticsVo">
        SELECT
        COUNT(*) AS amount,
        a.type
        FROM
        ( ( SELECT JZDDZ_P AS type FROM coordinate_investigate
        <if test="date!=null and date!=''">
            WHERE sdwtsj > #{date}
        </if>
        ) UNION ALL ( SELECT QCSS AS type FROM correction_placechange_immigration
        <if test="date!=null and date!=''">
            WHERE SQSJ > #{date}
        </if>
        ) ) a
        WHERE
        a.type IS NOT NULL
        GROUP BY
        a.type
    </select>
    <select id="recentCollaboration" resultType="com.concise.gen.acceptbaseinfo.vo.RecentCollaborationVo">
        ( SELECT '调查评估协同' AS type, xm AS NAME, HJDZ_P AS fqss, JZDDZ_P AS jsss, zt AS zt FROM coordinate_investigate ) UNION
        ( SELECT '外出协助监管' AS type, xm AS NAME, jzdw AS fqss, wcmdd AS jsss, zt AS zt FROM coordinate_supervise_submit ) UNION
        ( SELECT '跨省执行地变更' AS type, xm AS NAME, JZDDZ_P AS fqss, qcss AS jsss, 1 AS zt FROM correction_placechange_immigration )
    </select>
    <select id="monthChange" resultType="com.concise.gen.acceptbaseinfo.vo.StatisticsVo">
        SELECT
            y,
            m,
            SUM( amount ) AS amount
        FROM
            ((
                 SELECT YEAR
                     ( sdwtsj ) AS y,
                     MONTH ( sdwtsj ) AS m,
                     COUNT(*) AS amount
                 FROM
                     coordinate_investigate
                 GROUP BY
                     YEAR ( sdwtsj ),
                     MONTH ( sdwtsj )) UNION ALL
             (
                 SELECT YEAR
                     ( WCSQSJ ) AS y,
                     MONTH ( WCSQSJ ) AS m,
                     COUNT(*) AS amount
                 FROM
                     coordinate_supervise_submit
                 GROUP BY
                     YEAR ( WCSQSJ ),
                     MONTH ( WCSQSJ )) UNION ALL
             (
                 SELECT YEAR
                     ( SQSJ ) AS y,
                     MONTH ( SQSJ ) AS m,
                     COUNT(*) AS amount
                 FROM
                     correction_placechange_immigration
                 GROUP BY
                     YEAR ( SQSJ ),
                     MONTH ( SQSJ ))) a
        GROUP BY
            y,
            m
        ORDER BY
            y DESC,
            m DESC
    </select>
    <select id="collaborativeInstitutions" resultType="com.concise.gen.acceptbaseinfo.vo.StatisticsVo">
select * from coordinate_investigate where id = '1' limit 1
    </select>
</mapper>
