package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP5Param;
import com.concise.gen.ciInvestigate. service.InvestigateP5Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估_初审集体评议控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:21
 */
@Api(tags = "迁入调查评估_初审集体评议")
@RestController
public class InvestigateP5Controller {

    @Resource
    private InvestigateP5Service investigateP5Service;

    /**
     * 查询迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    @Permission
    @GetMapping("/investigateP5/page")
    @ApiOperation("迁入调查评估_初审集体评议_分页查询")
    @BusinessLog(title = "迁入调查评估_初审集体评议_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP5Param investigateP5Param) {
        return new SuccessResponseData(investigateP5Service.page(investigateP5Param));
    }

    /**
     * 添加迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    @Permission
    @PostMapping("/investigateP5/add")
    @ApiOperation("迁入调查评估_初审集体评议_增加")
    @BusinessLog(title = "迁入调查评估_初审集体评议_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP5Param.add.class) InvestigateP5Param investigateP5Param) {
        investigateP5Service.add(investigateP5Param);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    @Permission
    @PostMapping("/investigateP5/delete")
    @ApiOperation("迁入调查评估_初审集体评议_删除")
    @BusinessLog(title = "迁入调查评估_初审集体评议_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP5Param.delete.class) InvestigateP5Param investigateP5Param) {
        investigateP5Service.delete(investigateP5Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    @Permission
    @PostMapping("/investigateP5/edit")
    @ApiOperation("迁入调查评估_初审集体评议_编辑")
    @BusinessLog(title = "迁入调查评估_初审集体评议_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP5Param.edit.class) InvestigateP5Param investigateP5Param) {
        investigateP5Service.edit(investigateP5Param);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    @Permission
    @GetMapping("/investigateP5/detail")
    @ApiOperation("迁入调查评估_初审集体评议_查看")
    @BusinessLog(title = "迁入调查评估_初审集体评议_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP5Param.detail.class) InvestigateP5Param investigateP5Param) {
        return new SuccessResponseData(investigateP5Service.detail(investigateP5Param));
    }

    /**
     * 迁入调查评估_初审集体评议列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    @Permission
    @GetMapping("/investigateP5/list")
    @ApiOperation("迁入调查评估_初审集体评议_列表")
    @BusinessLog(title = "迁入调查评估_初审集体评议_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP5Param investigateP5Param) {
        return new SuccessResponseData(investigateP5Service.list(investigateP5Param));
    }

}
