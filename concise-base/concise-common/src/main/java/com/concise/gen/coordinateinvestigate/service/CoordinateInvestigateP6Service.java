package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP6;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP6Param;

import java.util.List;

/**
 * 调查评估协查_审批service接口
 *
 * <AUTHOR>
 * @date 2023-06-12 20:48:00
 */
public interface CoordinateInvestigateP6Service extends IService<CoordinateInvestigateP6> {

    /**
     * 查询调查评估协查_审批
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
    PageResult<CoordinateInvestigateP6> page(CoordinateInvestigateP6Param coordinateInvestigateP6Param);

    /**
     * 调查评估协查_审批列表
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
    List<CoordinateInvestigateP6> list(CoordinateInvestigateP6Param coordinateInvestigateP6Param);

    /**
     * 添加调查评估协查_审批
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
    void add(CoordinateInvestigateP6Param coordinateInvestigateP6Param);

    /**
     * 删除调查评估协查_审批
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
    void delete(CoordinateInvestigateP6Param coordinateInvestigateP6Param);

    /**
     * 编辑调查评估协查_审批
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
    void edit(CoordinateInvestigateP6Param coordinateInvestigateP6Param);

    /**
     * 查看调查评估协查_审批
     *
     * <AUTHOR>
     * @date 2023-06-12 20:48:00
     */
     CoordinateInvestigateP6 detail(CoordinateInvestigateP6Param coordinateInvestigateP6Param);
}
