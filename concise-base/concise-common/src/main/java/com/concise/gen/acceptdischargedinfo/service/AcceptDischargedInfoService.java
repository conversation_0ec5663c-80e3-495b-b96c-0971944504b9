package com.concise.gen.acceptdischargedinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptdischargedinfo.entity.AcceptDischargedInfo;
import com.concise.gen.acceptdischargedinfo.param.AcceptDischargedInfoParam;

import java.util.List;

/**
 * 释放信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
public interface AcceptDischargedInfoService extends IService<AcceptDischargedInfo> {

    /**
     * 查询释放信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
    PageResult<AcceptDischargedInfo> page(AcceptDischargedInfoParam acceptDischargedInfoParam);

    /**
     * 释放信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
    List<AcceptDischargedInfo> list(AcceptDischargedInfoParam acceptDischargedInfoParam);

    /**
     * 添加释放信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
    List<AcceptCorrectionDocParam> add(AcceptDischargedInfoParam acceptDischargedInfoParam);

    /**
     * 删除释放信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
    void delete(AcceptDischargedInfoParam acceptDischargedInfoParam);
    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);
    /**
     * 编辑释放信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
    void edit(AcceptDischargedInfoParam acceptDischargedInfoParam);

    /**
     * 查看释放信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
     AcceptDischargedInfo detail(AcceptDischargedInfoParam acceptDischargedInfoParam);
}
