package com.concise.gen.acceptcorrectionforbid.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionforbid.entity.AcceptCorrectionForbid;
import com.concise.gen.acceptcorrectionforbid.enums.AcceptCorrectionForbidExceptionEnum;
import com.concise.gen.acceptcorrectionforbid.mapper.AcceptCorrectionForbidMapper;
import com.concise.gen.acceptcorrectionforbid.param.AcceptCorrectionForbidParam;
import com.concise.gen.acceptcorrectionforbid.service.AcceptCorrectionForbidService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 矫正对象禁止令信息信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:19
 */
@Service
public class AcceptCorrectionForbidServiceImpl extends ServiceImpl<AcceptCorrectionForbidMapper, AcceptCorrectionForbid> implements AcceptCorrectionForbidService {

    @Override
    public PageResult<AcceptCorrectionForbid> page(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        QueryWrapper<AcceptCorrectionForbid> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptCorrectionForbidParam)) {

            queryWrapper.lambda().eq(AcceptCorrectionForbid::getContactId, acceptCorrectionForbidParam.getContactId());
            // 根据禁止令类型 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionForbidParam.getJzllx())) {
                queryWrapper.lambda().eq(AcceptCorrectionForbid::getJzllx, acceptCorrectionForbidParam.getJzllx());
            }
            // 根据禁止令内容 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionForbidParam.getJzlnr())) {
                queryWrapper.lambda().eq(AcceptCorrectionForbid::getJzlnr, acceptCorrectionForbidParam.getJzlnr());
            }
            // 根据禁止期限开始日期 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionForbidParam.getJzqxksrq())) {
                queryWrapper.lambda().eq(AcceptCorrectionForbid::getJzqxksrq, acceptCorrectionForbidParam.getJzqxksrq());
            }
            // 根据禁止期限结束日期 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionForbidParam.getJzqxjsrq())) {
                queryWrapper.lambda().eq(AcceptCorrectionForbid::getJzqxjsrq, acceptCorrectionForbidParam.getJzqxjsrq());
            }
            // 根据是否被宣告禁止令 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionForbidParam.getSfbxgjzl())) {
                queryWrapper.lambda().eq(AcceptCorrectionForbid::getSfbxgjzl, acceptCorrectionForbidParam.getSfbxgjzl());
            }
            // 根据特定区域坐标 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionForbidParam.getTdqyzb())) {
                queryWrapper.lambda().eq(AcceptCorrectionForbid::getTdqyzb, acceptCorrectionForbidParam.getTdqyzb());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionForbid> list(String contactId) {
        QueryWrapper<AcceptCorrectionForbid> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcceptCorrectionForbid::getContactId, contactId);
        return this.list(queryWrapper);
    }

    @Override
    public void add(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        AcceptCorrectionForbid acceptCorrectionForbid = new AcceptCorrectionForbid();
        BeanUtil.copyProperties(acceptCorrectionForbidParam, acceptCorrectionForbid);
        this.save(acceptCorrectionForbid);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        this.removeById(acceptCorrectionForbidParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        AcceptCorrectionForbid acceptCorrectionForbid = this.queryAcceptCorrectionForbid(acceptCorrectionForbidParam);
        BeanUtil.copyProperties(acceptCorrectionForbidParam, acceptCorrectionForbid);
        this.updateById(acceptCorrectionForbid);
    }

    @Override
    public AcceptCorrectionForbid detail(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        return this.queryAcceptCorrectionForbid(acceptCorrectionForbidParam);
    }

    /**
     * 获取矫正对象禁止令信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:19
     */
    private AcceptCorrectionForbid queryAcceptCorrectionForbid(AcceptCorrectionForbidParam acceptCorrectionForbidParam) {
        AcceptCorrectionForbid acceptCorrectionForbid = this.getById(acceptCorrectionForbidParam.getId());
        if (ObjectUtil.isNull(acceptCorrectionForbid)) {
            throw new ServiceException(AcceptCorrectionForbidExceptionEnum.NOT_EXIST);
        }
        return acceptCorrectionForbid;
    }
}
