package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP6;
import com.concise.gen.ciInvestigate.enums.InvestigateP6ExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP6Mapper;
import com.concise.gen.ciInvestigate.param.InvestigateP6Param;
import com.concise.gen.ciInvestigate.service.InvestigateP6Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 迁入调查评估_审批service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:23
 */
@Service
public class InvestigateP6ServiceImpl extends ServiceImpl<InvestigateP6Mapper, InvestigateP6> implements InvestigateP6Service {

    @Override
    public PageResult<InvestigateP6> page(InvestigateP6Param investigateP6Param) {
        QueryWrapper<InvestigateP6> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP6Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getPid())) {
                queryWrapper.lambda().eq(InvestigateP6::getPid, investigateP6Param.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getZt())) {
                queryWrapper.lambda().eq(InvestigateP6::getZt, investigateP6Param.getZt());
            }
            // 根据审批人 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getSpr())) {
                queryWrapper.lambda().eq(InvestigateP6::getSpr, investigateP6Param.getSpr());
            }
            // 根据审批时间 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getSpsj())) {
                queryWrapper.lambda().eq(InvestigateP6::getSpsj, investigateP6Param.getSpsj());
            }
            // 根据审批意见 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getSpyj())) {
                queryWrapper.lambda().eq(InvestigateP6::getSpyj, investigateP6Param.getSpyj());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getThly())) {
                queryWrapper.lambda().eq(InvestigateP6::getThly, investigateP6Param.getThly());
            }
            // 根据评估意见 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getPgyj())) {
                queryWrapper.lambda().eq(InvestigateP6::getPgyj, investigateP6Param.getPgyj());
            }
            // 根据调查结束时间 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getPyshsj())) {
                queryWrapper.lambda().eq(InvestigateP6::getPyshsj, investigateP6Param.getPyshsj());
            }
            // 根据调查评估意见 查询
            if (ObjectUtil.isNotEmpty(investigateP6Param.getDcpgyj())) {
                queryWrapper.lambda().eq(InvestigateP6::getDcpgyj, investigateP6Param.getDcpgyj());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP6> list(InvestigateP6Param investigateP6Param) {
        return this.list();
    }

    @Override
    public void add(InvestigateP6Param investigateP6Param) {
        InvestigateP6 investigateP6 = new InvestigateP6();
        BeanUtil.copyProperties(investigateP6Param, investigateP6);
        this.save(investigateP6);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP6Param investigateP6Param) {
        this.removeById(investigateP6Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP6Param investigateP6Param) {
        InvestigateP6 investigateP6 = this.queryInvestigateP6(investigateP6Param);
        BeanUtil.copyProperties(investigateP6Param, investigateP6);
        this.updateById(investigateP6);
    }

    @Override
    public InvestigateP6 detail(InvestigateP6Param investigateP6Param) {
        return this.queryInvestigateP6(investigateP6Param);
    }

    /**
     * 获取迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    private InvestigateP6 queryInvestigateP6(InvestigateP6Param investigateP6Param) {
        InvestigateP6 investigateP6 = this.getById(investigateP6Param.getId());
        if (ObjectUtil.isNull(investigateP6)) {
            throw new ServiceException(InvestigateP6ExceptionEnum.NOT_EXIST);
        }
        return investigateP6;
    }
}
