package com.concise.gen.coordinateinvestigatesend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigatesend.entity.CoordinateInvestigateSend;
import com.concise.gen.coordinateinvestigatesend.param.CoordinateInvestigateSendParam;
import java.util.List;

/**
 * 调查评估协查_发起跨省委托service接口
 *
 * <AUTHOR>
 * @date 2023-06-13 16:42:02
 */
public interface CoordinateInvestigateSendService extends IService<CoordinateInvestigateSend> {

    /**
     * 查询调查评估协查_发起跨省委托
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
    PageResult<CoordinateInvestigateSend> page(CoordinateInvestigateSendParam coordinateInvestigateSendParam);

    /**
     * 调查评估协查_发起跨省委托列表
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
    List<CoordinateInvestigateSend> list(CoordinateInvestigateSendParam coordinateInvestigateSendParam);

    /**
     * 添加调查评估协查_发起跨省委托
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
    void add(CoordinateInvestigateSendParam coordinateInvestigateSendParam);

    /**
     * 删除调查评估协查_发起跨省委托
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
    void delete(CoordinateInvestigateSendParam coordinateInvestigateSendParam);

    /**
     * 编辑调查评估协查_发起跨省委托
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
    void edit(CoordinateInvestigateSendParam coordinateInvestigateSendParam);

    /**
     * 查看调查评估协查_发起跨省委托
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
     CoordinateInvestigateSend detail(CoordinateInvestigateSendParam coordinateInvestigateSendParam);
}
