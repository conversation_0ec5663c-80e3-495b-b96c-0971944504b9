package com.concise.gen.acceptreturninfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptreturninfo.entity.AcceptReturnInfo;
import com.concise.gen.acceptreturninfo.param.AcceptReturnInfoParam;

import java.util.Date;
import java.util.List;

/**
 * 数据质检记录表service接口
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:07
 */
public interface AcceptReturnInfoService extends IService<AcceptReturnInfo> {

    /**
     * 查询数据质检记录表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:07
     */
    PageResult<AcceptReturnInfo> page(AcceptReturnInfoParam acceptReturnInfoParam);

    /**
     * 查看数据质检记录表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:07
     */
     AcceptReturnInfo detail(AcceptReturnInfoParam acceptReturnInfoParam);

     Boolean check(Date sdsj, List<AcceptCorrectionDocParam> newDocList, List<AcceptCorrectionDoc> oldDocList);
     Boolean check1(Date sdsj, List<AcceptCorrectionDoc> newDocList, List<AcceptCorrectionDoc> oldDocList);
     void save(String xtbh, String id, String tyfh, Date startTime, Date sdsj, String xm, String jsdw, String jsdwId, String jsdwPids, String jsdwmc);
}
