package com.concise.gen.acceptcorrectiondoc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;

import java.util.List;

/**
 * 矫正对象法律文书信息信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
 */
public interface AcceptCorrectionDocMapper extends BaseMapper<AcceptCorrectionDoc> {
    /**
     * 查找ids
     * @param contactId
     * @return
     */
    List<String> findIds(String contactId);
}
