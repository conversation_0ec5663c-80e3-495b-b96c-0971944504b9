package com.concise.gen.coordinateinvestigate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
/**
 * 调查评估协查_初审集体评议
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:59
 */
@Data
@TableName("coordinate_investigate_p5")
public class CoordinateInvestigateP5{

    private Date createTime;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 调查步骤id
     */
    private String zt;

    /**
     * 初审人
     */
    private String csr;

    /**
     * 初审时间
     */
    @Excel(name = "初审时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date cssj;

    /**
     * 初审意见
     */
    private String csyj;

    /**
     * 退回理由
     */
    private String thly;

    /**
     * 评议审核事项
     */
    private String pyshsx;

    /**
     * 主持人
     */
    private String zcr;

    /**
     * 评议审核地点
     */
    private String pyshdd;

    /**
     * 评议审核时间
     */
    @Excel(name = "评议审核时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pyshsj;

    /**
     * 评议审核人员(jsonarray)
     */
    private String pyshry;

    /**
     * 记录人
     */
    private String jlr;

    /**
     * 评议审核情况
     */
    private String pyshqk;

    /**
     * 评议审核意见
     */
    private String pyshyj;

    /**
     * 负责人
     */
    private String fzr;

    /**
     * 备注
     */
    private String bz;

}
