package com.concise.gen.acceptchargesinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 具体罪名信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:09
 */
@Data
@TableName("accept_charges_info")
public class AcceptChargesInfo{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String contactId;

    /**
     * 罪名(编码)
     */
    private String zm;
    /**
     * 罪名(中文值)
     */
    private String zmName;

    /**
     * 犯罪类型(编码)
     */
    private String fzlx;

    /**
     * 犯罪类型(中文值)
     */
    private String fzlxName;

    /**
     * 具体罪名
     */
    private String jtzm;

    /**
     * 是否主罪
     */
    private String sfzz;

}
