<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.chargeinfo.mapper.ChargeInfoMapper">

    <select id="getChargeInfoList" resultType="com.concise.gen.chargeinfo.entity.ChargeInfo">
        select *
        from s_charge_info
        <where>
            <if test="param1 == null">
                and shift = 0
            </if>
            <if test="param1 != null">
                and left(charge_code,length(charge_code)-shift) = #{param1}
            </if>
            <if test="param2 != null">
                and charge like concat('%',#{param2},'%')
            </if>
        </where>
    </select>
    <select id="toChinese" resultType="java.lang.String">
        select group_concat(name)
        from s_case_reason where code in
        <foreach collection="codeList" index="index" item="i" open="(" separator="," close=")">
            #{i}
        </foreach>
    </select>
</mapper>
