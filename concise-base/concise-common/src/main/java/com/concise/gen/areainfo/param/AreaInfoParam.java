package com.concise.gen.areainfo.param;

import com.concise.common.pojo.base.param.BaseParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 省市县乡镇参数类
 *
 * <AUTHOR>
 * @date 2024-01-03 17:33:22
*/
@Data
public class AreaInfoParam extends BaseParam {

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查areaid参数", groups = {edit.class, delete.class, detail.class})
    private String areaid;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查areacode参数", groups = {add.class, edit.class})
    private String areacode;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotBlank(message = "不能为空，请检查areaname参数", groups = {add.class, edit.class})
    private String areaname;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查arealevel参数", groups = {add.class, edit.class})
    private Integer arealevel;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查zxs参数", groups = {add.class, edit.class})
    private Integer zxs;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查zgx参数", groups = {add.class, edit.class})
    private Integer zgx;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查parentareaid参数", groups = {add.class, edit.class})
    private Integer parentareaid;

    /**
     *
     */
    @ApiModelProperty(value = "")
    @NotNull(message = "不能为空，请检查parentareacode参数", groups = {add.class, edit.class})
    private Integer parentareacode;

}
