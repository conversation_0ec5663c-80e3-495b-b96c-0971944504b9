package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP6;
import com.concise.gen.ciInvestigate.param.InvestigateP6Param;
import java.util.List;

/**
 * 迁入调查评估_审批service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:23
 */
public interface InvestigateP6Service extends IService<InvestigateP6> {

    /**
     * 查询迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    PageResult<InvestigateP6> page(InvestigateP6Param investigateP6Param);

    /**
     * 迁入调查评估_审批列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    List<InvestigateP6> list(InvestigateP6Param investigateP6Param);

    /**
     * 添加迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    void add(InvestigateP6Param investigateP6Param);

    /**
     * 删除迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    void delete(InvestigateP6Param investigateP6Param);

    /**
     * 编辑迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    void edit(InvestigateP6Param investigateP6Param);

    /**
     * 查看迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
     InvestigateP6 detail(InvestigateP6Param investigateP6Param);
}
