package com.concise.gen.coordinatesupervisereceive.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinatesupervisereceive.entity.CoordinateSuperviseReceive;
import com.concise.gen.coordinatesupervisereceive.enums.CoordinateSuperviseReceiveExceptionEnum;
import com.concise.gen.coordinatesupervisereceive.mapper.CoordinateSuperviseReceiveMapper;
import com.concise.gen.coordinatesupervisereceive.param.CoordinateSuperviseReceiveParam;
import com.concise.gen.coordinatesupervisereceive.service.CoordinateSuperviseReceiveService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 接收外出监管协同service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
@Service
public class CoordinateSuperviseReceiveServiceImpl extends ServiceImpl<CoordinateSuperviseReceiveMapper, CoordinateSuperviseReceive> implements CoordinateSuperviseReceiveService {

    @Override
    public PageResult<CoordinateSuperviseReceive> page(CoordinateSuperviseReceiveParam param) {
        QueryWrapper<CoordinateSuperviseReceive> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据单位 查询
            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(CoordinateSuperviseReceive::getJzdwId, param.getOrgs());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CoordinateSuperviseReceive::getXm, param.getXm());
            }
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(CoordinateSuperviseReceive::getZt, param.getZt());
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(CoordinateSuperviseReceive::getSdwtsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(CoordinateSuperviseReceive::getSdwtsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CoordinateSuperviseReceive::getSdwtsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateSuperviseReceive> list(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam) {
        return this.list();
    }

    @Override
    public void add(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam) {
        CoordinateSuperviseReceive coordinateSuperviseReceive = new CoordinateSuperviseReceive();
        BeanUtil.copyProperties(coordinateSuperviseReceiveParam, coordinateSuperviseReceive);
        this.save(coordinateSuperviseReceive);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam) {
        this.removeById(coordinateSuperviseReceiveParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam) {
        CoordinateSuperviseReceive coordinateSuperviseReceive = this.queryCoordinateSuperviseReceive(coordinateSuperviseReceiveParam);
        BeanUtil.copyProperties(coordinateSuperviseReceiveParam, coordinateSuperviseReceive);
        this.updateById(coordinateSuperviseReceive);
    }

    @Override
    public CoordinateSuperviseReceive detail(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam) {
        return this.queryCoordinateSuperviseReceive(coordinateSuperviseReceiveParam);
    }

    /**
     * 获取接收外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
    private CoordinateSuperviseReceive queryCoordinateSuperviseReceive(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam) {
        CoordinateSuperviseReceive coordinateSuperviseReceive = this.getById(coordinateSuperviseReceiveParam.getId());
        if (ObjectUtil.isNull(coordinateSuperviseReceive)) {
            throw new ServiceException(CoordinateSuperviseReceiveExceptionEnum.NOT_EXIST);
        }
        return coordinateSuperviseReceive;
    }
}
