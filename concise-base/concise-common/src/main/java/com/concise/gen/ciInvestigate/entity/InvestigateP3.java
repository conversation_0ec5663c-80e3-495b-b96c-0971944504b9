package com.concise.gen.ciInvestigate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 迁入调查评估_调查
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:16
 */
@Data
@TableName("correction_immigration_investigate_p3")
public class InvestigateP3{

    @TableField(exist = false)
    private List<InvestigateP3Record> records;
    private Date createTime;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 是否开展调查
     */
    private String zt;

    /**
     * 调查人
     */
    private String dcr;

    /**
     * 调查时间
     */
    @Excel(name = "调查时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date dcsj;

    /**
     * 备注
     */
    private String bz;

    /**
     * 退回理由
     */
    private String thly;

    /**
     * 调查单位（县区局）
     */
    private String dcdw1;

    /**
     * 调查单位（司法所）
     */
    private String dcdw2;

    /**
     * 委托调查材料
     */
    private String wtdccl;
    @TableField(exist = false)
    private List<SysFileInfoVO> wtdcclFileList;

    /**
     * 民族
     */
    private String mz;

    /**
     * 别名
     */
    private String bm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 籍贯
     */
    private String jg;

    /**
     * 家庭住址
     */
    private String jtzz;

    /**
     * 家庭住址明细
     */
    private String jtzzmx;

    /**
     * 联系电话
     */
    private String lxdh;

    /**
     * 职业
     */
    private String zy;

    /**
     * 有无家庭成员
     */
    private String ywjtcy;

    /**
     * 社会交往情况
     */
    private String shjwqk;

    /**
     * 主要社会关系
     */
    private String zyshgx;

    /**
     * 未成年对象的其他情况
     */
    private String wcn;

    /**
     * 家庭成员(jsonarray)
     */
    private String jtcy;

    /**
     * 生理状况
     */
    private String slzk;

    /**
     * 心理特征
     */
    private String xltz;

    /**
     * 性格类型
     */
    private String xglx;

    /**
     * 爱好特长
     */
    private String ahtc;

    /**
     * 工作（学习）表现
     */
    private String gzxxbx;

    /**
     * 遵纪守法情况
     */
    private String zjsfqk;

    /**
     * 有无不良嗜好、行为恶习
     */
    private String blsh;

    /**
     * 犯罪原因
     */
    private String fzyy;

    /**
     * 主观恶性
     */
    private String zgex;

    /**
     * 是否有犯罪前科
     */
    private String sfyfzqk;

    /**
     * 认罪悔罪态度
     */
    private String rzhztd;

    /**
     * 被害人或其亲属态度
     */
    private String bhrhqqstd;

    /**
     * 社会公众态度
     */
    private String shgztd;

    /**
     * 家庭成员态度
     */
    private String jtcytd;

    /**
     * 经济生活状况和环境
     */
    private String jjshzkhhj;

    /**
     * 工作单位、就读学校和村（社区）基层组织意见
     */
    private String zzyj;

    /**
     * 辖区公安派出所意见
     */
    private String gayj;

}
