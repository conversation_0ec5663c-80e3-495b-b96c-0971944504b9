package com.concise.gen.chargeinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.chargeinfo.entity.ChargeInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 罪名表
 *
 * <AUTHOR>
 * @date 2023-06-16 15:30:23
 */
public interface ChargeInfoMapper extends BaseMapper<ChargeInfo> {
    /**
     * 查询罪名
     * @param chargeCode 查询本级及下一级 未传值时查询所有第一级数据
     * @param charge 罪名名称
     * @return ChargeInfo
     */
    List<ChargeInfo> getChargeInfoList(String chargeCode, String charge);

    String toChinese(@Param("codeList") List<String> codeList);
}
