package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP1Param;
import com.concise.gen.ciInvestigate. service.InvestigateP1Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:13
 */
@Api(tags = "迁入调查评估")
@RestController
public class InvestigateP1Controller {

    @Resource
    private InvestigateP1Service investigateP1Service;

    /**
     * 查询迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    @Permission
    @GetMapping("/investigateP1/page")
    @ApiOperation("迁入调查评估_分页查询")
    @BusinessLog(title = "迁入调查评估_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP1Param investigateP1Param) {
        return new SuccessResponseData(investigateP1Service.page(investigateP1Param));
    }

    /**
     * 添加迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    @Permission
    @PostMapping("/investigateP1/add")
    @ApiOperation("迁入调查评估_增加")
    @BusinessLog(title = "迁入调查评估_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP1Param.add.class) InvestigateP1Param investigateP1Param) {
        investigateP1Service.add(investigateP1Param);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    @Permission
    @PostMapping("/investigateP1/delete")
    @ApiOperation("迁入调查评估_删除")
    @BusinessLog(title = "迁入调查评估_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP1Param.delete.class) InvestigateP1Param investigateP1Param) {
        investigateP1Service.delete(investigateP1Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    @Permission
    @PostMapping("/investigateP1/edit")
    @ApiOperation("迁入调查评估_编辑")
    @BusinessLog(title = "迁入调查评估_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP1Param.edit.class) InvestigateP1Param investigateP1Param) {
        investigateP1Service.edit(investigateP1Param);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    @Permission
    @GetMapping("/investigateP1/detail")
    @ApiOperation("迁入调查评估_查看")
    @BusinessLog(title = "迁入调查评估_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP1Param.detail.class) InvestigateP1Param investigateP1Param) {
        return new SuccessResponseData(investigateP1Service.detail(investigateP1Param));
    }

    /**
     * 迁入调查评估列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    @Permission
    @GetMapping("/investigateP1/list")
    @ApiOperation("迁入调查评估_列表")
    @BusinessLog(title = "迁入调查评估_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP1Param investigateP1Param) {
        return new SuccessResponseData(investigateP1Service.list(investigateP1Param));
    }

}
