package com.concise.gen.coordinateinvestigatesend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigatesend.entity.CoordinateInvestigateSend;
import com.concise.gen.coordinateinvestigatesend.enums.CoordinateInvestigateSendExceptionEnum;
import com.concise.gen.coordinateinvestigatesend.mapper.CoordinateInvestigateSendMapper;
import com.concise.gen.coordinateinvestigatesend.param.CoordinateInvestigateSendParam;
import com.concise.gen.coordinateinvestigatesend.service.CoordinateInvestigateSendService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估协查_发起跨省委托service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-13 16:42:02
 */
@Service
public class CoordinateInvestigateSendServiceImpl extends ServiceImpl<CoordinateInvestigateSendMapper, CoordinateInvestigateSend> implements CoordinateInvestigateSendService {

    @Override
    public PageResult<CoordinateInvestigateSend> page(CoordinateInvestigateSendParam param) {
        QueryWrapper<CoordinateInvestigateSend> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据协同状态 查询
            if (ObjectUtil.isNotEmpty(param.getXtzt())) {
                queryWrapper.lambda().eq(CoordinateInvestigateSend::getXtzt, param.getXtzt());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CoordinateInvestigateSend::getXm, param.getXm().trim());
            }
            // 根据矫正单位 查询
            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(CoordinateInvestigateSend::getJzdwId, param.getOrgs());
            }
            // 根据身份证号 查询
            if (ObjectUtil.isNotEmpty(param.getSfzh())) {
                queryWrapper.lambda().eq(CoordinateInvestigateSend::getSfzh, param.getSfzh());
            }
            // 根据申请时间 查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(CoordinateInvestigateSend::getSqsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(CoordinateInvestigateSend::getSqsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CoordinateInvestigateSend::getSqsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateSend> list(CoordinateInvestigateSendParam coordinateInvestigateSendParam) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateSendParam coordinateInvestigateSendParam) {
        CoordinateInvestigateSend coordinateInvestigateSend = new CoordinateInvestigateSend();
        BeanUtil.copyProperties(coordinateInvestigateSendParam, coordinateInvestigateSend);
        coordinateInvestigateSend.setSqsj(DateUtil.date());
        coordinateInvestigateSend.setXtzt("1");
        this.save(coordinateInvestigateSend);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateSendParam coordinateInvestigateSendParam) {
        this.removeById(coordinateInvestigateSendParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateSendParam coordinateInvestigateSendParam) {
        CoordinateInvestigateSend coordinateInvestigateSend = this.queryCoordinateInvestigateSend(coordinateInvestigateSendParam);
        BeanUtil.copyProperties(coordinateInvestigateSendParam, coordinateInvestigateSend);
        this.updateById(coordinateInvestigateSend);
    }

    @Override
    public CoordinateInvestigateSend detail(CoordinateInvestigateSendParam coordinateInvestigateSendParam) {
        return this.queryCoordinateInvestigateSend(coordinateInvestigateSendParam);
    }

    /**
     * 获取调查评估协查_发起跨省委托
     *
     * <AUTHOR>
     * @date 2023-06-13 16:42:02
     */
    private CoordinateInvestigateSend queryCoordinateInvestigateSend(CoordinateInvestigateSendParam coordinateInvestigateSendParam) {
        CoordinateInvestigateSend coordinateInvestigateSend = this.getById(coordinateInvestigateSendParam.getId());
        if (ObjectUtil.isNull(coordinateInvestigateSend)) {
            throw new ServiceException(CoordinateInvestigateSendExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateSend;
    }
}
