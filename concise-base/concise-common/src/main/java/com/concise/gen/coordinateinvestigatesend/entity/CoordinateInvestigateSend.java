package com.concise.gen.coordinateinvestigatesend.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 调查评估协查_发起跨省委托
 *
 * <AUTHOR>
 * @date 2023-06-13 16:42:02
 */
@Data
@TableName("coordinate_investigate_send")
public class CoordinateInvestigateSend{

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 协同状态
     */
    private String xtzt;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 矫正单位
     */
    private String jzdw;
    private String jzdwId;

    /**
     * 拟适用社区矫正人员类型
     */
    private String nsylx;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 性别
     */
    private String xb;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 居住地地址
     */
    private String jzddz;

    /**
     * 工作单位
     */
    private String gzdw;

    /**
     * 协查意见
     */
    private String xcyj;

    /**
     * 受理结果
     */
    private String sljg;

    /**
     * 申请时间
     */
    private Date sqsj;

    /**
     * 罪名
     */
    private String zm;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 原判刑期开始日期
     */
    private Date ypxqksrq;

    /**
     * 原判刑期结束日期
     */
    private Date ypxqjsrq;

    /**
     * 原判刑罚
     */
    private String ypxf;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 判决机关
     */
    private String pjjg;

    /**
     * 判决日期
     */
    private String pjrq;

    /**
     * 委托调查书(附件)
     */
    private String wtdcs;

    /**
     * 委托省(市)
     */
    private String wtss;

    /**
     * 委托协查单位
     */
    private String wtxcdw;

    /**
     * 建议协查完成返回期限
     */
    private Date jyfhqx;

}
