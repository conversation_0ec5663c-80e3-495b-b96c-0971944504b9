package com.concise.gen.investigationotheruser. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.investigationotheruser. param.InvestigationOtherUserParam;
import com.concise.gen.investigationotheruser. service.InvestigationOtherUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 调查评估-其他用户控制器
 *
 * <AUTHOR>
 * @date 2025-08-01 11:02:13
 */
@Api(tags = "调查评估-其他用户")
@RestController
public class InvestigationOtherUserController {

    @Resource
    private InvestigationOtherUserService investigationOtherUserService;

    /**
     * 查询调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    @GetMapping("/investigationOtherUser/page")
    @ApiOperation("调查评估-其他用户_分页查询")
    public ResponseData page(InvestigationOtherUserParam investigationOtherUserParam) {
        return new SuccessResponseData(investigationOtherUserService.page(investigationOtherUserParam));
    }

    /**
     * 添加调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    @PostMapping("/investigationOtherUser/add")
    @ApiOperation("调查评估-其他用户_增加")
    @BusinessLog(title = "调查评估-其他用户_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigationOtherUserParam.add.class) InvestigationOtherUserParam investigationOtherUserParam) {
        investigationOtherUserService.add(investigationOtherUserParam);
        return new SuccessResponseData();
    }

    /**
     * 删除调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    @PostMapping("/investigationOtherUser/delete")
    @ApiOperation("调查评估-其他用户_删除")
    @BusinessLog(title = "调查评估-其他用户_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigationOtherUserParam.delete.class) InvestigationOtherUserParam investigationOtherUserParam) {
        investigationOtherUserService.delete(investigationOtherUserParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    @PostMapping("/investigationOtherUser/edit")
    @ApiOperation("调查评估-其他用户_编辑")
    @BusinessLog(title = "调查评估-其他用户_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigationOtherUserParam.edit.class) InvestigationOtherUserParam investigationOtherUserParam) {
        investigationOtherUserService.edit(investigationOtherUserParam);
        return new SuccessResponseData();
    }

    /**
     * 查看调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    @GetMapping("/investigationOtherUser/detail")
    @ApiOperation("调查评估-其他用户_查看")
    public ResponseData detail(@Validated(InvestigationOtherUserParam.detail.class) InvestigationOtherUserParam investigationOtherUserParam) {
        return new SuccessResponseData(investigationOtherUserService.detail(investigationOtherUserParam));
    }

    /**
     * 调查评估-其他用户列表
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    @GetMapping("/investigationOtherUser/list")
    @ApiOperation("调查评估-其他用户_列表")
    public ResponseData list(InvestigationOtherUserParam investigationOtherUserParam) {
        return new SuccessResponseData(investigationOtherUserService.list(investigationOtherUserParam));
    }

}
