package com.concise.gen.acceptbaseinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.base.entity.BaseAcceptEntity;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.PoiUtil;
import com.concise.gen.acceptbaseinfo.entity.AcceptBaseInfo;
import com.concise.gen.acceptbaseinfo.mapper.AcceptBaseInfoMapper;
import com.concise.gen.acceptbaseinfo.param.AcceptBaseInfoParam;
import com.concise.gen.acceptbaseinfo.service.AcceptBaseInfoService;
import com.concise.gen.acceptbaseinfo.vo.ExtOrgStatisticsVo;
import com.concise.gen.acceptbaseinfo.vo.IndicatorCollaborationVo;
import com.concise.gen.acceptbaseinfo.vo.StatVo;
import com.concise.gen.acceptbaseinfo.vo.StatisticsVo;
import com.concise.gen.acceptcorrectionobject.entity.AcceptCorrectionObject;
import com.concise.gen.acceptcorrectionobject.service.AcceptCorrectionObjectService;
import com.concise.gen.acceptdischargedinfo.service.AcceptDischargedInfoService;
import com.concise.gen.acceptinvestinfo.service.AcceptInvestInfoService;
import com.concise.gen.acceptparoleexecute.service.AcceptParoleExecuteService;
import com.concise.gen.acceptprisonexecute.service.AcceptPrisonExecuteService;
import com.concise.gen.acceptreturninfo.entity.AcceptReturnInfo;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import com.concise.gen.accepttemporarilyoutsideprison.entity.AcceptTemporarilyOutsidePrison;
import com.concise.gen.accepttemporarilyoutsideprison.service.AcceptTemporarilyOutsidePrisonService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 交付衔接协同service接口实现类
 *
 * <AUTHOR>
 * @date 2023-02-03 16:46:06
 */
@Service
public class AcceptBaseInfoServiceImpl extends ServiceImpl<AcceptBaseInfoMapper, AcceptBaseInfo> implements AcceptBaseInfoService {

    @Resource
    private AcceptReturnInfoService acceptReturnInfoService;
    @Resource
    private AcceptCorrectionObjectService acceptCorrectionObjectService;
    @Resource
    private AcceptInvestInfoService acceptInvestInfoService;
    @Resource
    private AcceptDischargedInfoService acceptDischargedInfoService;
    @Resource
    private AcceptParoleExecuteService acceptParoleExecuteService;
    @Resource
    private AcceptPrisonExecuteService acceptPrisonExecuteService;
    @Resource
    private AcceptTemporarilyOutsidePrisonService acceptTemporarilyOutsidePrisonService;
    @Resource
    private OrgCommonService orgCommonService;

    @Override
    public PageResult<AcceptBaseInfo> page(AcceptBaseInfoParam param) {
        QueryWrapper<AcceptBaseInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {
            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getTaskId())) {
                queryWrapper.lambda().eq(AcceptBaseInfo::getTaskId, param.getTaskId());
            }
            // 根据数据状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                ArrayList<String> zt = ListUtil.toList(param.getZt().split(","));
                queryWrapper.lambda().in(AcceptBaseInfo::getZt, zt);
            }
            // 根据数据来源(机构类型) 查询
            if (ObjectUtil.isNotEmpty(param.getSjlylx())) {
                queryWrapper.lambda().likeRight(AcceptBaseInfo::getSjlylx, param.getSjlylx());
            }
            if (ObjectUtil.isNotEmpty(param.getDataFrom())) {
                queryWrapper.lambda().eq(AcceptBaseInfo::getDataFrom, param.getDataFrom());
            }
            // 根据协同类型 查询
            if (ObjectUtil.isNotEmpty(param.getXtlx())) {
                queryWrapper.lambda().eq(AcceptBaseInfo::getXtlx, param.getXtlx());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(AcceptBaseInfo::getXm, param.getXm());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(param.getJsdw())) {
                queryWrapper.lambda().and(i -> i.eq(AcceptBaseInfo::getJsdwId, param.getJsdw())
                        .or().like(AcceptBaseInfo::getJsdwPids, param.getJsdw())
                        .or().eq(AcceptBaseInfo::getJzjg, param.getJsdw())
                );
            }
            // 根据推送单位 查询
            if (ObjectUtil.isNotEmpty(param.getTsdw())) {
                queryWrapper.lambda().likeRight(AcceptBaseInfo::getTsdw, param.getTsdw());
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(AcceptBaseInfo::getSdsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(AcceptBaseInfo::getSdsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(AcceptBaseInfo::getSdsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptBaseInfo> list(AcceptBaseInfoParam acceptBaseInfoParam) {
        return this.list();
    }

    @Override
    public void add(AcceptBaseInfoParam acceptBaseInfoParam) {
        AcceptBaseInfo acceptBaseInfo = new AcceptBaseInfo();
        BeanUtil.copyProperties(acceptBaseInfoParam, acceptBaseInfo);
        this.save(acceptBaseInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptBaseInfoParam acceptBaseInfoParam) {
        this.removeById(acceptBaseInfoParam.getId());
    }

    @Override
    public void delete(String taskId) {
        List<AcceptBaseInfo> list = this.lambdaQuery().eq(AcceptBaseInfo::getTaskId, taskId).list();
        if (list.size() == 0) {
            return;
        }
        switch (list.get(0).getXtbh()) {
            case "XTBH4003":
                acceptCorrectionObjectService.delete(taskId);
                break;
            case "XTBH31001":
            case "XTBH31003":
                acceptInvestInfoService.delete(taskId);
                break;
            case "XTBH16001_1":
                acceptDischargedInfoService.delete(taskId);
                break;
            case "XTBH5021_1":
                acceptParoleExecuteService.delete(taskId);
                break;
            case "XTBH4302_1":
            case "XTBH4312_1":
                acceptPrisonExecuteService.delete(taskId);
                break;
            case "XTBH4052_1":
            case "XTBH4072_1":
                acceptTemporarilyOutsidePrisonService.delete(taskId);
                break;
            default:
        }
        for (AcceptBaseInfo info : list) {
            acceptReturnInfoService.lambdaUpdate().eq(AcceptReturnInfo::getContactId, info.getId());
            this.removeById(info.getId());
        }
    }

    @Override
    public void edit(AcceptBaseInfoParam param) {

        switch (param.getXtbh()) {
            case "XTBH4003":
                acceptCorrectionObjectService.edit(param);
                break;
            case "XTBH31001":
            case "XTBH31003":
                //acceptInvestInfoService.delete(taskId);
                break;
            case "XTBH16001_1":
                //acceptDischargedInfoService.delete(taskId);
                break;
            case "XTBH5021_1":
                //acceptParoleExecuteService.delete(taskId);
                break;
            case "XTBH4302_1":
            case "XTBH4312_1":
                //acceptPrisonExecuteService.delete(taskId);
                break;
            case "XTBH4022_1":
            case "XTBH4052_1":
            case "XTBH4072_1":
                acceptTemporarilyOutsidePrisonService.edit(param);
                break;
            default:
        }
    }

    @Override
    public void setStatus(AcceptBaseInfoParam param) {
        switch (param.getXtbh()) {
            case "XTBH4003":
                acceptCorrectionObjectService.lambdaUpdate()
                        .set(AcceptCorrectionObject::getZt, param.getZt())
                        .eq(AcceptCorrectionObject::getId, param.getId())
                        .update();
                break;
            case "XTBH4022_1":
            case "XTBH4052_1":
            case "XTBH4072_1":
                acceptTemporarilyOutsidePrisonService.lambdaUpdate()
                        .set(AcceptTemporarilyOutsidePrison::getZt, param.getZt())
                        .eq(AcceptTemporarilyOutsidePrison::getId, param.getId())
                        .update();
                break;
            default:
        }
        this.lambdaUpdate()
                .set(AcceptBaseInfo::getZt, param.getZt())
                .eq(AcceptBaseInfo::getId, param.getId())
                .update();
    }

    @Override
    public Object detail(AcceptBaseInfoParam param) {
        switch (param.getXtbh()) {
            case "XTBH4003":
                return acceptCorrectionObjectService.detail(param.getId());
            case "XTBH31001":
            case "XTBH31003":
                //acceptInvestInfoService.delete(taskId);
                break;
            case "XTBH16001_1":
                //acceptDischargedInfoService.delete(taskId);
                break;
            case "XTBH5021_1":
                //acceptParoleExecuteService.delete(taskId);
                break;
            case "XTBH4302_1":
            case "XTBH4312_1":
                //acceptPrisonExecuteService.delete(taskId);
                break;
            case "XTBH4022_1":
            case "XTBH4052_1":
            case "XTBH4072_1":
                return acceptTemporarilyOutsidePrisonService.detail(param.getId());
            default:
        }
        return null;
    }

    @Override
    public String distName(String dist) {
        return this.getBaseMapper().distName(dist);
    }


    @Override
    public String extDictTrans(String type, String dict, String source) {
        String target = this.baseMapper.extDictTrans(type, dict, source);
        if (ObjectUtil.isEmpty(target)) {
            return "";
        }
        return target;
    }

    @Override
    public List<IndicatorCollaborationVo> indicatorCollaboration(String orgId) {
        List<IndicatorCollaborationVo> indicatorCollaborationVoList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            String month = DateUtil.format(DateUtil.offsetMonth(DateUtil.date(), -i), DatePattern.NORM_MONTH_FORMAT);
            List<StatisticsVo> statisticsVoList = this.baseMapper.indicatorCollaboration(orgId, month);
            IndicatorCollaborationVo vo = new IndicatorCollaborationVo();
            if (CollectionUtil.isNotEmpty(statisticsVoList)) {
                for (StatisticsVo s : statisticsVoList) {
                    switch (s.getType()) {
                        case "0":
                            // 待接收
                            vo.setZero(s.getAmount());
                            break;
                        case "1":
                            // 待反馈
                            vo.setOne(s.getAmount());
                            break;
                        case "2":
                            // 退回
                            vo.setTwo(s.getAmount());
                            break;
                        default:
                            break;
                    }
                    // 总数
                    vo.setFour(vo.getFour()+s.getAmount());
                }
            }
            vo.setMonth(month.substring(5, 7));
            indicatorCollaborationVoList.add(vo);
        }
        return indicatorCollaborationVoList;
    }

    @Override
    public JSONArray step(AcceptBaseInfoParam param) {
        switch (param.getXtbh()) {
            case "XTBH4003":
                AcceptCorrectionObject detail = acceptCorrectionObjectService.getById(param.getId());
                OrgCommon org = orgCommonService.getById(detail.getJzjg());
                return buildStepList(detail, org==null?"":org.getName(),detail.getShjgName());
            case "XTBH31001":
            case "XTBH31003":
                //acceptInvestInfoService.delete(taskId);
                break;
            case "XTBH16001_1":
                //acceptDischargedInfoService.delete(taskId);
                break;
            case "XTBH5021_1":
                //acceptParoleExecuteService.delete(taskId);
                break;
            case "XTBH4302_1":
            case "XTBH4312_1":
                //acceptPrisonExecuteService.delete(taskId);
                break;
            case "XTBH4022_1":
            case "XTBH4052_1":
            case "XTBH4072_1":
                AcceptTemporarilyOutsidePrison top = acceptTemporarilyOutsidePrisonService.detail(param.getId());
                OrgCommon org1 = orgCommonService.getById(top.getJzjg());
                return buildStepList(top, org1==null?"":org1.getName(),top.getShjgName());
            default:
        }

        return new JSONArray();
    }

    @Override
    public void insertExtOrgStatistics() {
        this.baseMapper.insertExtOrgStatistics();
        this.baseMapper.updateExtOrgStatisticsOrgName();
    }

    @Override
    public List<ExtOrgStatisticsVo> rankExtOrgStatistics(int type, Set<String> orgSet) {
        List<ExtOrgStatisticsVo> list = this.baseMapper.rankExtOrgStatistics(type,orgSet);
        for (ExtOrgStatisticsVo vo : list) {
            int s = vo.getAccepted() + vo.getReturned();
            if (s==0) {
                vo.setPer(0);
            }else {
                vo.setPer(vo.getAccepted() * 10000 / s);
            }
        }
        list.sort((o1, o2) -> o2.getPer() - o1.getPer());
        return list;
    }

    @Override
    public List<StatisticsVo> trend(String orgId, int level, String time) {
        List<StatisticsVo> trend = this.baseMapper.trend(orgId, level, time);
        for (StatisticsVo vo : trend) {
            int beginIndex = vo.getY().lastIndexOf("/");
            if(beginIndex > 0){
                vo.setY(vo.getY().substring(beginIndex+1));
            }
        }
        return trend;
    }

    @Override
    public List<StatVo> statistics(String orgId, int level, String start, String end) {

        if (level == 1) {
            return addSum(this.baseMapper.statisticsLevel1(start,end));
        }else if (level == 2) {
            return addSum(this.baseMapper.statisticsLevel2(orgId,start,end));
        }else if (level == 3) {
            return addSum(this.baseMapper.statisticsLevel3(orgId, start, end));
        }else if (level == 4) {
            return this.baseMapper.statisticsLevel4(orgId,start,end);
        }
        return null;
    }

    @Override
    public void statisticsExport(String orgId, int level, String start, String end) {
        List<StatVo> list = null;
        if (level == 1) {
            list = addSum(this.baseMapper.statisticsLevel1(start,end));
        }else if (level == 2) {
            list =  addSum(this.baseMapper.statisticsLevel2(orgId,start,end));
        }else if (level == 3) {
            list =  addSum(this.baseMapper.statisticsLevel3(orgId,start,end));
        }else if (level == 4) {
            list =  this.baseMapper.statisticsLevel4(orgId,start,end);
        }
        PoiUtil.exportExcelWithStream("矫正对象衔接协同统计.xls", StatVo.class, list);
    }

    private List<StatVo> addSum(List<StatVo> statVos) {
        StatVo all = new StatVo("总计");
        statVos.forEach(statVo -> {
            all.setZt1(statVo.getZt1()+all.getZt1());
            all.setZt2(statVo.getZt2()+all.getZt2());
            all.setZt3(statVo.getZt3()+all.getZt3());
            all.setZt4(statVo.getZt4()+all.getZt4());
            all.setZt5(statVo.getZt5()+all.getZt5());
        });
        statVos.add(0,all);
        return statVos;
    }

    private JSONArray buildStepList(BaseAcceptEntity detail, String jzjgName, String shjgName) {
        JSONArray steps = new JSONArray();
        JSONObject s1 = new JSONObject();
        s1.put("stepName","社区矫正决定机关决定");
        s1.put("operator", detail.getTsdwmc());
        s1.put("timeTitle","协同日期");
        s1.put("opTime",DateUtil.formatDate(detail.getSdsj()));
        s1.put("sysName","政法办案系统");
        JSONArray sa1 = new JSONArray();
        sa1.add(s1);
        JSONObject g1 = new JSONObject();
        g1.put("groupName","人员衔接");
        g1.put("icon","step1");
        g1.put("steps",sa1);
        steps.add(g1);

        if ("0".equals(detail.getZt())) {
            steps.add(JSONArray.parse("{\"groupName\":\"审核信息\",\"icon\":\"step2\",\"steps\":[{\"timeTitle\":\"接收日期\",\"stepName\":\"审核信息并接收至司法所\",\"stepType\":\"1\",\"sysName\":\"社矫数据协同系统\",\"operator\":\""+ detail.getJsdwmc()+"\",\"active\":true}]}"));
            steps.add(JSONArray.parse("{\"groupName\":\"补充信息\",\"icon\":\"step3\",\"steps\":[{\"timeTitle\":\"入矫日期\",\"stepName\":\"执法办案补充入矫信息\"}]}"));
            steps.add(JSONArray.parse("{\"groupName\":\"反馈回执\",\"icon\":\"step4\",\"steps\":[{\"timeTitle\":\"反馈日期\",\"stepName\":\"反馈回执至社区矫正决定机关\"}]}"));
        }

        if ("1".equals(detail.getZt())) {
            JSONObject s2 = new JSONObject();
            s2.put("stepName","审核信息并接收至司法所");
            s2.put("operator", detail.getJsdwmc());
            s2.put("timeTitle","接收日期");
            s2.put("opTime", DateUtil.formatDate(detail.getShsj()));
            s2.put("sysName","社矫数据协同系统");
            JSONArray sa2 = new JSONArray();
            sa2.add(s2);
            JSONObject g2 = new JSONObject();
            g2.put("groupName","审核信息");
            g2.put("icon","step2");
            g2.put("steps",sa2);
            steps.add(g2);
            JSONObject s3 = new JSONObject();
            s3.put("stepName","执法办案补充入矫信息");
            s3.put("operator", jzjgName);
            s3.put("timeTitle","入矫日期");
            s3.put("opTime", DateUtil.formatDate(detail.getRjrq()));
            s3.put("sysName","社矫执法办案系统");
            JSONArray sa3 = new JSONArray();
            sa3.add(s3);
            JSONObject g3 = new JSONObject();
            g3.put("groupName","补充信息");
            g3.put("icon","step3");
            g3.put("steps",sa3);
            steps.add(g3);
            steps.add(JSONArray.parse("{\"groupName\":\"反馈回执\",\"icon\":\"step4\",\"steps\":[{\"timeTitle\":\"反馈日期\",\"stepName\":\"反馈回执至社区矫正决定机关\",\"stepType\":\"2\",\"active\":true}]}"));
        }

        if ("2".equals(detail.getZt())||"3".equals(detail.getZt())) {
            //退回
            JSONObject s2 = new JSONObject();
            s2.put("stepName","审核信息并接收至司法所");
            s2.put("active",true);
            s2.put("operator", detail.getJsdwmc());
            s2.put("timeTitle","退回日期");
            s2.put("opTime", DateUtil.formatDate(detail.getShsj()));
            s2.put("sysName","社矫数据协同系统");
            s2.put("stepType","3");
            s2.put("remark",shjgName+","+detail.getShbz());
            JSONArray sa2 = new JSONArray();
            sa2.add(s2);
            JSONObject g2 = new JSONObject();
            g2.put("groupName","审核信息");
            g2.put("icon","step2");
            g2.put("steps",sa2);
            steps.add(g2);
        }

        if ("4".equals(detail.getZt())) {
            JSONObject s2 = new JSONObject();
            s2.put("stepName","审核信息并接收至司法所");
            s2.put("operator", detail.getJsdwmc());
            s2.put("timeTitle","接收日期");
            s2.put("opTime", DateUtil.formatDate(detail.getShsj()));
            s2.put("sysName","社矫数据协同系统");
            JSONArray sa2 = new JSONArray();
            sa2.add(s2);
            JSONObject g2 = new JSONObject();
            g2.put("groupName","审核信息");
            g2.put("icon","step2");
            g2.put("steps",sa2);
            steps.add(g2);

            JSONObject s3 = new JSONObject();
            s3.put("stepName","执法办案补充入矫信息");
            s3.put("operator", jzjgName);
            s3.put("timeTitle","入矫日期");
            s3.put("opTime", DateUtil.formatDate(detail.getRjrq()));
            s3.put("sysName","社矫执法办案系统");
            JSONArray sa3 = new JSONArray();
            sa3.add(s3);
            JSONObject g3 = new JSONObject();
            g3.put("groupName","补充信息");
            g3.put("icon","step3");
            g3.put("steps",sa3);
            steps.add(g3);

            JSONObject s4 = new JSONObject();
            s4.put("stepName","反馈回执至社区矫正决定机关");
            s4.put("active",true);
            s4.put("operator", detail.getJsdwmc());
            s4.put("timeTitle","反馈日期");
            s4.put("opTime", DateUtil.formatDate(detail.getFksj()));
            s4.put("sysName","社矫数据协同系统");
            JSONArray sa4 = new JSONArray();
            sa4.add(s4);
            JSONObject g4 = new JSONObject();
            g4.put("groupName","反馈回执");
            g4.put("icon","step4");
            g4.put("steps",sa4);
            steps.add(g4);
        }
        return steps;
    }


}
