package com.concise.gen.acceptcorrectiondoc.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 矫正对象法律文书信息信息接收表参数类
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptCorrectionDocParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     *
     */
    private String contactId;
    private String xtbh;

    /**
     * 文书名称
     */
    @NotBlank(message = "文书名称不能为空，请检查ws参数", groups = {add.class, edit.class})
    private String ws;

    /**
     * 文书代码
     */
    @NotBlank(message = "文书代码不能为空，请检查wsdm参数", groups = {add.class, edit.class})
    private String wsdm;

    /**
     * URI
     */
    @NotBlank(message = "URI不能为空，请检查uri参数", groups = {add.class, edit.class})
    private String uri;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查fileId参数", groups = {add.class, edit.class})
    private String fileId;
    private String ossUrl;
    private String bizType = "receive";

}
