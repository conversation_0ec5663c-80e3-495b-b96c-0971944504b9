package com.concise.gen.accepttemporarilyoutsideprison.param;

import com.concise.common.file.param.SysFileInfoParam;
import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
* 暂予监外接收表参数类
 *
 * <AUTHOR>
 * @date 2022-12-01 10:33:30
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptTemporarilyOutsidePrisonParam extends BaseParam {


    private Set<String> orgs;
    private List<AcceptCorrectionDocParam> docList;
    private List<SysFileInfoParam> hzclList;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    private String jzlb;
    private String taskId;
    private String xtbh;
    /**
     * 审核结果
     */
    private String shjgName;

    /**
     * 数据状态
     * 0,待接收
     * 1,待反馈
     * 2,退回
     * 3,拒接收
     * 4,已反馈
     */
    private String zt;
    /** 是否自动退回*/
    private String returned;

    /**
     * 数据来源(机构类型)
     * OrgTypeEnum
     *        10  "政法委"
     *        20  "法院"
     *        30  "检察院"
     *        40  "公安"
     *        41  "监管场所" 监所 看守所
     *        42  "公安戒毒场所"
     *        50  "安全"
     *        60  "司法"
     *        61  "监狱"
     *        62  "社区矫正"
     *        63  "司法戒毒场所"
     *        64  "法律援助中心"
     *        65  "司法局数据中心"
     *        70  "物管中心"
     *        80  "纪委监委"
     */
    private String sjlylx;

    /**
     * 接收单位
     */
    private String jsdw;

    /**
     * 接收单位名称
     */
    private String jsdwmc;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 推送单位
     */
    private String tsdw;

    /**
     * 推送单位名称
     */
    private String tsdwmc;
    /**推送时间*/
    private String tssj;

    /**
     * 送达时间
     */
    private String sdsj;


    /**
     * 审核时间
     */
    private String shsj;

    /**
     * 审核人员
     */
    private String shry;

    /**
     * 审核矫正机构
     */
    private String shjzjg;

    /**
     * 审核结果
     */
    private String shjg;
    /**
     * 审核备注
     */
    private String shbz;


    /**
     * 执行通知书回执文号
     */
    private String zxtzshzwh;

    /**
     * 入矫日期
     */
    private String rjrq;

    /**
     * 社区矫正机关
     */
    private String sqjzjg;

    /**
     * 反馈人
     */
    private String fkr;

    /**
     * 反馈时间
     */
    private String fksj;

    /**
     * 反馈结果
     */
    private String fkjg;

    /**
     * 反馈备注
     */
    private String fkbz;

    /**
     * 反馈法院机构code
     */
    private String fkFyCode;
    /**
     * 反馈看守所机构code
     */
    private String fkKssCode;
    /**
     * 反馈检察院机构code
     */
    private String fkJcyCode;
    /**
     * 反馈公安机构code
     */
    private String fkGaCode;



    /**
     * 回执材料
     */

    private String hzcl;
    /**
     * 备注
     */
    private String bz;

    /**
     * 统一赋号
     */

    private String tyfh;

    /**
     * 案件编号
     */

    private String ajbs;

    /**
     * 公安嫌疑人编号
     */

    private String gaxyrbh;

    /**
     * 检察院嫌疑人编号
     */

    private String jcyxyrbh;

    /**
     * 姓名
     */

    private String xm;

    /**
     * 曾用名
     */

    private String cym;

    /**
     * 性别
     */

    private String xb;

    /**
     * 证件类型
     */

    private String zjlx;

    /**
     * 证件号码
     */

    private String zjhm;

    /**
     * 出生日期
     */

    private String csrq;

    /**
     * 民族
     */

    private String mz;

    /**
     * 出生地
     */

    private String csd;

    /**
     * 户籍地
     */

    private String hjd;

    /**
     * 户籍地详址
     */

    private String hjdxz;

    /**
     * 现住地
     */

    private String xzd;

    /**
     * 现住地详址
     */

    private String xzdxz;

    /**
     * 文化程度
     */

    private String whcd;

    /**
     * 决定书文号
     */

    private String jdswh;

    /**
     * 是否决定暂予监外执行
     */

    private String sfjdzyjwzx;

    /**
     * 决定（不）暂予监外执行日期
     */

    private String jdzyjwzxrq;

    /**
     * 暂予监外执行决定机关
     */

    private String zyjwzxjdjg;

    /**
     * （不）暂予监外执行原因
     */

    private String zyjwzxyy;

    /**
     * 矫正机构
     */

    private String jzjg;

    /**
     * 交付执行日期
     */

    private String jfzxrq;

    /**
     * 移交罪犯机关类型
     */

    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */

    private String yjzfjgmc;

    /**
     * 矫正期限
     */

    private String jzqx;

    /**
     * 暂予监外执行起日
     */

    private String zyjwzxqr;

    /**
     * 暂予监外执行止日
     */

    private String zyjwzxzr;

    /**
     * 生效判决机关
     */

    private String sxpjjg;

    /**
     * 判决文书文号
     */

    private String pjwswh;

    /**
     * 判决日期
     */

    private String pjrq;

    /**
     * 判决罪名
     */

    private String pjzm;

    /**
     * 判决其他罪名
     */

    private String pjqtzm;

    /**
     * 主刑
     */
    private String zx;

    /**
     * 原判刑期
     */

    private String ypxq;

    /**
     * 原刑期起日
     */

    private String yxqqr;

    /**
     * 原刑期止日
     */

    private String yxqzr;

    /**
     * 附加刑
     */

    private String fjx;

    /**
     * 附加刑具体情况
     */

    private String fjxjtqk;

    /**
     * 缓刑考验期
     */

    private String hxkyq;

    /**
     * 财产性判项
     */

    private String ccxpx;

    /**
     * 罚金金额（万元）
     */

    private String fjjewy;

    /**
     * 没收财产金额（万元）
     */

    private String msccjewy;

    /**
     * 其他财产性判项金额（万元）
     */

    private String qtccxpxjewy;

    /**
     * 剥夺政治权利期限
     */

    private String bdzzqlqx;

    /**
     * 未能入矫原因
     **/
    private String wnrjyy;
    /**罪犯编号*/
    private String zfbh;
}
