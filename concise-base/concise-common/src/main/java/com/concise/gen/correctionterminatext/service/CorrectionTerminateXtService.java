package com.concise.gen.correctionterminatext.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionterminatext.entity.CorrectionTerminateXt;
import com.concise.gen.correctionterminatext.param.CorrectionTerminateXtParam;
import com.concise.gen.correctionterminatext.param.TerminateVo;
import com.concise.gen.dataCenter.correctionterminate.entity.CorrectionTerminate;

/**
 * 解矫协同信息service接口
 *
 * <AUTHOR>
 * @date 2022-06-17 15:08:39
 */
public interface CorrectionTerminateXtService extends IService<CorrectionTerminateXt> {

    /**
     * 查询解矫协同信息
     *
     * <AUTHOR>
     * @date 2022-06-17 15:08:39
     */
    PageResult<CorrectionTerminateXt> page(CorrectionTerminateXtParam correctionTerminateXtParam);

    /**
     * 编辑解矫协同信息
     *
     * <AUTHOR>
     * @date 2022-06-17 15:08:39
     */
    void edit(CorrectionTerminateXt correctionTerminateXtParam);


    TerminateVo add(CorrectionTerminate correctionTerminate);
}
