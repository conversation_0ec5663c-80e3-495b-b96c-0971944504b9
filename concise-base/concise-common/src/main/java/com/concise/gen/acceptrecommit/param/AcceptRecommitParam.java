package com.concise.gen.acceptrecommit.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 公安再犯罪协同接收表参数类
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptRecommitParam extends BaseParam {


    private List<AcceptCorrectionDocParam> docList;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     *
     */
    @NotBlank(message = "不能为空，请检查taskId参数", groups = {add.class, edit.class})
    private String taskId;

    /**
     * 接收单位
     */
    @NotBlank(message = "接收单位不能为空，请检查jsdw参数", groups = {add.class, edit.class})
    private String jsdw;

    /**
     * 接收单位名称
     */
    @NotBlank(message = "接收单位名称不能为空，请检查jsdwmc参数", groups = {add.class, edit.class})
    private String jsdwmc;

    /**
     * 推送单位
     */
    @NotBlank(message = "推送单位不能为空，请检查tsdw参数", groups = {add.class, edit.class})
    private String tsdw;

    /**
     * 推送单位名称
     */
    @NotBlank(message = "推送单位名称不能为空，请检查tsdwmc参数", groups = {add.class, edit.class})
    private String tsdwmc;
    /**推送时间*/
    private String tssj;

    /**
     * 送达时间
     */
    @NotNull(message = "送达时间不能为空，请检查sdsj参数", groups = {add.class, edit.class})
    private String sdsj;

    /**
     * 统一赋号
     */
    @NotBlank(message = "统一赋号不能为空，请检查tyfh参数", groups = {add.class, edit.class})
    private String tyfh;

    /**
     * 社区矫正案件编号
     **/
    private String sqjzajbh;
    /**
     * 公安案件编号
     **/
    private String gaajbh;

    /**
     * 嫌疑人编号
     */
    @NotBlank(message = "嫌疑人编号不能为空，请检查xyrbh参数", groups = {add.class, edit.class})
    private String xyrbh;

    /**
     * 案件类型
     */
    @NotBlank(message = "案件类型不能为空，请检查ajlx参数", groups = {add.class, edit.class})
    private String ajlx;
    private String ajlxmc;

    /**
     * 措施类型
     */
    @NotBlank(message = "措施类型不能为空，请检查cslx参数", groups = {add.class, edit.class})
    private String cslx;
    private String cslxmc;

    /**
     * 措施内容
     */
    @NotBlank(message = "措施内容不能为空，请检查csnr参数", groups = {add.class, edit.class})
    private String csnr;

    /**
     * 办案单位
     */
    @NotBlank(message = "办案单位不能为空，请检查badw参数", groups = {add.class, edit.class})
    private String badw;

    /**
     * 办案单位名称
     */
    @NotBlank(message = "办案单位名称不能为空，请检查badwmc参数", groups = {add.class, edit.class})
    private String badwmc;

    /**
     * 主办人
     */
    @NotBlank(message = "主办人不能为空，请检查zbr参数", groups = {add.class, edit.class})
    private String zbr;

    /**
     * 主办人联系电话
     */
    @NotBlank(message = "主办人联系电话不能为空，请检查zbrlxdh参数", groups = {add.class, edit.class})
    private String zbrlxdh;

    /**
     * 社区矫正对象编号
     */
    @NotBlank(message = "社区矫正对象编号不能为空，请检查sqjzdxbh参数", groups = {add.class, edit.class})
    private String sqjzdxbh;

    /**
     * 矫正单位
     */
    @NotBlank(message = "矫正单位不能为空，请检查jzdw参数", groups = {add.class, edit.class})
    private String jzdw;

    /**
     * 矫正单位名称
     */
    @NotBlank(message = "矫正单位名称不能为空，请检查jzdwmc参数", groups = {add.class, edit.class})
    private String jzdwmc;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空，请检查zjlx参数", groups = {add.class, edit.class})
    private String zjlx;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空，请检查zjhm参数", groups = {add.class, edit.class})
    private String zjhm;

    /**
     * 矫正类别
     */
    @NotBlank(message = "矫正类别不能为空，请检查jzlb参数", groups = {add.class, edit.class})
    private String jzlb;

    /**
     * 执行地县级人民检察院
     */
    @NotBlank(message = "执行地县级人民检察院不能为空，请检查qxjcy参数", groups = {add.class, edit.class})
    private String qxjcy;

    /**
     * 矫正开始时间
     */
    @NotNull(message = "矫正开始时间不能为空，请检查sqjzkssj参数", groups = {add.class, edit.class})
    private String sqjzkssj;

    /**
     * 矫正结束时间
     */
    @NotNull(message = "矫正结束时间不能为空，请检查sqjzjssj参数", groups = {add.class, edit.class})
    private String sqjzjssj;

    /**
     * 现住地
     */
    @NotBlank(message = "现住地不能为空，请检查xzd参数", groups = {add.class, edit.class})
    private String xzd;

    /**
     * 现住地详址
     */
    @NotBlank(message = "现住地详址不能为空，请检查xzdxz参数", groups = {add.class, edit.class})
    private String xzdxz;

    /**
     * 判决日期
     */
    @NotNull(message = "判决日期不能为空，请检查pjrq参数", groups = {add.class, edit.class})
    private String pjrq;

    /**
     * 判决罪名
     */
    @NotBlank(message = "判决罪名不能为空，请检查pjzm参数", groups = {add.class, edit.class})
    private String pjzm;

    /**
     * 原判刑期
     */
    @NotBlank(message = "原判刑期不能为空，请检查ypxq参数", groups = {add.class, edit.class})
    private String ypxq;

    /**
     * 矫正单位联系人
     */
    @NotBlank(message = "矫正单位联系人不能为空，请检查jzjglxr参数", groups = {add.class, edit.class})
    private String jzjglxr;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空，请检查jzjglxrdh参数", groups = {add.class, edit.class})
    private String jzjglxrdh;

    /**
     * 文书号
     */
    private String wsh;

    /**
     * 文书名称
     */
    private String wsmc;

    /**
     * 上级矫正单位id
     */
    private String sjjzdw;

    /**
     * 上级矫正单位名称
     */
    private String sjjzdwmc;

    /**
     * 反馈日期，年-月-日
     */
    private String fkrq;

    /**
     * 操作时间
     */
    private Date czsj;
    /**
     * 操作人
     */
    private String czr;

    /**
     * 备注说明
     */
    private String bzsm;

    /**
     * 通知回执文书附件id集合，用逗号隔开
     */
    @ApiModelProperty(value = "通知回执文书附件id集合，用逗号隔开")
    private String hzws;

    /**
     * 反馈状态，0-未反馈，1-已反馈
     */
    private Integer feedbackStatus;

    /**
     * 是否手动上传
     */
    private boolean manualUpload;
}
