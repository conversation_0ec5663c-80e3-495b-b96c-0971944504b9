package com.concise.gen.acceptcorrectionaccomplice.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 矫正对象同案犯信息信息接收表参数类
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:23
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptCorrectionAccompliceParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     *
     */
    private String contactId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 证件类型
     */
    @NotBlank(message = "证件类型不能为空，请检查zjlx参数", groups = {add.class, edit.class})
    private String zjlx;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空，请检查zjhm参数", groups = {add.class, edit.class})
    private String zjhm;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空，请检查xb参数", groups = {add.class, edit.class})
    private String xb;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空，请检查csrq参数", groups = {add.class, edit.class})
    private String csrq;

    /**
     * 罪名
     */
    @NotBlank(message = "罪名不能为空，请检查zm参数", groups = {add.class, edit.class})
    private String zm;
    /**
     * 刑种
     */
    private String xz;
    /**
     * 刑期
     */
    private String xq;

    /**
     * 被判处刑罚及所在监所
     */
    @NotBlank(message = "被判处刑罚及所在监所不能为空，请检查bpcxfjszjs参数", groups = {add.class, edit.class})
    private String bpcxfjszjs;

}
