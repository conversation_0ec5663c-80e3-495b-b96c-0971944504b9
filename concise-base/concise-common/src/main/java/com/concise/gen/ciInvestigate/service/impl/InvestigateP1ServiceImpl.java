package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP1;
import com.concise.gen.ciInvestigate.enums.InvestigateP1ExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP1Mapper;
import com.concise.gen.ciInvestigate.param.InvestigateP1Param;
import com.concise.gen.ciInvestigate.service.InvestigateP1Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 迁入调查评估service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:13
 */
@Service
public class InvestigateP1ServiceImpl extends ServiceImpl<InvestigateP1Mapper, InvestigateP1> implements InvestigateP1Service {

    @Override
    public PageResult<InvestigateP1> page(InvestigateP1Param investigateP1Param) {
        QueryWrapper<InvestigateP1> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP1Param)) {

            // 根据状态 查询
            if (ObjectUtil.isNotEmpty(investigateP1Param.getZt())) {
                queryWrapper.lambda().eq(InvestigateP1::getZt, investigateP1Param.getZt());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP1> list(InvestigateP1Param investigateP1Param) {
        return this.list();
    }

    @Override
    public void add(InvestigateP1Param investigateP1Param) {
        InvestigateP1 investigateP1 = new InvestigateP1();
        BeanUtil.copyProperties(investigateP1Param, investigateP1);
        this.saveOrUpdate(investigateP1);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP1Param investigateP1Param) {
        this.removeById(investigateP1Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP1Param investigateP1Param) {
        InvestigateP1 investigateP1 = this.queryInvestigateP1(investigateP1Param);
        BeanUtil.copyProperties(investigateP1Param, investigateP1);
        this.updateById(investigateP1);
    }

    @Override
    public InvestigateP1 detail(InvestigateP1Param investigateP1Param) {
        return this.queryInvestigateP1(investigateP1Param);
    }

    /**
     * 获取迁入调查评估
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:13
     */
    private InvestigateP1 queryInvestigateP1(InvestigateP1Param investigateP1Param) {
        InvestigateP1 investigateP1 = this.getById(investigateP1Param.getId());
        if (ObjectUtil.isNull(investigateP1)) {
            throw new ServiceException(InvestigateP1ExceptionEnum.NOT_EXIST);
        }
        return investigateP1;
    }
}
