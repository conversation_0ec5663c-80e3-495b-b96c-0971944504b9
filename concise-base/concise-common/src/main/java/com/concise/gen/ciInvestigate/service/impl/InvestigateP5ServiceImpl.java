package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP5;
import com.concise.gen.ciInvestigate.enums.InvestigateP5ExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP5Mapper;
import com.concise.gen.ciInvestigate.param.InvestigateP5Param;
import com.concise.gen.ciInvestigate.service.InvestigateP5Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 迁入调查评估_初审集体评议service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:21
 */
@Service
public class InvestigateP5ServiceImpl extends ServiceImpl<InvestigateP5Mapper, InvestigateP5> implements InvestigateP5Service {

    @Override
    public PageResult<InvestigateP5> page(InvestigateP5Param investigateP5Param) {
        QueryWrapper<InvestigateP5> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP5Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPid())) {
                queryWrapper.lambda().eq(InvestigateP5::getPid, investigateP5Param.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getZt())) {
                queryWrapper.lambda().eq(InvestigateP5::getZt, investigateP5Param.getZt());
            }
            // 根据初审人 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getCsr())) {
                queryWrapper.lambda().eq(InvestigateP5::getCsr, investigateP5Param.getCsr());
            }
            // 根据初审时间 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getCssj())) {
                queryWrapper.lambda().eq(InvestigateP5::getCssj, investigateP5Param.getCssj());
            }
            // 根据初审意见 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getCsyj())) {
                queryWrapper.lambda().eq(InvestigateP5::getCsyj, investigateP5Param.getCsyj());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getThly())) {
                queryWrapper.lambda().eq(InvestigateP5::getThly, investigateP5Param.getThly());
            }
            // 根据评议审核事项 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPyshsx())) {
                queryWrapper.lambda().eq(InvestigateP5::getPyshsx, investigateP5Param.getPyshsx());
            }
            // 根据主持人 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getZcr())) {
                queryWrapper.lambda().eq(InvestigateP5::getZcr, investigateP5Param.getZcr());
            }
            // 根据评议审核地点 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPyshdd())) {
                queryWrapper.lambda().eq(InvestigateP5::getPyshdd, investigateP5Param.getPyshdd());
            }
            // 根据评议审核时间 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPyshsj())) {
                queryWrapper.lambda().eq(InvestigateP5::getPyshsj, investigateP5Param.getPyshsj());
            }
            // 根据评议审核人员(jsonarray) 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPyshry())) {
                queryWrapper.lambda().eq(InvestigateP5::getPyshry, investigateP5Param.getPyshry());
            }
            // 根据记录人 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getJlr())) {
                queryWrapper.lambda().eq(InvestigateP5::getJlr, investigateP5Param.getJlr());
            }
            // 根据评议审核情况 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPyshqk())) {
                queryWrapper.lambda().eq(InvestigateP5::getPyshqk, investigateP5Param.getPyshqk());
            }
            // 根据评议审核意见 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getPyshyj())) {
                queryWrapper.lambda().eq(InvestigateP5::getPyshyj, investigateP5Param.getPyshyj());
            }
            // 根据负责人 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getFzr())) {
                queryWrapper.lambda().eq(InvestigateP5::getFzr, investigateP5Param.getFzr());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(investigateP5Param.getBz())) {
                queryWrapper.lambda().eq(InvestigateP5::getBz, investigateP5Param.getBz());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP5> list(InvestigateP5Param investigateP5Param) {
        return this.list();
    }

    @Override
    public void add(InvestigateP5Param investigateP5Param) {
        InvestigateP5 investigateP5 = new InvestigateP5();
        BeanUtil.copyProperties(investigateP5Param, investigateP5);
        this.save(investigateP5);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP5Param investigateP5Param) {
        this.removeById(investigateP5Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP5Param investigateP5Param) {
        InvestigateP5 investigateP5 = this.queryInvestigateP5(investigateP5Param);
        BeanUtil.copyProperties(investigateP5Param, investigateP5);
        this.updateById(investigateP5);
    }

    @Override
    public InvestigateP5 detail(InvestigateP5Param investigateP5Param) {
        return this.queryInvestigateP5(investigateP5Param);
    }

    /**
     * 获取迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    private InvestigateP5 queryInvestigateP5(InvestigateP5Param investigateP5Param) {
        InvestigateP5 investigateP5 = this.getById(investigateP5Param.getId());
        if (ObjectUtil.isNull(investigateP5)) {
            throw new ServiceException(InvestigateP5ExceptionEnum.NOT_EXIST);
        }
        return investigateP5;
    }
}
