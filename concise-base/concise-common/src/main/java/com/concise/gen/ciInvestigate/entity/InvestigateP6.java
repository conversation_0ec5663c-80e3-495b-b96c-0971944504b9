package com.concise.gen.ciInvestigate.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

/**
 * 迁入调查评估_审批
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:23
 */
@Data
@TableName("correction_immigration_investigate_p6")
public class InvestigateP6{
    private Date createTime;
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 调查步骤id
     */
    private String zt;

    /**
     * 审批人
     */
    private String spr;

    /**
     * 审批时间
     */
    @Excel(name = "审批时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date spsj;

    /**
     * 审批意见
     */
    private String spyj;

    /**
     * 退回理由
     */
    private String thly;

    /**
     * 评估意见
     */
    private String pgyj;

    /**
     * 调查结束时间
     */
    @Excel(name = "调查结束时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pyshsj;

    /**
     * 调查评估意见
     */
    private String dcpgyj;

}
