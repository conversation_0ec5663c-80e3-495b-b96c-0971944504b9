package com.concise.gen.acceptreturninfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 数据质检记录表
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:07
 */
@Data
@TableName("accept_return_info")
public class AcceptReturnInfo {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 关联项id
     */
    private String contactId;
    /**
     * 数据类别
     *(协同编号)
     */
    private String type;
    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 矫正单位
     */
    private String jsdw;
    private String jsdwId;
    private String jsdwPids;

    private String jsdwmc;

    /**
     * 退回类型
     */
    private String thlx;

    /**
     * 退回原因
     */
    private String thyy;

    /**
     * 退回时间
     */
    private Date thsj;

    /**
     * 推送时间
     */
    @TableField(value = "jssj")
    private Date tssj;

}
