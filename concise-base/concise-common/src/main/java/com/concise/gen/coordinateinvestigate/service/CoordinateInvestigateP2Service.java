package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP2;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP2Param;

import java.util.List;

/**
 * 调查评估协查_接收service接口
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:51
 */
public interface CoordinateInvestigateP2Service extends IService<CoordinateInvestigateP2> {

    /**
     * 查询调查评估协查_接收
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
    PageResult<CoordinateInvestigateP2> page(CoordinateInvestigateP2Param coordinateInvestigateP2Param);

    /**
     * 调查评估协查_接收列表
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
    List<CoordinateInvestigateP2> list(CoordinateInvestigateP2Param coordinateInvestigateP2Param);

    /**
     * 添加调查评估协查_接收
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
    void add(CoordinateInvestigateP2Param coordinateInvestigateP2Param);

    /**
     * 删除调查评估协查_接收
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
    void delete(CoordinateInvestigateP2Param coordinateInvestigateP2Param);

    /**
     * 编辑调查评估协查_接收
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
    void edit(CoordinateInvestigateP2Param coordinateInvestigateP2Param);

    /**
     * 查看调查评估协查_接收
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
     CoordinateInvestigateP2 detail(CoordinateInvestigateP2Param coordinateInvestigateP2Param);
}
