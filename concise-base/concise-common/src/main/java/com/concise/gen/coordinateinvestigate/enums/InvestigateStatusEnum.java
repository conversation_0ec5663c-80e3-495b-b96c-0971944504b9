package com.concise.gen.coordinateinvestigate.enums;

/**
 * <AUTHOR>
 */

public enum InvestigateStatusEnum {

    /**
     * 调查评估协查状态
     * 0    (待受理)
     * 1    待指派
     * 2    待接收
     * 3    待调查
     * 4    待初审小组意见
     * 5    待初审集体评议
     * 6    待审批
     * 7    待反馈
     * 8    已反馈
     * 9    退回
     */
    P1("1", "待指派")
    ,P2("2", "待接收")
    ,P3("3", "待调查")
    ,P3_1("31", "待填写调查过程")
    ,P3_2("32", "待填写调查评估表")
    ,P3_3("33", "待填写调查评估意见书")
    ,P4("4", "待初审小组意见")
    ,P5("5", "待初审集体评议")
    ,P6("6", "待审批")
    ,P7("7", "待反馈")
    ,END("8", "已反馈")
    ,REJECT("9", "退回");

    private final String code;

    private final String message;

    InvestigateStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }
}
