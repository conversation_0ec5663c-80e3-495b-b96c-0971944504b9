package com.concise.gen.acceptcorrectionaccomplice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionaccomplice.entity.AcceptCorrectionAccomplice;
import com.concise.gen.acceptcorrectionaccomplice.param.AcceptCorrectionAccompliceParam;
import java.util.List;

/**
 * 矫正对象同案犯信息信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:23
 */
public interface AcceptCorrectionAccompliceService extends IService<AcceptCorrectionAccomplice> {

    /**
     * 查询矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    PageResult<AcceptCorrectionAccomplice> page(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam);

    /**
     * 矫正对象同案犯信息信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    List<AcceptCorrectionAccomplice> list(String contactId);

    /**
     * 添加矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    void add(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam);

    /**
     * 删除矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    void delete(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam);

    /**
     * 编辑矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    void edit(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam);

    /**
     * 查看矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
     AcceptCorrectionAccomplice detail(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam);
}
