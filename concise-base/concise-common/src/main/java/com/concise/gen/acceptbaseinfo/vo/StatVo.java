package com.concise.gen.acceptbaseinfo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/30
 * 数据统计vo
 */
@NoArgsConstructor
@Data
public class StatVo {

    @Excel(name = "矫正单位", width = 20,orderNum = "1")
    private String orgName;
    private String orgId;
    @Excel(name = "接收总计", width = 20,orderNum = "5")
    private Integer zt1;
    @Excel(name = "待接收", width = 20,orderNum = "4")
    private Integer zt2;
    @Excel(name = "拒接收", width = 20,orderNum = "3")
    private Integer zt3;
    @Excel(name = "待反馈", width = 20,orderNum = "6")
    private Integer zt4;
    @Excel(name = "已反馈", width = 20,orderNum = "7")
    private Integer zt5;
    @Excel(name = "已接收", width = 20,orderNum = "2")
    private Integer ca1;
    @Excel(name = "反馈总计", width = 20,orderNum = "8")
    private Integer ca2;
    public Integer getCa1() {
        return zt1-zt2-zt3;
    }
    public Integer getCa2() {
        return zt4+zt5;
    }
    public StatVo(String name){
        orgName = name;
        orgId = "0";
        zt1 = 0;
        zt2 = 0;
        zt3 = 0;
        zt4 = 0;
        zt5 = 0;
    }
}
