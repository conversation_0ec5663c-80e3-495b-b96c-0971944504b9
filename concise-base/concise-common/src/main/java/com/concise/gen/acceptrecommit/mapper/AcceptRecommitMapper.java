package com.concise.gen.acceptrecommit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.gen.acceptrecommit.entity.AcceptRecommit;
import com.concise.gen.acceptrecommit.param.SendLogExport;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 公安再犯罪协同接收表
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
public interface AcceptRecommitMapper extends BaseMapper<AcceptRecommit> {
    /**
     * 发送日志
     * @param id 矫正人员id
     * @param xm xm
     * @param jzjgId 机构id
     * @param jzjgName 机构
     * @param jsdw 接收单位
     * @param zhuangtai 矫正状态
     */
    void sendLog(String id, String xm, String jzjgId, String jzjgName, String jsdw, String zhuangtai);

    void deleteLog(@Param("psnId") String psnId);
    /**
     * 日志列表
     * @param page page
     * @param id 矫正人员id
     * @param fsrq yyyy-MM-dd
     * @param xm xm
     * @param jzjgSet 机构id
     * @return List
     */
    Page<Map<String, Object>> logPage(@Param("page") Page page,
                                      @Param("id") String id,
                                      @Param("fsrq") String fsrq,
                                      @Param("xm") String xm,
                                      @Param("deleted") int deleted,
                                      @Param("jzjgSet") Set<String> jzjgSet);
    /**
     * 导出日志
     * @param xm xm
     * @param jzjgSet jzjg
     * @return List
     * */
    List<SendLogExport> logExport(@Param("xm") String xm, @Param("jzjgSet") Set<String> jzjgSet);

    String getMaxNumberByYear(String pattern);
}
