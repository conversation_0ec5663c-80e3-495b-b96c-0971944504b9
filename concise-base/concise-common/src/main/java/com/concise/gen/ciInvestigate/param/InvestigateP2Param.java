package com.concise.gen.ciInvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 迁入调查评估_接收参数类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:15
*/
@Data
public class InvestigateP2Param extends BaseParam {
    private Date createTime;
    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 接收状态
     */
    @NotBlank(message = "接收状态不能为空，请检查zt参数", groups = {add.class, edit.class})
    private String zt;

    /**
     * 接收人
     */
    @NotBlank(message = "接收人不能为空，请检查jsr参数", groups = {add.class, edit.class})
    private String jsr;

    /**
     * 接收时间
     */
    @NotNull(message = "接收时间不能为空，请检查jssj参数", groups = {add.class, edit.class})
    private String jssj;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空，请检查bz参数", groups = {add.class, edit.class})
    private String bz;

    /**
     * 退回理由
     */
    @NotBlank(message = "退回理由不能为空，请检查thly参数", groups = {add.class, edit.class})
    private String thly;

}
