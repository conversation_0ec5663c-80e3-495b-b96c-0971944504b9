<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.acceptinvestinfo.mapper.AcceptInvestInfoMapper">
    <select id="statisticsLevel1" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select `name`      as org_name
             , id          as org_id
             , sum(t2.zt1) as zt1
             , sum(t2.zt2) as zt2
             , sum(t2.zt3) as zt3
             , sum(t2.zt4) as zt4
             , sum(t2.zt5) as zt5
        from (select pid, temp.*
              from (select jsdw_id
                         , count(1)                            as zt1
                         , sum(if(zt = '0', 1, 0))             as zt2
                         , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                         , sum(if(zt = '1', 1, 0))             as zt4
                         , sum(if(zt = '4', 1, 0))             as zt5
                    from accept_invest_info
                    where zt != '99'
                    <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                    <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
                    group by jsdw_id) temp
                       left join sys_org on jsdw_id = id) t2
                 left join sys_org on t2.pid = id
        where `level` = 2
        group by id;
    </select>
    <select id="statisticsLevel2" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select `name` as org_name
             , temp.*
        from (select jsdw_id                             as org_id
                   , count(1)                            as zt1
                   , sum(if(zt = '0', 1, 0))             as zt2
                   , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                   , sum(if(zt = '1', 1, 0))             as zt4
                   , sum(if(zt = '4', 1, 0))             as zt5
              from accept_invest_info
              where zt != '99'
                  and jsdw_pids like concat('%',#{orgId}, '%')
                    <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                    <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
              group by jsdw_id) temp
                 left join sys_org on org_id = id
            where `level` = 3
    </select>
    <select id="statisticsLevel3" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select `name` as org_name
             , temp.*
        from (select DCDW                                as org_id
                   , count(1)                            as zt1
                   , sum(if(zt = '0', 1, 0))             as zt2
                   , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                   , sum(if(zt = '1', 1, 0))             as zt4
                   , sum(if(zt = '4', 1, 0))             as zt5
              from accept_invest_info
              where zt != '99'
                and jsdw_id = #{orgId}
                <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
              group by DCDW) temp
                 left join sys_org on org_id = id
                where `level` = 4
    </select>
    <select id="statisticsLevel4" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select name as org_name
             , temp.*
        from (select DCDW                                as org_id
                   , count(1)                            as zt1
                   , sum(if(zt = '0', 1, 0))             as zt2
                   , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                   , sum(if(zt = '1', 1, 0))             as zt4
                   , sum(if(zt = '4', 1, 0))             as zt5
              from accept_invest_info
              where zt != '99'
                and DCDW = #{orgId}
                <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
              group by DCDW) temp
                 left join sys_org on org_id = id
        where `level` = 4
    </select>
</mapper>
