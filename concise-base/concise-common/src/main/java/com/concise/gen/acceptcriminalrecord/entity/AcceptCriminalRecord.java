package com.concise.gen.acceptcriminalrecord.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 前科信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:43
 */
@Data
@TableName("accept_criminal_record")
public class AcceptCriminalRecord{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String contactId;

    /**
     * 刑种
     */
    private String xz;

    /**
     * 判决日期
     */
    private String pjrq;

    /**
     * 判决法院
     */
    private String pjfy;

    /**
     * 罪名
     */
    private String zm;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 执行机关
     */
    private String zxjg;

    /**
     * 执行刑期
     */
    private String zxxq;

    /**
     * 备注
     */
    private String bz;

}
