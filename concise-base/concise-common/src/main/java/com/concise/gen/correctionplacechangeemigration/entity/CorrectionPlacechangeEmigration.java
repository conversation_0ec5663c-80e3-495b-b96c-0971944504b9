package com.concise.gen.correctionplacechangeemigration.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import com.concise.common.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 执行地变更_跨省迁出
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_placechange_emigration")
public class CorrectionPlacechangeEmigration extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 社区矫正人员ID
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 执行地变更申请时间
     */
    @Excel(name = "执行地变更申请时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date sqsj;

    /**
     * 迁入地
     */
    private String qrd;

    /**
     * 迁入地明细
     */
    private String qrdmx;

    /**
     * 拟迁入矫正单位
     */
    private String qrjzdw;

    /**
     * 拟迁入矫正单位ID
     */
    private String qrjzdwId;

    /**
     * 变更理由
     */
    private String bgly;

    /**
     * 附件1
     */
    private String file1;
    /**
     * 附件
     */
    @TableField(exist = false)
    private List<SysFileInfoVO> fileList;

    /**
     * 附件2
     */
    private String file2;

    /**
     * 报到状态
     */
    private String bdzt;
    /**
     * 调查评估意见
     */
    private String dcpgyj;
    /**
     * 报道时间
     */
    private Date bdsj;
    /**
     * 矫正单位
     */
    private String jzdwId;
    /**
     * 矫正单位
     */
    private String jzdwmc;

}
