package com.concise.gen.correctionterminatext.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionterminatext.entity.CorrectionTerminateXt;
import com.concise.gen.correctionterminatext.mapper.CorrectionTerminateXtMapper;
import com.concise.gen.correctionterminatext.param.CorrectionTerminateXtParam;
import com.concise.gen.correctionterminatext.param.TerminateVo;
import com.concise.gen.correctionterminatext.service.CorrectionTerminateXtService;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.dataCenter.correctionterminate.entity.CorrectionTerminate;
import com.concise.gen.dataCenter.orgDc.entity.OrgDc;
import com.concise.gen.dataCenter.orgDc.service.OrgDcService;
import com.concise.gen.sysdistinfo.entity.SysDistinfo;
import com.concise.gen.sysdistinfo.service.SysDistinfoService;
import com.concise.gen.webservice.service.SendTerminateService;
import com.concise.gen.webservice.utils.DictWdToHyUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 解矫协同信息service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-17 15:08:39
 */
@Service
@DS("master")
public class CorrectionTerminateXtServiceImpl extends ServiceImpl<CorrectionTerminateXtMapper, CorrectionTerminateXt> implements CorrectionTerminateXtService {

    @Resource
    private CorrectionObjectInformationService correctionObjectInformationService;
    @Resource
    private OrgDcService orgDcService;
    @Resource
    private SysDistinfoService sysDistinfoService;
    @Resource
    private SendTerminateService sendService;

    @Override
    public PageResult<CorrectionTerminateXt> page(CorrectionTerminateXtParam param) {
        QueryWrapper<CorrectionTerminateXt> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(param.getTyfh())) {
                queryWrapper.lambda().eq(CorrectionTerminateXt::getTyfh, param.getTyfh());
            }
            if (param.getOrgSet().size() < 1000) {
                queryWrapper.lambda().in(CorrectionTerminateXt::getJzjgId, param.getOrgSet());
            }
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CorrectionTerminateXt::getXm, param.getXm());
            }
            if (ObjectUtil.isNotEmpty(param.getJcsjzzsjlx())) {
                queryWrapper.lambda().eq(CorrectionTerminateXt::getJcsjzzsjlx, param.getJcsjzzsjlx());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.apply("date_format (send_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime() + "','%Y-%m-%d')")
                        .apply("date_format (send_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime() + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().orderByDesc(CorrectionTerminateXt::getSendTime);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public void edit(CorrectionTerminateXt xt) {
        if (sendService.request3302(xt)) {
            xt.setSendTime(DateUtil.date());
            this.saveOrUpdate(xt);
            System.out.println("send: "+ xt.getXm());
        }
    }

    @Override
    public TerminateVo add(CorrectionTerminate correctionTerminate) {
        CorrectionObjectInformation obj = this.getCorrectionObjectInformationById(correctionTerminate.getId());

        TerminateVo vo = new TerminateVo();
        if (obj == null) {
            return vo;
        }

        CorrectionTerminateXt xt = this.baseMapper.getByTyfh(correctionTerminate.getId());
        if (xt == null) {
            xt = new CorrectionTerminateXt();
        }
        xt.setTyfh(obj.getId());
        xt.setZfbh(obj.getId());

        xt.setXm(obj.getXm());
        xt.setXb(obj.getXb());
        xt.setZjlx("111");
        xt.setZjhm(obj.getSfzh());
        xt.setCsrq(obj.getCsrq());

        SysDistinfo hjd = sysDistinfoService.getById(obj.getHjszxq());
        SysDistinfo xzd = sysDistinfoService.getById(obj.getGdjzdszxq());
        if (hjd != null) {
            xt.setHjd(hjd.getAreacode());
        }
        xt.setHjdxz(obj.getHjszdmx());
        if (xzd != null) {
            xt.setXzd(xzd.getAreacode());
        }
        xt.setXzdxz(obj.getGdjzdmx());

        xt.setFzlx(obj.getFzlx());
        //todo 需要转换
        xt.setJtzm(obj.getJtzm());
        // 万达数据问题现不支持转换
/*        if ("01".equals(obj.getJzlb())) {
            //管制
            xt.setGzqx(DictWdToHyUtils.gzqx(obj.getSqjzksrq(),obj.getSqjzjsrq()));
        }
        if ("02".equals(obj.getJzlb())) {
            //缓刑
            xt.setHxkyqx(DictWdToHyUtils.gzqx(obj.getSqjzksrq(),obj.getSqjzjsrq()));
        }*/

        xt.setSxpjszh(obj.getPjszh());
        xt.setPjwssxrq(obj.getWssxrq());
        xt.setSfszbf(obj.getSfszbf());
        xt.setPjxqksrq(obj.getYpxqkssj());
        xt.setPjxqjsrq(obj.getYpxqjssj());
        xt.setYqtxqx(obj.getYqtxqx());
        xt.setJtfkje(obj.getFj());
        xt.setSfwd(DictWdToHyUtils.sfwd(obj.getSfwd()));
        xt.setSfws(DictWdToHyUtils.sfws(obj.getSfws()));
        xt.setSfyss(DictWdToHyUtils.sfyss(obj.getSfyss()));

        OrgDc orgDc = orgDcService.getById(obj.getJzjg());
        if (orgDc != null) {
            OrgDc pOrg = orgDcService.lambdaQuery().eq(OrgDc::getId, orgDc.getPid()).last(" limit 1").one();
            //发送时直接获取 区县司法局code
            xt.setJzjg(pOrg.getCode());

            xt.setJzjgName(pOrg.getName());
            xt.setJzjgId(orgDc.getId());
            xt.setJzjgPids(orgDc.getPids());
            xt.setSqjzzxd(pOrg.getCode().substring(0, 6));
            if (xt.getSqjzzxd().startsWith("330188")) {
                //钱塘区
                xt.setSqjzzxd("330114");
            }
            vo.setSfsId(obj.getJzjg());
            vo.setQxsfjId(orgDc.getPid());
            vo.setSsfjId(pOrg.getPid());
        }

        xt.setZyjwzxqr(obj.getSqjzksrq());
        xt.setZyjwzxzr(obj.getSqjzjsrq());

        xt.setJzlb(DictWdToHyUtils.jzlb(obj.getJzlb()));
        xt.setSfs(obj.getJzjgName());
        xt.setSqjzksrq(obj.getSqjzksrq());
        xt.setSqjzjsrq(obj.getSqjzjsrq());
        xt.setJzqx(obj.getSqjzqx());


        //除了期满解矫是解除矫正，都是终止社矫
        if ("01".equals(correctionTerminate.getZhuangtai())
            ||"1".equals(correctionTerminate.getZhuangtai())) {
            //01：期满解矫
            xt.setJcsjzzsjlx("1");
            xt.setJcsjzzsjyy("1");
        }else {
            xt.setJcsjzzsjlx("2");
            if ("02".equals(obj.getJzlb())) {
                //缓刑
                xt.setJcsjzzsjyy("2");
                xt.setJdswh(obj.getZxtzswh());
                xt.setCxhxjdrq(obj.getZxtzsrq());
                xt.setCxhxyy(correctionTerminate.getSjzxyyName());
            }
            if ("03".equals(obj.getJzlb())) {
                //假释
                xt.setJcsjzzsjyy("3");
                xt.setJscdrq(obj.getWssxrq());
                xt.setJskyq(obj.getHxkyqx());
                xt.setJskyqqr(obj.getSqjzksrq());
                xt.setJskyqzr(obj.getSqjzjsrq());
                xt.setJscdswh(obj.getZxtzswh());
                xt.setJfzxrq(obj.getJfzxrq());
                xt.setZxtzsrq(obj.getZxtzsrq());
                xt.setJdswh(obj.getZxtzswh());
                xt.setCxjsjdrq(obj.getZxtzsrq());
                xt.setCxjsyy(correctionTerminate.getSjzxyyName());
            }
            if ("01".equals(obj.getJzlb())||"04".equals(obj.getJzlb())) {
                //管制、暂予监外执行
                xt.setJcsjzzsjyy("4");
                xt.setZyjwzxjdrq(obj.getZxtzsrq());
                xt.setZyjwzxjdswh(obj.getZxtzswh());
                xt.setJfzxrq(obj.getJfzxrq());
                xt.setJdswh(obj.getZxtzswh());
                xt.setSjjdrq(obj.getZxtzsrq());
                xt.setSjyy(correctionTerminate.getSjzxyyName());
            }
            if ("08".equals(obj.getJzlb())) {
                //特赦解矫
                xt.setJcsjzzsjyy("6");
            }
        }

        xt.setTzsrq(correctionTerminate.getZhongzhiriqi());
        xt.setRjrq(obj.getRujiaoriqi());
        xt.setJcsjzzsjrq(correctionTerminate.getZhongzhiriqi());


        xt.setSfsc("2");
        xt.setCjsj(obj.getLastModifiedTime());
        xt.setGxsj(correctionTerminate.getLastModifiedTime());

        if (ObjectUtil.isEmpty(xt.getId())) {
            xt.setId(IdUtil.fastSimpleUUID());
            xt.setSqjzajbh(IdUtil.fastSimpleUUID());
            xt.setSendTime(DateUtil.date());
            vo.setSuccess(sendService.request3302(xt));
        }else {
            vo.setSuccess(false);
        }
        this.saveOrUpdate(xt);
        return vo;
    }

    private CorrectionObjectInformation getCorrectionObjectInformationById(String id){
        return correctionObjectInformationService.getById(id);
    }
}
