package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigate;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateParam;
import java.util.List;

/**
 * 调查评估协查service接口
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
public interface CoordinateInvestigateService extends IService<CoordinateInvestigate> {

    /**
     * 查询调查评估协查
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
    PageResult<CoordinateInvestigate> page(CoordinateInvestigateParam coordinateInvestigateParam);

    /**
     * 调查评估协查列表
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
    List<CoordinateInvestigate> list(CoordinateInvestigateParam coordinateInvestigateParam);

    /**
     * 添加调查评估协查
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
    void add(CoordinateInvestigateParam coordinateInvestigateParam);

    /**
     * 删除调查评估协查
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
    void delete(CoordinateInvestigateParam coordinateInvestigateParam);

    /**
     * 编辑调查评估协查
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
    void edit(CoordinateInvestigateParam coordinateInvestigateParam);

    /**
     * 查看调查评估协查
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
     CoordinateInvestigate detail(CoordinateInvestigateParam coordinateInvestigateParam);
}
