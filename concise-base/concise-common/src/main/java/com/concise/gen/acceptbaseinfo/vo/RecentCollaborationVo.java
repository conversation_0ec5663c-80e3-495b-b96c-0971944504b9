package com.concise.gen.acceptbaseinfo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/4
 */
@Data
public class RecentCollaborationVo {
    /**
     * 协同类型
     */
    private String type;
    /**
     * 姓名
     */
    private String name;

    /**
     * 发起省市
     */
    private String fqss;
    /**
     * 接收省市
     */
    private String jsss;
    /**
     * 状态
     */
    private String zt;

    /**
     * 用来排序的时间
     */
    private Date sortTime;
}
