package com.concise.gen.correctionplacechangeimmigration.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.correctionplacechangeimmigration.entity.CorrectionPlacechangeImmigration;
import com.concise.gen.correctionplacechangeimmigration.param.CorrectionPlacechangeImmigrationParam;
import java.util.List;

/**
 * 执行地变更_外省迁入service接口
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
public interface CorrectionPlacechangeImmigrationService extends IService<CorrectionPlacechangeImmigration> {

    /**
     * 查询执行地变更_外省迁入
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
    PageResult<CorrectionPlacechangeImmigration> page(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam);

    /**
     * 执行地变更_外省迁入列表
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
    List<CorrectionPlacechangeImmigration> list(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam);

    /**
     * 添加执行地变更_外省迁入
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
    void add(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam);

    /**
     * 删除执行地变更_外省迁入
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
    void delete(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam);

    /**
     * 编辑执行地变更_外省迁入
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
    void edit(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam);

    /**
     * 查看执行地变更_外省迁入
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:53
     */
     CorrectionPlacechangeImmigration detail(CorrectionPlacechangeImmigrationParam correctionPlacechangeImmigrationParam);
}
