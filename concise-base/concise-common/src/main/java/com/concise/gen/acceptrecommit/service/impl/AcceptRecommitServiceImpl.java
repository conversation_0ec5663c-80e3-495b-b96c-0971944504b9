package com.concise.gen.acceptrecommit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import cn.idev.excel.FastExcel;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.config.FileConfig;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.file.util.OssBootUtil;
import com.concise.common.pojo.PdfModalVo;
import com.concise.common.pojo.base.enums.OrgTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.*;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptrecommit.entity.AcceptRecommit;
import com.concise.gen.acceptrecommit.enums.AcceptRecommitExceptionEnum;
import com.concise.gen.acceptrecommit.mapper.AcceptRecommitMapper;
import com.concise.gen.acceptrecommit.param.AcceptRecommitParam;
import com.concise.gen.acceptrecommit.param.SendLogExport;
import com.concise.gen.acceptrecommit.service.AcceptRecommitService;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.dataCenter.correctionobjectinformation.service.CorrectionObjectInformationService;
import com.concise.gen.dataCenter.recommit.service.AcceptRecommitDcService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.gen.signaturemaintenance.service.SignatureMaintenanceService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.utils.OCRToolsUtil;
import com.concise.gen.webservice.utils.EbcpRequestUtil;
import com.concise.gen.ywxtcount.service.YwxtCountService;
import com.concise.gen.ywxtsendmsglog.service.YwxtSendMsgLogService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 公安再犯罪协同接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
@Service
public class AcceptRecommitServiceImpl extends ServiceImpl<AcceptRecommitMapper, AcceptRecommit> implements AcceptRecommitService {

    private static final Log log = Log.get();
    @Resource
    private AcceptRecommitDcService recommitDcService;
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private CorrectionObjectInformationService objService;
    @Resource
    private OrgCommonService orgService;
    @Resource
    private YwxtNoticeService ywxtNoticeService;
    @Resource
    private YwxtCountService ywxtCountService;
    @Resource
    private AcceptReturnInfoService acceptReturnInfoService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private SignatureMaintenanceService signatureMaintenanceService;
    @Resource
    private YwxtSendMsgLogService ywxtSendMsgLogService;

    @Value("${sqjzOss.endpoint}")
    private String endPoint;
    @Value("${sqjzOss.accessKey}")
    private String accessKeyId;
    @Value("${sqjzOss.secretKey}")
    private String accessKeySecret;
    @Value("${sqjzOss.bucketName}")
    private String bucketName;
    @Value("${sqjzOss.downloadFile}")
    private boolean downloadFile;
    @Value("${tessDataPath}")
    private String tessDataPath;
    @Value("${electronicSignature.modalPath}")
    private String modalPath;
    @Value("${electronicSignature.fontPath}")
    private String fontPath;
    @Value("${sjxt.msgUrl}")
    private String msgUrl;

    @Resource
    private ElectronicSignatureUtil electronicSignatureUtil;


    @Override
    public PageResult<AcceptRecommit> page(AcceptRecommitParam param) {
        QueryWrapper<AcceptRecommit> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(AcceptRecommit::getXm, param.getXm().trim());
            }
            // 根据案件类型 查询
            if (ObjectUtil.isNotEmpty(param.getAjlx())) {
                queryWrapper.lambda().eq(AcceptRecommit::getAjlx, param.getAjlx());
            }
            // 根据措施类型 查询
            if (ObjectUtil.isNotEmpty(param.getCslx())) {
                queryWrapper.lambda().eq(AcceptRecommit::getCslx, param.getCslx());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getTaskId())) {
                queryWrapper.lambda().eq(AcceptRecommit::getTaskId, param.getTaskId());
            }
            // 根据推送单位 查询
            if (ObjectUtil.isNotEmpty(param.getTsdw())) {
                queryWrapper.lambda().eq(AcceptRecommit::getTsdw, param.getTsdw());
            }
            // 根据矫正单位 查询
            if (param.getOrgSet().size() < 1000) {
                queryWrapper.lambda().in(AcceptRecommit::getJzdw, param.getOrgSet());
            }
            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(param.getJzlb())) {
                queryWrapper.lambda().eq(AcceptRecommit::getJzlb, param.getJzlb());
            }
            if (ObjectUtil.isNotEmpty(param.getFeedbackStatus())) {
                queryWrapper.lambda().eq(AcceptRecommit::getFeedbackStatus, param.getFeedbackStatus());
            }
        }
        queryWrapper.lambda().eq(AcceptRecommit::getDeleted, 0).orderByDesc(AcceptRecommit::getSdsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptRecommitParam param) {

        AcceptRecommit one = this.lambdaQuery()
                .eq(AcceptRecommit::getGaajbh, param.getGaajbh())
                .eq(AcceptRecommit::getSqjzajbh, param.getSqjzajbh())
                .eq(AcceptRecommit::getDeleted, 0)
                .orderByDesc(AcceptRecommit::getSdsj)
                .last("limit 1")
                .one();
        CorrectionObjectInformation byId = objService.getById(param.getSqjzajbh());
        if (one != null && one.getSdsj() != null) {
            List<AcceptCorrectionDoc> oldDocList = acceptCorrectionDocService.list(one.getId());
            OrgCommon org = orgService.getOrgByCode(one.getJzdw());
            Boolean check = acceptReturnInfoService.check(one.getSdsj(), param.getDocList(), oldDocList);
            if (check) {
                acceptReturnInfoService.save("XTBH44002", param.getId(), param.getTyfh(), DateUtil.parse(param.getTssj()), one.getSdsj(), one.getXm(), org.getCode(), org.getId(), org.getPids(), org.getName());
                ywxtSendMsgLogService.save(EbcpRequestUtil.sendMessage(4,param.getTyfh(),param.getTaskId(),"XTBH44002",param.getGaajbh(),"",param.getTsdw(),param.getJsdw(),"",
                        "你好，【"+param.getTsdwmc()+"】【"+param.getGaajbh()+"】【公安再犯接收】协同业务已于【"
                                +DateUtil.formatChineseDate(DateUtil.date(),false,true)+"】被【浙江省司法厅】退回，退回原因是【数据重复】。",
                        msgUrl));
                return null;
            }
        }
        ywxtSendMsgLogService.save(EbcpRequestUtil.sendMessage(3,param.getTyfh(),param.getTaskId(),"XTBH44002",param.getGaajbh(),"",param.getTsdw(),param.getJsdw(),"",
                "你好，【"+param.getTsdwmc()+"】【"+param.getGaajbh()+"】【公安再犯接收】协同业务已于【"
                        +DateUtil.formatChineseDate(DateUtil.date(),false,true)+"】被【浙江省司法厅】受理。",
                msgUrl));


        AcceptRecommit acceptRecommit = new AcceptRecommit();
        BeanUtil.copyProperties(param, acceptRecommit);
        if (byId != null) {
            acceptRecommit.setXm(byId.getXm());
            acceptRecommit.setXb(byId.getXbName());
            acceptRecommit.setSqjzdxbh(byId.getSqjzrybh());
            acceptRecommit.setJzdw(byId.getJzjg());
            acceptRecommit.setJzdwmc(byId.getJzjgName());
            acceptRecommit.setZjlx("111");
            acceptRecommit.setZjhm(byId.getSfzh());
            acceptRecommit.setJzlb(byId.getJzlbName());
            acceptRecommit.setSqjzkssj(byId.getSqjzksrq());
            acceptRecommit.setSqjzjssj(byId.getSqjzjsrq());
            acceptRecommit.setXzd(byId.getGdjzdszxq());
            acceptRecommit.setXzdxz(byId.getGdjzdmx());
            acceptRecommit.setPjrq(byId.getPjrq());
            acceptRecommit.setPjzm(byId.getZmName());
            acceptRecommit.setYpxq(byId.getYqtxqx());

            OrgCommon org = orgService.getById(byId.getJzjg());
            if (org != null) {
                OrgCommon pOrg = orgService.getById(org.getPid());
                acceptRecommit.setJzjglxr(pOrg.getLxr());
                acceptRecommit.setJzjglxrdh(pOrg.getLxdh());
                acceptRecommit.setQxjcy(extOrgInfoService.getExtOrgName(OrgTypeEnum.JIAN_CHA_YUAN.getCode(), org.getCode().substring(0, 6)));
                ywxtNoticeService.buildNoticeByOrgId(
                        YwxtNoticeTypeEnum.RECOMMIT_01,
                        byId.getJzjgName()+","+DateUtil.formatDate(acceptRecommit.getSdsj())+","+byId.getXm(),
                        byId.getXm(),
                        org.getId(),org.getPid());
            }
        }
        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));
/*


        if (downloadFile && param.getDocList().size() >0 ) {
            List<FileParam> fileList = new ArrayList<>();
            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setSupportCname(false);
            OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);
            for (AcceptCorrectionDocParam doc : param.getDocList()) {
                try {
                    String fileName = doc.getId() + doc.getUri().substring(doc.getUri().lastIndexOf("."));
                    fileList.add(new FileParam(OssBootUtil.download(FileConfig.DEFAULT_TEMP + File.separator, fileName,ossClient,bucketName,doc.getUri()),doc.getId()));
                }catch (Exception e) {
                    e.printStackTrace();
                    log.error("downloadFileErr: {} : {}",doc.getId(),doc.getUri());
                }
            }
            if (fileList.size()>0) {
                List<FileParam> list = sysFileInfoService.uploadFileOss(fileList);
                for (FileParam fileParam : list) {
                    acceptCorrectionDocService.lambdaUpdate()
                            .set(AcceptCorrectionDoc::getOssUrl,fileParam.getUrl())
                            .set(AcceptCorrectionDoc::getFileId,fileParam.getFileId())
                            .eq(AcceptCorrectionDoc::getId,fileParam.getBizId()).update();
                }
                String regex = "已于(.*?)被本单位决定";
                Pattern pattern = Pattern.compile(regex);
                for (FileParam fileParam : fileList) {
                    File pdf = fileParam.getFile();
                    //再犯罪日期
                    String ocrText = OCRToolsUtil.getOCRText(pdf, pattern, tessDataPath);
                    if (ObjectUtil.isEmpty(ocrText)) {
                        continue;
                    }
                    DateTime parse = DateUtil.parse(ocrText);
                    if (parse == null) {
                        continue;
                    }
                    acceptRecommit.setZfrq(parse);
                    break;
                }
            }
        }*/
        this.save(acceptRecommit);
        recommitDcService.sync(acceptRecommit);
        if (byId != null) {
            saveOrUpdateCount(acceptRecommit);
        }
        return param.getDocList();
    }

    private void saveOrUpdateCount(AcceptRecommit acceptRecommit) {
        int count = this.lambdaQuery()
                .eq(AcceptRecommit::getJzdw, acceptRecommit.getJzdw())
                .apply("date_format (sdsj,'%Y-%m-%d') = date_format('" + acceptRecommit.getSdsj() + "','%Y-%m-%d')")
                .count();
        OrgCommon org = orgService.getById(acceptRecommit.getJzdw());
        ywxtCountService.saveOrUpdateCount("T03", DateUtil.formatDate(acceptRecommit.getSdsj()), org.getId(), count,true);
    }

    @Override
    public void edit(AcceptRecommitParam acceptRecommitParam) {
        AcceptRecommit acceptRecommit = this.queryAcceptRecommit(acceptRecommitParam);
        BeanUtil.copyProperties(acceptRecommitParam, acceptRecommit);
        this.updateById(acceptRecommit);
        recommitDcService.sync(acceptRecommit);
    }

    @Override
    public void export(AcceptRecommitParam param) {
        QueryWrapper<AcceptRecommit> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(AcceptRecommit::getXm, param.getXm().trim());
            }
            // 根据案件类型 查询
            if (ObjectUtil.isNotEmpty(param.getAjlx())) {
                queryWrapper.lambda().eq(AcceptRecommit::getAjlx, param.getAjlx());
            }
            // 根据措施类型 查询
            if (ObjectUtil.isNotEmpty(param.getCslx())) {
                queryWrapper.lambda().eq(AcceptRecommit::getCslx, param.getCslx());
            }
            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getTaskId())) {
                queryWrapper.lambda().eq(AcceptRecommit::getTaskId, param.getTaskId());
            }
            // 根据推送单位 查询
            if (ObjectUtil.isNotEmpty(param.getTsdw())) {
                queryWrapper.lambda().eq(AcceptRecommit::getTsdw, param.getTsdw());
            }
            // 根据矫正单位 查询
            if (param.getOrgSet().size() < 1000) {
                queryWrapper.lambda().in(AcceptRecommit::getJzdw, param.getOrgSet());
            }
            // 根据矫正类别 查询
            if (ObjectUtil.isNotEmpty(param.getJzlb())) {
                queryWrapper.lambda().eq(AcceptRecommit::getJzlb, param.getJzlb());
            }
        }
        List<AcceptRecommit> list = this.list(queryWrapper);
        PoiUtil.exportExcelWithStream("AcceptRecommit.xls", AcceptRecommit.class, list);
    }

    @Override
    public void sendLog(String id, String xm, String jzjgId, String jzjgName, String jsdw, String zhuangtai) {
        this.baseMapper.deleteLog(id);
        this.baseMapper.sendLog(id, xm, jzjgId, jzjgName, extOrgInfoService.getExtOrgName(OrgTypeEnum.GONG_AN.getCode(), jsdw), zhuangtai);
    }

    @Override
    public PageResult<Map<String, Object>> logPage(String id, String fsrq, String xm, Set<String> jzjgSet, int deleted) {
        return new PageResult<>(this.baseMapper.logPage(PageFactory.defaultPage(), id, fsrq,xm,deleted, jzjgSet));
    }

    @Override
    public void logExport(String xm, Set<String> jzjgSet) {
        try {
            HttpServletResponse response = HttpServletUtil.getResponse();
            String fileName = URLEncoder.encode("已列管发送至公安的社区矫正对象清单.xlsx", CharsetUtil.UTF_8);
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setContentType("application/octet-stream;charset=UTF-8");
            FastExcel.write(response.getOutputStream(), SendLogExport.class)
                    .sheet("已列管发送至公安的社区矫正对象清单")
                    .doWrite(this.baseMapper.logExport(xm,jzjgSet));
        } catch (IOException e) {
            log.error(">>> logExport数据异常：{}", e.getMessage());
        }
    }

    /**
     * 获取公安再犯罪协同接收表
     *
     * <AUTHOR>
     * @date 2023-08-02 11:42:25
     */
    private AcceptRecommit queryAcceptRecommit(AcceptRecommitParam acceptRecommitParam) {
        AcceptRecommit acceptRecommit = this.getById(acceptRecommitParam.getId());
        if (ObjectUtil.isNull(acceptRecommit)) {
            throw new ServiceException(AcceptRecommitExceptionEnum.NOT_EXIST);
        }
        return acceptRecommit;
    }

    @Override
    public void getNoSignPdf(AcceptRecommit acceptRecommit, HttpServletResponse response) {
        try {
            String base64 = getNoSignPdfBase64(acceptRecommit);
            MultipartFile multipartFile = PdfFillUtil.convertBase64ToMultipartFile(base64, acceptRecommit.getWsmc());
            byte[] fileBytes = multipartFile.getBytes();
            //设置contentType
            response.setContentType("application/pdf");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException("获取签章pdf异常，" + e.getMessage());
        }
    }

    private String getNoSignPdfBase64(AcceptRecommit acceptRecommit) {
        List<PdfModalVo> pdfModalVoList = putList(fontPath, acceptRecommit);
        long start = System.currentTimeMillis();
        String template = PdfFillUtil.fillByTemplate(modalPath, pdfModalVoList);
        long end = System.currentTimeMillis();
        log.debug("画pdf耗时：" + (end - start));
        return template;
    }

    /**
     * 根据再犯罪信息填充模版数据
     *
     * @param fontPath
     * @param acceptRecommit
     * @return
     */
    private static List<PdfModalVo> putList(String fontPath, AcceptRecommit acceptRecommit) {

        List<PdfModalVo> pdfModalVoList = new ArrayList<>();

        PdfModalVo num = new PdfModalVo();
        num.setName("num");
        num.setValue(acceptRecommit.getWsh());
        num.setFontPath(fontPath);
        pdfModalVoList.add(num);

        PdfModalVo receiveDate = new PdfModalVo();
        receiveDate.setName("receiveDate");
        receiveDate.setValue(DateUtil.format(acceptRecommit.getSdsj(), DatePattern.CHINESE_DATE_PATTERN));
        receiveDate.setFontPath(fontPath);
        pdfModalVoList.add(receiveDate);

        PdfModalVo xm = new PdfModalVo();
        xm.setName("xm");
        xm.setValue(acceptRecommit.getXm());
        xm.setFontPath(fontPath);
        pdfModalVoList.add(xm);

        PdfModalVo receiveOrg = new PdfModalVo();
        receiveOrg.setName("receiveOrg");
        receiveOrg.setValue(acceptRecommit.getTsdwmc() + ":");
        receiveOrg.setUnderline(true);
        receiveOrg.setFontPath(fontPath);
        pdfModalVoList.add(receiveOrg);

        PdfModalVo jzjgName = new PdfModalVo();
        jzjgName.setName("jzjgName");
        jzjgName.setValue(acceptRecommit.getSjjzdwmc());
        jzjgName.setUnderline(false);
        jzjgName.setFontSize(18);
        jzjgName.setFontPath(fontPath);
        pdfModalVoList.add(jzjgName);

        //增加签章处的机构名称，字体变小
        PdfModalVo jzjgNameSmall = new PdfModalVo();
        jzjgNameSmall.setName("jzjgNameSmall");
        jzjgNameSmall.setValue(acceptRecommit.getSjjzdwmc());
        jzjgNameSmall.setUnderline(false);
        jzjgNameSmall.setFontSize(15);
        jzjgNameSmall.setFontPath(fontPath);
        pdfModalVoList.add(jzjgNameSmall);

        PdfModalVo backDate = new PdfModalVo();
        backDate.setName("backDate");
        backDate.setValue(acceptRecommit.getFkrq());
        backDate.setFontPath(fontPath);
        pdfModalVoList.add(backDate);

        return pdfModalVoList;
    }

    @Override
    public void getSignedPdf(AcceptRecommit acceptRecommit, HttpServletResponse response) {
        String sealNo = null;
        SignatureMaintenance signatureMaintenance = signatureMaintenanceService.getOne(new QueryWrapper<SignatureMaintenance>().lambda().eq(SignatureMaintenance::getJzjg, acceptRecommit.getSjjzdw()).eq(SignatureMaintenance::getSealType, "1").eq(SignatureMaintenance::getEnabled, 0));
        if (ObjectUtil.isNotEmpty(signatureMaintenance)) {
            sealNo = signatureMaintenance.getSealNo();
        } else {
            try {
                ResponseUtil.responseExceptionError(response, 500, "未维护社区矫正机构章编码，请联系技术人员或手动上传", null);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return;
        }
        try {
            long begin = System.currentTimeMillis();
            String base64 = getNoSignPdfBase64(acceptRecommit);
            long start = System.currentTimeMillis();
            log.debug("获取未盖章的pdf一共消耗" + (start - begin));
            String signedBase64 = electronicSignatureUtil.signedBase64(base64, sealNo, acceptRecommit.getWsmc() + ".pdf", "1", "400", "350");
            long end = System.currentTimeMillis();
            log.debug("上传返回签章一共消耗了：" + (end - start));
            MultipartFile multipartFile = PdfFillUtil.convertBase64ToMultipartFile(signedBase64, acceptRecommit.getWsmc() + ".pdf");
            byte[] fileBytes = multipartFile.getBytes();
            //设置contentType
            response.setContentType("application/pdf");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException("获取签章pdf异常，" + e.getMessage());
        }
    }

    @Override
    public MultipartFile getSignedPdfFile(AcceptRecommit acceptRecommit) {
        String sealNo = null;
        SignatureMaintenance signatureMaintenance = signatureMaintenanceService.getOne(new QueryWrapper<SignatureMaintenance>().lambda().eq(SignatureMaintenance::getJzjg, acceptRecommit.getSjjzdw()).eq(SignatureMaintenance::getSealType, "1").eq(SignatureMaintenance::getEnabled, 0));
        if (ObjectUtil.isNotEmpty(signatureMaintenance)) {
            sealNo = signatureMaintenance.getSealNo();
        } else {
            throw new RuntimeException("未找到" + acceptRecommit.getSjjzdwmc() + "的矫正机构签章编号，或签章被禁用，请在签章管理模块中维护！");
        }
        try {
            String base64 = getNoSignPdfBase64(acceptRecommit);
            String signedBase64 = electronicSignatureUtil.signedBase64(base64, sealNo, acceptRecommit.getWsmc() + ".pdf", "1", "400", "350");
            return PdfFillUtil.convertBase64ToMultipartFile(signedBase64, acceptRecommit.getWsmc() + ".pdf");
        } catch (IOException e) {
            throw new RuntimeException("尚未授权云签章，请联系管理员或使用手动上传");
        }
    }

    @Override
    public String generateDocumentNumber(AcceptRecommit acceptRecommit) {
        String documentNumber = acceptRecommit.getWsh();
        if (ObjectUtil.isEmpty(documentNumber)) {
            String pattern = "社矫通回字[" + DateUtil.year(DateUtil.date()) + "]";
            String maxwsh = this.baseMapper.getMaxNumberByYear(pattern);
            if (ObjectUtil.isEmpty(maxwsh)) {
                documentNumber = pattern + "0001号";
            } else {
                String number = maxwsh.substring(11, 15);
                int lastMax = Integer.parseInt(number);
                int newMax = lastMax + 1;
                String formatted = String.format("%04d", newMax);
                documentNumber = pattern + formatted + "号";
            }
            //默认占用一个坑位，保持文书号唯一
            acceptRecommit.setWsh(documentNumber);
            this.updateById(acceptRecommit);
            return documentNumber;
        }
        return documentNumber;
    }

    @Override
    public String getZfrq(String id) {
        List<AcceptCorrectionDoc> list = acceptCorrectionDocService.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, id).list();
        if (list.size()==0) {
            return "list=0";
        }

        ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
        conf.setSupportCname(false);
        OSSClient ossClient = (OSSClient) new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret, conf);

        String content = "";
        String regex = "已于(.*?)被本单位决定";
        Pattern pattern = Pattern.compile(regex);
        try {
            for (AcceptCorrectionDoc doc : list) {
                String fileName = doc.getId();
                int beginIndex = doc.getUri().lastIndexOf(".");
                if (beginIndex>=0) {
                    fileName += doc.getUri().substring(beginIndex);
                }
                File pdf = OssBootUtil.download(FileConfig.DEFAULT_TEMP + File.separator, fileName, ossClient, bucketName, doc.getUri());

                //再犯罪日期
                String ocrText = OCRToolsUtil.getOCRText(pdf, pattern, tessDataPath);
                if (ObjectUtil.isEmpty(ocrText)) {
                    continue;
                }
                content = ocrText;
                DateTime parseZfrq = DateUtil.parse(ocrText);
                if (parseZfrq == null) {
                    continue;
                }
                this.lambdaUpdate().set(AcceptRecommit::getZfrq, parseZfrq).eq(AcceptRecommit::getId, id).update();
                break;
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return content;
    }

    @Override
    public AcceptRecommit generateFeedbackInfo(AcceptRecommitParam param, String userName) {
        if (param == null) {
            return null;
        }
        AcceptRecommit acceptRecommit = this.getById(param.getId());
        if (ObjectUtil.isNotEmpty(acceptRecommit.getFeedbackStatus()) && 1 == acceptRecommit.getFeedbackStatus()) {
            acceptRecommit.setHzwsList(sysFileInfoService.getDetailByIds(acceptRecommit.getHzws()));
            return acceptRecommit;
        }
        if (ObjectUtil.isEmpty(acceptRecommit.getWsh())) {
            acceptRecommit.setWsh(generateDocumentNumber(acceptRecommit));
        }
        acceptRecommit.setWsmc(acceptRecommit.getXm() + "-被限制人身自由通知书（回执）");
        acceptRecommit.setFkrq(DateUtil.format(DateUtil.date(), DatePattern.CHINESE_DATE_PATTERN));
        acceptRecommit.setCzsj(DateUtil.date());
        acceptRecommit.setCzr(userName);
        OrgCommon sysOrg = orgService.getById(acceptRecommit.getJzdw());
        OrgCommon pOrg = orgService.getById(sysOrg.getPid());
        SignatureMaintenance signatureMaintenance = signatureMaintenanceService.getOne(new QueryWrapper<SignatureMaintenance>().lambda().eq(SignatureMaintenance::getJzjg, acceptRecommit.getSjjzdw()).eq(SignatureMaintenance::getSealType, "1").eq(SignatureMaintenance::getEnabled, 0));
        if (ObjectUtil.isNotEmpty(signatureMaintenance)) {
            acceptRecommit.setSjjzdwmc(signatureMaintenance.getSealName());
        } else if (!param.isManualUpload()) {
//            throw new ServiceException(500, "未维护社区矫正机构章编码，请联系技术人员或手动上传");
            acceptRecommit.setSjjzdwmc(pOrg.getName());
        }
        acceptRecommit.setSjjzdw(pOrg.getId());
        acceptRecommit.setBzsm(param.getBzsm());
        acceptRecommit.setHzwsList(sysFileInfoService.getDetailByIds(acceptRecommit.getHzws()));
        return acceptRecommit;
    }


}
