package com.concise.gen.chargeinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 罪名表
 *
 * <AUTHOR>
 * @date 2023-06-16 15:30:23
 */
@Data
@TableName("s_charge_info")
public class ChargeInfo{

    /**
     * 罪名编码
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String chargeCode;

    /**
     *
     */
    private String charge;
    /**
     * 犯罪类型
     */
    private String chargeType;

    /**
     *
     */
    private Integer shift;

}
