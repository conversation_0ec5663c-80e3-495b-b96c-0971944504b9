package com.concise.gen.signatureauthorization.enums;

import com.concise.common.annotion.ExpEnumType;
import com.concise.common.consts.SysExpEnumConstant;
import com.concise.common.exception.enums.abs.AbstractBaseExceptionEnum;
import com.concise.common.factory.ExpEnumCodeFactory;

/**
 * 签章授权表
 *
 * <AUTHOR>
 * @date 2025-07-31 11:04:05
 */
@ExpEnumType(module = SysExpEnumConstant.SNOWY_SYS_MODULE_EXP_CODE, kind = SysExpEnumConstant.SYS_POS_EXCEPTION_ENUM)
public enum SignatureAuthorizationExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 数据不存在
     */
    NOT_EXIST(1, "此数据不存在"),
    /**
     * 授权证明不存在
     */
    AUTHORIZATION_PROOF_NOT_EXIST(2, "授权证明不存在"),
    PARAM_ERROR(3, "参数错误"),
    SIGNATURE_ID_NOT_EXIST(4, "签章id不存在"),
    USER_ID_NOT_EXIST(5, "用户id不存在"),
    USER_NAME_NOT_EXIST(6, "用户不存在"), START_TIME_NOT_EXIST(7, "开始时间不存在"),
    END_TIME_NOT_EXIST(8, "结束时间不存在"), SIGNATURE_NOT_EXIST(9, "签章不存在"),
    ;

    private final Integer code;

    private final String message;

    SignatureAuthorizationExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
