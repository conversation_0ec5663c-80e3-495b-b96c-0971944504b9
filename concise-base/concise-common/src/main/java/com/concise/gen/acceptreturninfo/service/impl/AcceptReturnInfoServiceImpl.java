package com.concise.gen.acceptreturninfo.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptreturninfo.entity.AcceptReturnInfo;
import com.concise.gen.acceptreturninfo.enums.AcceptReturnInfoExceptionEnum;
import com.concise.gen.acceptreturninfo.mapper.AcceptReturnInfoMapper;
import com.concise.gen.acceptreturninfo.param.AcceptReturnInfoParam;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 数据质检记录表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:07
 */
@Service
public class AcceptReturnInfoServiceImpl extends ServiceImpl<AcceptReturnInfoMapper, AcceptReturnInfo> implements AcceptReturnInfoService {

    @Override
    public PageResult<AcceptReturnInfo> page(AcceptReturnInfoParam acceptReturnInfoParam) {
        QueryWrapper<AcceptReturnInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptReturnInfoParam)) {

            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(acceptReturnInfoParam.getTyfh())) {
                queryWrapper.lambda().eq(AcceptReturnInfo::getTyfh, acceptReturnInfoParam.getTyfh());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(acceptReturnInfoParam.getXm())) {
                queryWrapper.lambda().like(AcceptReturnInfo::getXm, acceptReturnInfoParam.getXm());
            }
            // 根据矫正单位 查询
            if (ObjectUtil.isNotEmpty(acceptReturnInfoParam.getJsdw())) {
                queryWrapper.lambda().like(AcceptReturnInfo::getJsdwPids, acceptReturnInfoParam.getJsdw());
            }
            // 根据退回类型 查询
            if (ObjectUtil.isNotEmpty(acceptReturnInfoParam.getThlx())) {
                queryWrapper.lambda().eq(AcceptReturnInfo::getThlx, acceptReturnInfoParam.getThlx());
            }
            if (ObjectUtil.isNotEmpty(acceptReturnInfoParam.getType())) {
                queryWrapper.lambda().eq(AcceptReturnInfo::getType, acceptReturnInfoParam.getType());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(acceptReturnInfoParam.getSearchBeginTime(), acceptReturnInfoParam.getSearchEndTime())) {
                queryWrapper.lambda().ge(AcceptReturnInfo::getThsj, acceptReturnInfoParam.getSearchBeginTime());
                queryWrapper.lambda().le(AcceptReturnInfo::getThsj, acceptReturnInfoParam.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(AcceptReturnInfo::getThsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public AcceptReturnInfo detail(AcceptReturnInfoParam acceptReturnInfoParam) {
        return this.queryAcceptReturnInfo(acceptReturnInfoParam);
    }

    @Override
    public Boolean check(Date sdsj, List<AcceptCorrectionDocParam> newDocList, List<AcceptCorrectionDoc> oldDocList) {
        DateTime now = DateUtil.date();
        if (DateUtil.compare(DateUtil.offset(sdsj, DateField.HOUR,2), now)<0) {
            //间隔时间小于2小时的
            return false;
        }
        if (newDocList.size()!=oldDocList.size()) {
            return false;
        }
        Set<String> newDocNames = newDocList.stream().map(AcceptCorrectionDocParam::getWs).collect(Collectors.toSet());
        Set<String> oldDocNames = oldDocList.stream().map(AcceptCorrectionDoc::getWs).collect(Collectors.toSet());
        if (!newDocNames.containsAll(oldDocNames) || !oldDocNames.containsAll(newDocNames)) {
            // 文书名称有不同的
            return false;
        }
        return true;
    }

    @Override
    public Boolean check1(Date sdsj, List<AcceptCorrectionDoc> newDocList, List<AcceptCorrectionDoc> oldDocList) {
        DateTime now = DateUtil.date();
        if (DateUtil.compare(DateUtil.offset(sdsj, DateField.HOUR,2), now)<0) {
            //间隔时间小于2小时的
            return false;
        }
        if (newDocList.size()!=oldDocList.size()) {
            return false;
        }
        Set<String> newDocNames = newDocList.stream().map(AcceptCorrectionDoc::getWs).collect(Collectors.toSet());
        Set<String> oldDocNames = oldDocList.stream().map(AcceptCorrectionDoc::getWs).collect(Collectors.toSet());
        if (!newDocNames.containsAll(oldDocNames) || !oldDocNames.containsAll(newDocNames)) {
            // 文书名称有不同的
            return false;
        }
        return true;
    }

    @Override
    public void save(String xtbh, String id, String tyfh, Date startTime, Date sdsj, String xm, String jsdw, String jsdwId, String jsdwPids, String jsdwmc) {
        DateTime now = DateUtil.date();
        AcceptReturnInfo returnInfo = new AcceptReturnInfo();
        returnInfo.setId(id);
        returnInfo.setType(xtbh);
        returnInfo.setTyfh(tyfh);
        returnInfo.setJsdw(jsdw);
        returnInfo.setJsdwmc(jsdwmc);
        returnInfo.setJsdwId(jsdwId);
        returnInfo.setJsdwPids(jsdwPids);
        returnInfo.setXm(xm);
        //数据质检自动过滤
        returnInfo.setThlx("1");
        returnInfo.setThyy("数据重复：本案件与"+ DateUtil.formatDateTime(sdsj)+"接收的"+ xm +"协同案件重复");
        returnInfo.setTssj(startTime);
        returnInfo.setThsj(now);
        this.save(returnInfo);
    }

    /**
     * 获取数据质检记录表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:07
     */
    private AcceptReturnInfo queryAcceptReturnInfo(AcceptReturnInfoParam acceptReturnInfoParam) {
        AcceptReturnInfo acceptReturnInfo = this.getById(acceptReturnInfoParam.getId());
        if (ObjectUtil.isNull(acceptReturnInfo)) {
            throw new ServiceException(AcceptReturnInfoExceptionEnum.NOT_EXIST);
        }
        return acceptReturnInfo;
    }
}
