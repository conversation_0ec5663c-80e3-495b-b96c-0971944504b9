package com.concise.gen.acceptcorrectionaccomplice. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.acceptcorrectionaccomplice. param.AcceptCorrectionAccompliceParam;
import com.concise.gen.acceptcorrectionaccomplice. service.AcceptCorrectionAccompliceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 矫正对象同案犯信息信息接收表控制器
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:23
 */
@Api(tags = "矫正对象同案犯信息信息接收表")
@RestController
public class AcceptCorrectionAccompliceController {

    @Resource
    private AcceptCorrectionAccompliceService acceptCorrectionAccompliceService;

    /**
     * 查询矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    @Permission
    @GetMapping("/acceptCorrectionAccomplice/page")
    @ApiOperation("矫正对象同案犯信息信息接收表_分页查询")
    @BusinessLog(title = "矫正对象同案犯信息信息接收表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        return new SuccessResponseData(acceptCorrectionAccompliceService.page(acceptCorrectionAccompliceParam));
    }

    /**
     * 添加矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    @Permission
    @PostMapping("/acceptCorrectionAccomplice/add")
    @ApiOperation("矫正对象同案犯信息信息接收表_增加")
    @BusinessLog(title = "矫正对象同案犯信息信息接收表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AcceptCorrectionAccompliceParam.add.class) AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        acceptCorrectionAccompliceService.add(acceptCorrectionAccompliceParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    @Permission
    @PostMapping("/acceptCorrectionAccomplice/delete")
    @ApiOperation("矫正对象同案犯信息信息接收表_删除")
    @BusinessLog(title = "矫正对象同案犯信息信息接收表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AcceptCorrectionAccompliceParam.delete.class) AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        acceptCorrectionAccompliceService.delete(acceptCorrectionAccompliceParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    @Permission
    @PostMapping("/acceptCorrectionAccomplice/edit")
    @ApiOperation("矫正对象同案犯信息信息接收表_编辑")
    @BusinessLog(title = "矫正对象同案犯信息信息接收表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AcceptCorrectionAccompliceParam.edit.class) AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        acceptCorrectionAccompliceService.edit(acceptCorrectionAccompliceParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    @Permission
    @GetMapping("/acceptCorrectionAccomplice/detail")
    @ApiOperation("矫正对象同案犯信息信息接收表_查看")
    @BusinessLog(title = "矫正对象同案犯信息信息接收表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AcceptCorrectionAccompliceParam.detail.class) AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        return new SuccessResponseData(acceptCorrectionAccompliceService.detail(acceptCorrectionAccompliceParam));
    }

    /**
     * 矫正对象同案犯信息信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    @Permission
    @GetMapping("/acceptCorrectionAccomplice/list")
    @ApiOperation("矫正对象同案犯信息信息接收表_列表")
    @BusinessLog(title = "矫正对象同案犯信息信息接收表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        return new SuccessResponseData(acceptCorrectionAccompliceService.list(acceptCorrectionAccompliceParam.getContactId()));
    }

}
