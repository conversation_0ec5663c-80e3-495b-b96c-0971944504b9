package com.concise.gen.acceptreturninfo.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
* 数据质检记录表参数类
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:07
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptReturnInfoParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;
    private String contactId;
    /**
     * 数据类别
     *(协同编号)
     */
    private String type;

    /**
     * 统一赋号
     */
    @NotBlank(message = "统一赋号不能为空，请检查tyfh参数", groups = {add.class, edit.class})
    private String tyfh;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 矫正单位
     */
    @NotBlank(message = "矫正单位不能为空，请检查jzdw参数", groups = {add.class, edit.class})
    private String jsdw;
    private String jsdwmc;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 退回类型
     */
    @NotBlank(message = "退回类型不能为空，请检查thlx参数", groups = {add.class, edit.class})
    private String thlx;

    /**
     * 退回原因
     */
    @NotBlank(message = "退回原因不能为空，请检查thyy参数", groups = {add.class, edit.class})
    private String thyy;

    /**
     * 退回时间
     */
    @NotNull(message = "退回时间不能为空，请检查thsj参数", groups = {add.class, edit.class})
    private String thsj;

    /**
     * 接收时间
     */
    @NotNull(message = "接收时间不能为空，请检查jssj参数", groups = {add.class, edit.class})
    private String jssj;

}
