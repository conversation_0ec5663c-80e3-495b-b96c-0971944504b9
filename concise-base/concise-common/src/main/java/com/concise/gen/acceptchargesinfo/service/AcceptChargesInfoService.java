package com.concise.gen.acceptchargesinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.gen.acceptchargesinfo.entity.AcceptChargesInfo;

import java.util.List;

/**
 * 具体罪名信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:09
 */
public interface AcceptChargesInfoService extends IService<AcceptChargesInfo> {

    /**
     * contactId
     *
     * <AUTHOR>
     * @date 2022-07-22 13:34:09
     */
    List<AcceptChargesInfo> list(String contactId);
}
