package com.concise.gen.acceptprisonexecute.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseAcceptEntity;
import lombok.Data;

import java.util.Date;

/**
 * 收监执行接收表
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
@Data
@TableName("accept_prison_execute")
public class AcceptPrisonExecute extends BaseAcceptEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     */
    private String sqjzajbh;

    /**
     * （法院｜监狱）案件标识
     */
    private String ajbs;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 决定书文号
     */
    private String jdswh;

    /**
     * 是否决定收监
     */
    private String sfjdsj;

    /**
     * （不）收监决定日期
     */
    @Excel(name = "（不）收监决定日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date sjjdrq;

    /**
     * （不）收监原因
     */
    private String sjyy;

    /**
     * （不）收监决定机关
     */
    private String sjjdjg;

}
