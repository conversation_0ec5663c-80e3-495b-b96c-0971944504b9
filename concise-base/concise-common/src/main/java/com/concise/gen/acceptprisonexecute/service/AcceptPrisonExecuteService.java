package com.concise.gen.acceptprisonexecute.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptprisonexecute.entity.AcceptPrisonExecute;
import com.concise.gen.acceptprisonexecute.param.AcceptPrisonExecuteParam;

import java.util.List;

/**
 * 收监执行接收表service接口
 *
 * <AUTHOR>
 * @date 2022-08-01 15:29:08
 */
public interface AcceptPrisonExecuteService extends IService<AcceptPrisonExecute> {

    /**
     * 查询收监执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
    PageResult<AcceptPrisonExecute> page(AcceptPrisonExecuteParam acceptPrisonExecuteParam);

    /**
     * 收监执行接收表列表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
    List<AcceptPrisonExecute> list(AcceptPrisonExecuteParam acceptPrisonExecuteParam);

    /**
     * 添加收监执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
    List<AcceptCorrectionDocParam> add(AcceptPrisonExecuteParam acceptPrisonExecuteParam);

    /**
     * 删除收监执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
    void delete(AcceptPrisonExecuteParam acceptPrisonExecuteParam);
    /**
     * delete
     * @param taskId taskId
     */
    void delete(String taskId);
    /**
     * 编辑收监执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
    void edit(AcceptPrisonExecuteParam acceptPrisonExecuteParam);

    /**
     * 查看收监执行接收表
     *
     * <AUTHOR>
     * @date 2022-08-01 15:29:08
     */
     AcceptPrisonExecute detail(AcceptPrisonExecuteParam acceptPrisonExecuteParam);
}
