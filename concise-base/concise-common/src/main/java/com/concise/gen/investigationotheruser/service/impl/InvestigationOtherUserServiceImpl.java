package com.concise.gen.investigationotheruser.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.investigationotheruser.entity.InvestigationOtherUser;
import com.concise.gen.investigationotheruser.enums.InvestigationOtherUserExceptionEnum;
import com.concise.gen.investigationotheruser.mapper.InvestigationOtherUserMapper;
import com.concise.gen.investigationotheruser.param.InvestigationOtherUserParam;
import com.concise.gen.investigationotheruser.service.InvestigationOtherUserService;
import com.concise.gen.investigationsign.param.InvestigationSignParam;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估-其他用户service接口实现类
 *
 * <AUTHOR>
 * @date 2025-08-01 11:02:13
 */
@Service
public class InvestigationOtherUserServiceImpl extends ServiceImpl<InvestigationOtherUserMapper, InvestigationOtherUser> implements InvestigationOtherUserService {

    @Override
    public PageResult<InvestigationOtherUser> page(InvestigationOtherUserParam investigationOtherUserParam) {
        QueryWrapper<InvestigationOtherUser> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigationOtherUserParam)) {

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(investigationOtherUserParam.getName())) {
                queryWrapper.lambda().eq(InvestigationOtherUser::getName, investigationOtherUserParam.getName());
            }
            // 根据手机号 查询
            if (ObjectUtil.isNotEmpty(investigationOtherUserParam.getPhone())) {
                queryWrapper.lambda().eq(InvestigationOtherUser::getPhone, investigationOtherUserParam.getPhone());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigationOtherUser> list(InvestigationOtherUserParam investigationOtherUserParam) {
        return this.list();
    }

    @Override
    public void add(InvestigationOtherUserParam investigationOtherUserParam) {
        InvestigationOtherUser investigationOtherUser = new InvestigationOtherUser();
        BeanUtil.copyProperties(investigationOtherUserParam, investigationOtherUser);
        this.save(investigationOtherUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigationOtherUserParam investigationOtherUserParam) {
        this.removeById(investigationOtherUserParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigationOtherUserParam investigationOtherUserParam) {
        InvestigationOtherUser investigationOtherUser = this.queryInvestigationOtherUser(investigationOtherUserParam);
        BeanUtil.copyProperties(investigationOtherUserParam, investigationOtherUser);
        this.updateById(investigationOtherUser);
    }

    @Override
    public InvestigationOtherUser detail(InvestigationOtherUserParam investigationOtherUserParam) {
        return this.queryInvestigationOtherUser(investigationOtherUserParam);
    }

    /**
     * 获取调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    private InvestigationOtherUser queryInvestigationOtherUser(InvestigationOtherUserParam investigationOtherUserParam) {
        InvestigationOtherUser investigationOtherUser = this.getById(investigationOtherUserParam.getId());
        if (ObjectUtil.isNull(investigationOtherUser)) {
            throw new ServiceException(InvestigationOtherUserExceptionEnum.NOT_EXIST);
        }
        return investigationOtherUser;
    }

    @Override
    public void addOtherUser(List<InvestigationSignParam> zlbUserList) {
        //如果不存在同名同姓的就新增
        if (CollUtil.isNotEmpty(zlbUserList)) {
            for (InvestigationSignParam zlbUser : zlbUserList) {
                int count = this.count(new QueryWrapper<InvestigationOtherUser>().lambda().eq(InvestigationOtherUser::getName, zlbUser.getReceiveUserName()).eq(InvestigationOtherUser::getPhone, zlbUser.getPhone()));
                if (count > 0) {
                    continue;
                }
                InvestigationOtherUser investigationOtherUser = new InvestigationOtherUser();
                investigationOtherUser.setName(zlbUser.getReceiveUserName());
                investigationOtherUser.setPhone(zlbUser.getPhone());
                investigationOtherUser.setCreateTime(DateUtil.date());
                this.save(investigationOtherUser);
            }
        }

    }
}
