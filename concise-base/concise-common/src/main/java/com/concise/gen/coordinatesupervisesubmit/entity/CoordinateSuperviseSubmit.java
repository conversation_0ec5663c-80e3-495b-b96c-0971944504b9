package com.concise.gen.coordinatesupervisesubmit.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.concise.common.pojo.base.entity.BaseEntity;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

/**
 * 提请外出监管协同
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("coordinate_supervise_submit")
public class CoordinateSuperviseSubmit extends BaseEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 委托单位
     */
    private String wtdwmc;

    /**
     * 委托单位id
     */
    private String wtdwId;

    /**
     * 收到委托时间
     */
    @Excel(name = "收到委托时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date sdwtsj;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 社区矫正人员ID
     */
    private String sqjzryId;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 矫正单位
     */
    private String jzdw;

    /**
     * 矫正单位ID
     */
    private String jzdwId;

    /**
     * 外出申请编号
     */
    private String wcsqbh;

    /**
     * 外出申请时间
     */
    @Excel(name = "外出申请时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date wcsqsj;

    /**
     * 外出目的地
     */
    private String wcmdd;

    /**
     * 外出开始时间
     */
    @Excel(name = "外出开始时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date wckssj;

    /**
     * 外出结束时间
     */
    @Excel(name = "外出结束时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date wcjssj;

    /**
     * 外出原因
     */
    private String wcyy;

    /**
     * 外出天数
     */
    private Integer wcts;

    /**
     * 状态（字典 0正常 1停用 2删除)
     */
    private Integer zt;

}
