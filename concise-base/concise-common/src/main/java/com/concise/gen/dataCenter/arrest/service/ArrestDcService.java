package com.concise.gen.dataCenter.arrest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.dataCenter.arrest.entity.ArrestDc;
import com.concise.gen.dataCenter.arrest.param.ArrestDcParam;
import java.util.List;

/**
 * 提请逮捕service接口
 *
 * <AUTHOR>
 * @date 2023-09-19 17:30:07
 */
public interface ArrestDcService extends IService<ArrestDc> {

    /**
     * 查询提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    PageResult<ArrestDc> page(ArrestDcParam arrestDcParam);

    /**
     * 提请逮捕列表
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    List<ArrestDc> list(ArrestDcParam arrestDcParam);

    /**
     * 添加提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    void add(ArrestDcParam arrestDcParam);

    /**
     * 删除提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    void delete(ArrestDcParam arrestDcParam);

    /**
     * 编辑提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    void edit(ArrestDcParam arrestDcParam);

    /**
     * 查看提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
     ArrestDc detail(ArrestDcParam arrestDcParam);
}
