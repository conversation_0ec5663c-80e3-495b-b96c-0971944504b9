package com.concise.gen.acceptcorrectionfamily.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionfamily.entity.AcceptCorrectionFamily;
import com.concise.gen.acceptcorrectionfamily.param.AcceptCorrectionFamilyParam;
import java.util.List;

/**
 * 矫正对象家庭及社会关系信息接收表service接口
 *
 * <AUTHOR>
 * @date 2022-07-08 16:36:09
 */
public interface AcceptCorrectionFamilyService extends IService<AcceptCorrectionFamily> {

    /**
     * 查询矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    PageResult<AcceptCorrectionFamily> page(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam);

    /**
     * 矫正对象家庭及社会关系信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    List<AcceptCorrectionFamily> list(String contactId);

    /**
     * 添加矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    void add(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam);

    /**
     * 删除矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    void delete(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam);

    /**
     * 编辑矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
    void edit(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam);

    /**
     * 查看矫正对象家庭及社会关系信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-08 16:36:09
     */
     AcceptCorrectionFamily detail(AcceptCorrectionFamilyParam acceptCorrectionFamilyParam);
}
