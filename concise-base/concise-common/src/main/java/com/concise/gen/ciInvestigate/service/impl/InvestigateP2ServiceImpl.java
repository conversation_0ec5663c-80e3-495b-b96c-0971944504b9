package com.concise.gen.ciInvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP2;
import com.concise.gen.ciInvestigate.enums.InvestigateP2ExceptionEnum;
import com.concise.gen.ciInvestigate.mapper.InvestigateP2Mapper;
import com.concise.gen.ciInvestigate.param.InvestigateP2Param;
import com.concise.gen.ciInvestigate.service.InvestigateP2Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 迁入调查评估_接收service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:15
 */
@Service
public class InvestigateP2ServiceImpl extends ServiceImpl<InvestigateP2Mapper, InvestigateP2> implements InvestigateP2Service {

    @Override
    public PageResult<InvestigateP2> page(InvestigateP2Param investigateP2Param) {
        QueryWrapper<InvestigateP2> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(investigateP2Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(investigateP2Param.getPid())) {
                queryWrapper.lambda().eq(InvestigateP2::getPid, investigateP2Param.getPid());
            }
            // 根据接收状态 查询
            if (ObjectUtil.isNotEmpty(investigateP2Param.getZt())) {
                queryWrapper.lambda().eq(InvestigateP2::getZt, investigateP2Param.getZt());
            }
            // 根据接收人 查询
            if (ObjectUtil.isNotEmpty(investigateP2Param.getJsr())) {
                queryWrapper.lambda().eq(InvestigateP2::getJsr, investigateP2Param.getJsr());
            }
            // 根据接收时间 查询
            if (ObjectUtil.isNotEmpty(investigateP2Param.getJssj())) {
                queryWrapper.lambda().eq(InvestigateP2::getJssj, investigateP2Param.getJssj());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(investigateP2Param.getBz())) {
                queryWrapper.lambda().eq(InvestigateP2::getBz, investigateP2Param.getBz());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(investigateP2Param.getThly())) {
                queryWrapper.lambda().eq(InvestigateP2::getThly, investigateP2Param.getThly());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<InvestigateP2> list(InvestigateP2Param investigateP2Param) {
        return this.list();
    }

    @Override
    public void add(InvestigateP2Param investigateP2Param) {
        InvestigateP2 investigateP2 = new InvestigateP2();
        BeanUtil.copyProperties(investigateP2Param, investigateP2);
        this.save(investigateP2);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(InvestigateP2Param investigateP2Param) {
        this.removeById(investigateP2Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(InvestigateP2Param investigateP2Param) {
        InvestigateP2 investigateP2 = this.queryInvestigateP2(investigateP2Param);
        BeanUtil.copyProperties(investigateP2Param, investigateP2);
        this.updateById(investigateP2);
    }

    @Override
    public InvestigateP2 detail(InvestigateP2Param investigateP2Param) {
        return this.queryInvestigateP2(investigateP2Param);
    }

    /**
     * 获取迁入调查评估_接收
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:15
     */
    private InvestigateP2 queryInvestigateP2(InvestigateP2Param investigateP2Param) {
        InvestigateP2 investigateP2 = this.getById(investigateP2Param.getId());
        if (ObjectUtil.isNull(investigateP2)) {
            throw new ServiceException(InvestigateP2ExceptionEnum.NOT_EXIST);
        }
        return investigateP2;
    }
}
