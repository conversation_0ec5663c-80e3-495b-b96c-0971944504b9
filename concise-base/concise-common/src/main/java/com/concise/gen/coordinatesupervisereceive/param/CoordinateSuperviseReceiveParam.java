package com.concise.gen.coordinatesupervisereceive.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 接收外出监管协同参数类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateSuperviseReceiveParam extends BaseParam {


    private Set<String> orgs;
    /**
     * 受理时间
     */
    private String slsj;
    /**
     * 受理说明
     */
    private String slsm;
    /**
     * 受理文书
     */
    private String slws;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 委托单位
     */
    @NotBlank(message = "委托单位不能为空，请检查wtdwmc参数", groups = {add.class, edit.class})
    private String wtdwmc;

    /**
     * 委托单位id
     */
    @NotBlank(message = "委托单位id不能为空，请检查wtdwId参数", groups = {add.class, edit.class})
    private String wtdwId;

    /**
     * 收到委托时间
     */
    @NotNull(message = "收到委托时间不能为空，请检查sdwtsj参数", groups = {add.class, edit.class})
    private String sdwtsj;

    /**
     * 委托编号
     */
    @NotBlank(message = "委托编号不能为空，请检查wtbh参数", groups = {add.class, edit.class})
    private String wtbh;

    /**
     * 社区矫正人员ID
     */
    @NotBlank(message = "社区矫正人员ID不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 矫正单位
     */
    @NotBlank(message = "矫正单位不能为空，请检查jzdw参数", groups = {add.class, edit.class})
    private String jzdw;

    /**
     * 矫正单位ID
     */
    @NotBlank(message = "矫正单位ID不能为空，请检查jzdwId参数", groups = {add.class, edit.class})
    private String jzdwId;

    /**
     * 外出申请编号
     */
    @NotBlank(message = "外出申请编号不能为空，请检查wcsqbh参数", groups = {add.class, edit.class})
    private String wcsqbh;

    /**
     * 外出申请时间
     */
    @NotNull(message = "外出申请时间不能为空，请检查wcsqsj参数", groups = {add.class, edit.class})
    private String wcsqsj;

    /**
     * 外出目的地
     */
    @NotBlank(message = "外出目的地不能为空，请检查wcmdd参数", groups = {add.class, edit.class})
    private String wcmdd;

    /**
     * 外出开始时间
     */
    @NotNull(message = "外出开始时间不能为空，请检查wckssj参数", groups = {add.class, edit.class})
    private String wckssj;

    /**
     * 外出结束时间
     */
    @NotNull(message = "外出结束时间不能为空，请检查wcjssj参数", groups = {add.class, edit.class})
    private String wcjssj;

    /**
     * 外出原因
     */
    @NotBlank(message = "外出原因不能为空，请检查wcyy参数", groups = {add.class, edit.class})
    private String wcyy;

    /**
     * 外出天数
     */
    @NotNull(message = "外出天数不能为空，请检查wcts参数", groups = {add.class, edit.class})
    private Integer wcts;

    /**
     * 是否提请监管
     */
    @NotBlank(message = "是否提请监管不能为空，请检查sftqjg参数", groups = {add.class, edit.class})
    private String sftqjg;

    /**
     * 个别教育主题
     */
    @NotBlank(message = "个别教育主题不能为空，请检查gbjyzt参数", groups = {add.class, edit.class})
    private String gbjyzt;

    /**
     * 个别教育内容
     */
    @NotBlank(message = "个别教育内容不能为空，请检查gbjynr参数", groups = {add.class, edit.class})
    private String gbjynr;

    /**
     * 个别教育地点
     */
    @NotBlank(message = "个别教育地点不能为空，请检查gbjydd参数", groups = {add.class, edit.class})
    private String gbjydd;

    /**
     * 个别教育日期
     */
    @NotNull(message = "个别教育日期不能为空，请检查gbjyrq参数", groups = {add.class, edit.class})
    private String gbjyrq;

    /**
     * 个别教育机构
     */
    @NotBlank(message = "个别教育机构不能为空，请检查gbjyjg参数", groups = {add.class, edit.class})
    private String gbjyjg;

    /**
     * 报到日期
     */
    @NotNull(message = "报到日期不能为空，请检查bdrq参数", groups = {add.class, edit.class})
    private String bdrq;

    /**
     * 报到地点
     */
    @NotBlank(message = "报到地点不能为空，请检查bddd参数", groups = {add.class, edit.class})
    private String bddd;

    /**
     * 报到方式
     */
    @NotBlank(message = "报到方式不能为空，请检查bdfs参数", groups = {add.class, edit.class})
    private String bdfs;

    /**
     * 报到机构
     */
    @NotBlank(message = "报到机构不能为空，请检查bdjg参数", groups = {add.class, edit.class})
    private String bdjg;


    private String zt;

}
