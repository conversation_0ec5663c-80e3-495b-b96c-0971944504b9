package com.concise.gen.acceptbaseinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 交付衔接协同
 *
 * <AUTHOR>
 * @date 2023-02-03 16:46:06
 */
@Data
@TableName("accept_base_info")
public class AcceptBaseInfo{


    /**
     * 矫正机构
     */
    private String jzjg;

    @TableField(exist = false)
    private int statistics;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String taskId;

    /**
     * 协同编号
     */
    private String xtbh;

    /**
     * 数据状态
     * 0 待接收
     * 1 待反馈
     * 2 退回
     * 3 拒接收
     * 4 已反馈
     */
    private String zt;
    /**流程终止原因*/
    private String terminateReason;
    /** 是否自动退回*/
    private String returned;

    /**
     * 数据来源(机构类型)
     */
    private String sjlylx;
    /**
     * 数据来源名称
     */
    private String sjlylxmc;

    /**
     * 数据来源
     * 1：省内
     * 2：省外
     * */
    private String dataFrom;

    /**
     * 协同类型
     * 01 判处管制
     * 02 宣告缓刑
     * 03 裁定假释
     * 04 决定暂外
     * 05 矫正人员接收
     */
    private String xtlx;
    /**
     * 协同类型名称
     */
    private String xtlxmc;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 接收单位
     */
    private String jsdw;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 接收单位名称
     */
    private String jsdwmc;

    /**
     * 推送单位
     */
    private String tsdw;

    /**
     * 推送单位名称
     */
    private String tsdwmc;


    /**
     * 送达时间
     */
    private Date sdsj;

    /**
     * 反馈时间
     */
    private Date fksj;


    /**
     * 退回消息发送时间
     * */
    private Date returnMsgTime;
    /**
     * 退回消息发送成功
     * */
    private String returnMsgSuccess;



}
