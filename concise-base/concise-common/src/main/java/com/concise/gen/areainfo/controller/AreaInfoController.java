package com.concise.gen.areainfo.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.areainfo.param.AreaInfoParam;
import com.concise.gen.areainfo.service.AreaInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 省市县乡镇控制器
 *
 * <AUTHOR>
 * @date 2024-01-03 17:33:22
 */
@Api(tags = "省市县乡镇")
@RestController
public class AreaInfoController {

    @Resource
    private AreaInfoService areaInfoService;

    /**
     * 查询省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    @Permission
    @GetMapping("/areaInfo/page")
    @ApiOperation("省市县乡镇_分页查询")
    @BusinessLog(title = "省市县乡镇_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page() {

        return new SuccessResponseData(areaInfoService.tree());
    }

    /**
     * 添加省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    @Permission
    @PostMapping("/areaInfo/add")
    @ApiOperation("省市县乡镇_增加")
    @BusinessLog(title = "省市县乡镇_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AreaInfoParam.add.class) AreaInfoParam areaInfoParam) {
        areaInfoService.add(areaInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 删除省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    @Permission
    @PostMapping("/areaInfo/delete")
    @ApiOperation("省市县乡镇_删除")
    @BusinessLog(title = "省市县乡镇_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AreaInfoParam.delete.class) AreaInfoParam areaInfoParam) {
        areaInfoService.delete(areaInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    @Permission
    @PostMapping("/areaInfo/edit")
    @ApiOperation("省市县乡镇_编辑")
    @BusinessLog(title = "省市县乡镇_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AreaInfoParam.edit.class) AreaInfoParam areaInfoParam) {
        areaInfoService.edit(areaInfoParam);
        return new SuccessResponseData();
    }

    /**
     * 查看省市县乡镇
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    @Permission
    @GetMapping("/areaInfo/detail")
    @ApiOperation("省市县乡镇_查看")
    @BusinessLog(title = "省市县乡镇_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AreaInfoParam.detail.class) AreaInfoParam areaInfoParam) {
        return new SuccessResponseData(areaInfoService.detail(areaInfoParam));
    }

    /**
     * 省市县乡镇列表
     *
     * <AUTHOR>
     * @date 2024-01-03 17:33:22
     */
    @Permission
    @GetMapping("/areaInfo/list")
    @ApiOperation("省市县乡镇_列表")
    @BusinessLog(title = "省市县乡镇_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(AreaInfoParam areaInfoParam) {
        return new SuccessResponseData(areaInfoService.list(areaInfoParam));
    }

}
