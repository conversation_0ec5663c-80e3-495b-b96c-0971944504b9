package com.concise.gen.coordinateinvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP5;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateP5ExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateP5Mapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP5Param;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP5Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估协查_初审集体评议service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:59
 */
@Service
public class CoordinateInvestigateP5ServiceImpl extends ServiceImpl<CoordinateInvestigateP5Mapper, CoordinateInvestigateP5> implements CoordinateInvestigateP5Service {

    @Override
    public PageResult<CoordinateInvestigateP5> page(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        QueryWrapper<CoordinateInvestigateP5> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(coordinateInvestigateP5Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPid())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPid, coordinateInvestigateP5Param.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getZt())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getZt, coordinateInvestigateP5Param.getZt());
            }
            // 根据初审人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getCsr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getCsr, coordinateInvestigateP5Param.getCsr());
            }
            // 根据初审时间 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getCssj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getCssj, coordinateInvestigateP5Param.getCssj());
            }
            // 根据初审意见 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getCsyj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getCsyj, coordinateInvestigateP5Param.getCsyj());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getThly())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getThly, coordinateInvestigateP5Param.getThly());
            }
            // 根据评议审核事项 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPyshsx())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPyshsx, coordinateInvestigateP5Param.getPyshsx());
            }
            // 根据主持人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getZcr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getZcr, coordinateInvestigateP5Param.getZcr());
            }
            // 根据评议审核地点 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPyshdd())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPyshdd, coordinateInvestigateP5Param.getPyshdd());
            }
            // 根据评议审核时间 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPyshsj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPyshsj, coordinateInvestigateP5Param.getPyshsj());
            }
            // 根据评议审核人员(jsonarray) 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPyshry())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPyshry, coordinateInvestigateP5Param.getPyshry());
            }
            // 根据记录人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getJlr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getJlr, coordinateInvestigateP5Param.getJlr());
            }
            // 根据评议审核情况 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPyshqk())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPyshqk, coordinateInvestigateP5Param.getPyshqk());
            }
            // 根据评议审核意见 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getPyshyj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getPyshyj, coordinateInvestigateP5Param.getPyshyj());
            }
            // 根据负责人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getFzr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getFzr, coordinateInvestigateP5Param.getFzr());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP5Param.getBz())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP5::getBz, coordinateInvestigateP5Param.getBz());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateP5> list(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        CoordinateInvestigateP5 coordinateInvestigateP5 = new CoordinateInvestigateP5();
        BeanUtil.copyProperties(coordinateInvestigateP5Param, coordinateInvestigateP5);
        this.save(coordinateInvestigateP5);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        this.removeById(coordinateInvestigateP5Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        CoordinateInvestigateP5 coordinateInvestigateP5 = this.queryCoordinateInvestigateP5(coordinateInvestigateP5Param);
        BeanUtil.copyProperties(coordinateInvestigateP5Param, coordinateInvestigateP5);
        this.updateById(coordinateInvestigateP5);
    }

    @Override
    public CoordinateInvestigateP5 detail(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        return this.queryCoordinateInvestigateP5(coordinateInvestigateP5Param);
    }

    /**
     * 获取调查评估协查_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:59
     */
    private CoordinateInvestigateP5 queryCoordinateInvestigateP5(CoordinateInvestigateP5Param coordinateInvestigateP5Param) {
        CoordinateInvestigateP5 coordinateInvestigateP5 = this.getById(coordinateInvestigateP5Param.getId());
        if (ObjectUtil.isNull(coordinateInvestigateP5)) {
            throw new ServiceException(CoordinateInvestigateP5ExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateP5;
    }
}
