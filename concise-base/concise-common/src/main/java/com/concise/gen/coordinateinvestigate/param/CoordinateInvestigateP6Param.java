package com.concise.gen.coordinateinvestigate.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
* 调查评估协查_审批参数类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:48:00
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateInvestigateP6Param extends BaseParam {

    private Date createTime;
    /**
     * id
     */
    @NotNull(message = "id不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 调查评估id
     */
    @NotBlank(message = "调查评估id不能为空，请检查pid参数", groups = {add.class, edit.class})
    private String pid;

    /**
     * 调查步骤id
     */
    @NotBlank(message = "调查步骤id不能为空，请检查zt参数", groups = {add.class, edit.class})
    private String zt;

    /**
     * 审批人
     */
    @NotBlank(message = "审批人不能为空，请检查spr参数", groups = {add.class, edit.class})
    private String spr;

    /**
     * 审批时间
     */
    @NotNull(message = "审批时间不能为空，请检查spsj参数", groups = {add.class, edit.class})
    private String spsj;

    /**
     * 审批意见
     */
    @NotBlank(message = "审批意见不能为空，请检查spyj参数", groups = {add.class, edit.class})
    private String spyj;

    /**
     * 退回理由
     */
    @NotBlank(message = "退回理由不能为空，请检查thly参数", groups = {add.class, edit.class})
    private String thly;

    /**
     * 评估意见
     */
    @NotBlank(message = "评估意见不能为空，请检查pgyj参数", groups = {add.class, edit.class})
    private String pgyj;

    /**
     * 调查结束时间
     */
    @NotNull(message = "调查结束时间不能为空，请检查pyshsj参数", groups = {add.class, edit.class})
    private Date dcjssj;

    /**
     * 调查评估意见
     */
    @NotBlank(message = "调查评估意见不能为空，请检查dcpgyj参数", groups = {add.class, edit.class})
    private String dcpgyj;

    /**
     * 有关情况
     */
    private String ygqk;

}
