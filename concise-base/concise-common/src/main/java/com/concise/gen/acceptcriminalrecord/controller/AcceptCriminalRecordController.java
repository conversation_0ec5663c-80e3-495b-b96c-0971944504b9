package com.concise.gen.acceptcriminalrecord. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.acceptcriminalrecord. param.AcceptCriminalRecordParam;
import com.concise.gen.acceptcriminalrecord. service.AcceptCriminalRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 前科信息接收表控制器
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:43
 */
@Api(tags = "前科信息接收表")
@RestController
public class AcceptCriminalRecordController {

    @Resource
    private AcceptCriminalRecordService acceptCriminalRecordService;

    /**
     * 查询前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    @Permission
    @GetMapping("/acceptCriminalRecord/page")
    @ApiOperation("前科信息接收表_分页查询")
    @BusinessLog(title = "前科信息接收表_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        return new SuccessResponseData(acceptCriminalRecordService.page(acceptCriminalRecordParam));
    }

    /**
     * 添加前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    @Permission
    @PostMapping("/acceptCriminalRecord/add")
    @ApiOperation("前科信息接收表_增加")
    @BusinessLog(title = "前科信息接收表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AcceptCriminalRecordParam.add.class) AcceptCriminalRecordParam acceptCriminalRecordParam) {
        acceptCriminalRecordService.add(acceptCriminalRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 删除前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    @Permission
    @PostMapping("/acceptCriminalRecord/delete")
    @ApiOperation("前科信息接收表_删除")
    @BusinessLog(title = "前科信息接收表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AcceptCriminalRecordParam.delete.class) AcceptCriminalRecordParam acceptCriminalRecordParam) {
        acceptCriminalRecordService.delete(acceptCriminalRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    @Permission
    @PostMapping("/acceptCriminalRecord/edit")
    @ApiOperation("前科信息接收表_编辑")
    @BusinessLog(title = "前科信息接收表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AcceptCriminalRecordParam.edit.class) AcceptCriminalRecordParam acceptCriminalRecordParam) {
        acceptCriminalRecordService.edit(acceptCriminalRecordParam);
        return new SuccessResponseData();
    }

    /**
     * 查看前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    @Permission
    @GetMapping("/acceptCriminalRecord/detail")
    @ApiOperation("前科信息接收表_查看")
    @BusinessLog(title = "前科信息接收表_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(AcceptCriminalRecordParam.detail.class) AcceptCriminalRecordParam acceptCriminalRecordParam) {
        return new SuccessResponseData(acceptCriminalRecordService.detail(acceptCriminalRecordParam));
    }

    /**
     * 前科信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    @Permission
    @GetMapping("/acceptCriminalRecord/list")
    @ApiOperation("前科信息接收表_列表")
    @BusinessLog(title = "前科信息接收表_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        return new SuccessResponseData(acceptCriminalRecordService.list(acceptCriminalRecordParam.getContactId()));
    }

}
