package com.concise.gen.acceptcorrectionobject.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseAcceptEntity;
import com.concise.gen.acceptchargesinfo.entity.AcceptChargesInfo;
import com.concise.gen.acceptcorrectionaccomplice.entity.AcceptCorrectionAccomplice;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectionfamily.entity.AcceptCorrectionFamily;
import com.concise.gen.acceptcorrectionforbid.entity.AcceptCorrectionForbid;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 矫正对象信息接收表
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("accept_correction_object")
public class AcceptCorrectionObject extends BaseAcceptEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;


    @TableField(exist = false)
    private List<AcceptCorrectionFamily> familyList;
    @TableField(exist = false)
    private List<AcceptCorrectionAccomplice> accompliceList;
    @TableField(exist = false)
    private List<AcceptCorrectionForbid> forbidList;
    @TableField(exist = false)
    private List<AcceptCorrectionDoc> docList;
    @TableField(exist = false)
    private List<AcceptChargesInfo> chargeList;



    @TableField(exist = false)
    private String sqjzjgmc;
    /**法院案号*/
    private String fyah;
    /**法院被告人编号*/
    private String fybgrbh;
    /**检察院部门收案号*/
    private String jcybmsah;

    /**
     * 决定文书生效日期
     */
    private Date jdwssxrq;
    /**
     * 审核结果
     */
    private String shjgName;

    /**
     * 统一赋号
     */
    private String tyfh;


    /**
     * 法院案件标识
     */
    private String fyajbs;

    /**
     * 案件名称
     */
    private String ajmc;

    /**
     * 主要犯罪事实
     */
    private String zyfzss;

    /**
     * 公安嫌疑人编号
     */
    private String gaxyrbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xb;

    /**
     * 民族
     */
    private String mz;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 是否成年
     */
    private String sfcn;

    /**
     * 未成年
     */
    private String wcn;

    /**
     * 是否有精神病
     */
    private String sfyjsb;

    /**
     * 鉴定机构
     */
    private String jdjg;

    /**
     * 是否有传染病
     */
    private String sfycrb;

    /**
     * 传染病类型
     */
    private String crblx;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 婚姻状况
     */
    private String hyzk;

    /**
     * 职业
     */
    private String zy;

    /**
     * 就业就学情况
     */
    private String jyjxqk;

    /**
     * 现政治面貌
     */
    private String xzzmm;

    /**
     * 原政治面貌
     */
    private String yzzmm;

    /**
     * 原工作单位
     */
    private String gzdw;

    /**
     * 单位联系电话
     */
    private String dwlxdh;

    /**
     * 个人联系电话
     */
    private String grlxdh;

    /**
     * 国籍
     * 使用万达字典 3.11国籍
     */
    private String gj;

    /**
     * 有无家庭成员及主要社会关系
     */
    private String ywjtcyjzyshgx;

    /**
     * 照片
     */
    private String zp;

    /**
     * 户籍地是否与居住地相同
     */
    private String hjdsfyjzdxt;

    /**
     * 住所地
     */
    private String zsd;

    /**
     * 住所地详细地址
     */
    private String zsdxxdz;

    /**
     * 户籍所在地
     */
    private String hjszd;

    /**
     * 户籍地址明细
     */
    private String hjdzmx;

    /**
     * 是否有前科
     */
    private String sfyqk;

    /**
     * 是否累犯
     */
    private String sflf;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 社区矫正期限
     */
    private String sqjzqx;

    /**
     * 社区矫正开始日期
     */
    private String sqjzksrq;

    /**
     * 社区矫正结束日期
     */
    private String sqjzjsrq;

    /**
     * 矫正机构
     */
    private String jzjg;

    /**
     * 执行通知书文号
     */
    private String zxtzswh;

    /**
     * 执行通知书日期
     */
    private String zxtzsrq;

    /**
     * 交付执行日期
     */
    private String jfzxrq;

    /**
     * 移交罪犯机关类型
     */
    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */
    private String yjzfjgmc;

    /**
     * 生效判决书字号
     * ==决定文书编号JDWSBH
     */
    private String sxpjszh;

    /**
     * 判决文书生效日期
     * ==文书生效日期WSSXRQ
     */
    private String pjwssxrq;

    /**
     * 原羁押场所
     */
    private String yjycs;

    /**
     * 是否调查评估
     */
    private String sfdcpg;

    /**
     * 调查评估意见
     */
    private String dcpgyj;

    /**
     * 调查意见采信情况
     */
    private String dcyjcxqk;

    /**
     * 犯罪类型
     */
    private String fzlx;

    /**
     * 具体罪名
     */
    private String jtzm;

    /**
     * 管制期限
     */
    private String gzqx;

    /**
     * 缓刑考验期限
     */
    private String hxkyqx;

    /**
     * 是否数罪并罚
     */
    private String sfszbf;

    /**
     * 判决刑种
     */
    private String pjxz;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 判决刑期开始日期
     */
    private String pjxqksrq;

    /**
     * 判决刑期结束日期
     */
    private String pjxqjsrq;

    /**
     * 有期徒刑期限
     */
    private String yqtxqx;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 审判机关名称
     */
    private String spjgmc;

    /**
     * 剥夺政治权利年限
     */
    private String bdzzqlnx;

    /**
     * 具体罚款金额
     */
    private String jtfkje;

    /**
     * 驱逐出境
     */
    private String qzcj;

    /**
     * 没收财产
     */
    private String mscc;

    /**
     * 是否“五独”
     */
    private String sfwd;

    /**
     * 是否“五涉”
     */
    private String sfws;

    /**
     * 是否有“四史”
     */
    private String sfyss;

}
