package com.concise.gen.coordinateinvestigate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.acceptbaseinfo.vo.RecentCollaborationVo;
import com.concise.gen.acceptbaseinfo.vo.StatisticsVo;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调查评估协查
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
public interface CoordinateInvestigateMapper extends BaseMapper<CoordinateInvestigate> {
    List<StatisticsVo> getAmountByDate(@Param("date") String date);

    List<RecentCollaborationVo> recentCollaboration();

    List<StatisticsVo> monthChange();

    List<StatisticsVo> collaborativeInstitutions();
}
