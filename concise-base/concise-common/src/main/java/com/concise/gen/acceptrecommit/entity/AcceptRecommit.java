package com.concise.gen.acceptrecommit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公安再犯罪协同接收表
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
@Data
@TableName("accept_recommit")
public class AcceptRecommit {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private int deleted;
    /**再犯日期*/
    private Date zfrq;

    private String taskId;

    /**
     * 推送单位
     * 再犯接收单位编码有问题 使用矫正人员所在机构
     */
    private String tsdw;

    /**
     * 推送单位名称
     */
    private String tsdwmc;

    /**
     * 送达时间
     */
    private Date sdsj;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 社区矫正案件编号
     **/
    private String sqjzajbh;
    /**
     * 公安案件编号
     **/
    private String gaajbh;

    /**
     * 嫌疑人编号
     */
    private String xyrbh;

    /**
     * 案件类型
     */
    private String ajlx;
    private String ajlxmc;

    /**
     * 措施类型
     */
    private String cslx;
    private String cslxmc;

    /**
     * 措施内容
     */
    private String csnr;

    /**
     * 办案单位
     */
    private String badw;

    /**
     * 办案单位名称
     */
    private String badwmc;

    /**
     * 主办人
     */
    private String zbr;

    /**
     * 主办人联系电话
     */
    private String zbrlxdh;

    /**
     * 社区矫正对象编号
     */
    private String sqjzdxbh;

    /**
     * 矫正单位
     */
    private String jzdw;

    /**
     * 矫正单位名称
     */
    private String jzdwmc;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 矫正类别
     */
    private String jzlb;

    /**
     * 执行地县级人民检察院
     */
    private String qxjcy;

    /**
     * 矫正开始时间
     */
    private Date sqjzkssj;

    /**
     * 矫正结束时间
     */
    private Date sqjzjssj;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 判决日期
     */
    private Date pjrq;

    /**
     * 判决罪名
     */
    private String pjzm;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 矫正单位联系人
     */
    private String jzjglxr;

    /**
     * 联系电话
     */
    private String jzjglxrdh;

    /**
     * 文书号
     */
    private String wsh;

    /**
     * 文书名称
     */
    private String wsmc;

    /**
     * 上级矫正单位id
     */
    private String sjjzdw;

    /**
     * 上级矫正单位名称
     */
    private String sjjzdwmc;

    /**
     * 反馈日期，年-月-日
     */
    private String fkrq;

    /**
     * 操作时间
     */
    private Date czsj;
    /**
     * 操作人
     */
    private String czr;

    /**
     * 备注说明
     */
    private String bzsm;

    /**
     * 通知回执文书附件id集合，用逗号隔开
     */
    private String hzws;

    /**
     * 反馈状态，0-未反馈，1-已反馈云签章，2-已反馈手动签章
     */
    private int feedbackStatus;

    /**
     * 反馈类型，1-云签章，2-手动签章
     */
    private int feedbackType;

    /**
     * 是否已云签章，0-未签章，1-已签章
     */
    @TableField(value = "signed_")
    private int signed;

    /**
     * 通知回执文书附件列表
     */
    @TableField(exist = false)
    private List<SysFileInfoVO> hzwsList;
}
