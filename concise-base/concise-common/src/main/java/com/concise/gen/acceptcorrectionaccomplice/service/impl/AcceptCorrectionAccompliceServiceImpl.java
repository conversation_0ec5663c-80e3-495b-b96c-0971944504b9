package com.concise.gen.acceptcorrectionaccomplice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionaccomplice.entity.AcceptCorrectionAccomplice;
import com.concise.gen.acceptcorrectionaccomplice.enums.AcceptCorrectionAccompliceExceptionEnum;
import com.concise.gen.acceptcorrectionaccomplice.mapper.AcceptCorrectionAccompliceMapper;
import com.concise.gen.acceptcorrectionaccomplice.param.AcceptCorrectionAccompliceParam;
import com.concise.gen.acceptcorrectionaccomplice.service.AcceptCorrectionAccompliceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 矫正对象同案犯信息信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:23
 */
@Service
public class AcceptCorrectionAccompliceServiceImpl extends ServiceImpl<AcceptCorrectionAccompliceMapper, AcceptCorrectionAccomplice> implements AcceptCorrectionAccompliceService {

    @Override
    public PageResult<AcceptCorrectionAccomplice> page(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        QueryWrapper<AcceptCorrectionAccomplice> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptCorrectionAccompliceParam)) {

            queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getContactId, acceptCorrectionAccompliceParam.getContactId());

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getXm())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getXm, acceptCorrectionAccompliceParam.getXm());
            }
            // 根据证件类型 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getZjlx())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getZjlx, acceptCorrectionAccompliceParam.getZjlx());
            }
            // 根据证件号码 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getZjhm())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getZjhm, acceptCorrectionAccompliceParam.getZjhm());
            }
            // 根据性别 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getXb())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getXb, acceptCorrectionAccompliceParam.getXb());
            }
            // 根据出生日期 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getCsrq())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getCsrq, acceptCorrectionAccompliceParam.getCsrq());
            }
            // 根据罪名 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getZm())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getZm, acceptCorrectionAccompliceParam.getZm());
            }
            // 根据被判处刑罚及所在监所 查询
            if (ObjectUtil.isNotEmpty(acceptCorrectionAccompliceParam.getBpcxfjszjs())) {
                queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getBpcxfjszjs, acceptCorrectionAccompliceParam.getBpcxfjszjs());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionAccomplice> list(String contactId) {
        QueryWrapper<AcceptCorrectionAccomplice> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcceptCorrectionAccomplice::getContactId, contactId);
        return this.list(queryWrapper);
    }

    @Override
    public void add(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        AcceptCorrectionAccomplice acceptCorrectionAccomplice = new AcceptCorrectionAccomplice();
        BeanUtil.copyProperties(acceptCorrectionAccompliceParam, acceptCorrectionAccomplice);
        this.save(acceptCorrectionAccomplice);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        this.removeById(acceptCorrectionAccompliceParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        AcceptCorrectionAccomplice acceptCorrectionAccomplice = this.queryAcceptCorrectionAccomplice(acceptCorrectionAccompliceParam);
        BeanUtil.copyProperties(acceptCorrectionAccompliceParam, acceptCorrectionAccomplice);
        this.updateById(acceptCorrectionAccomplice);
    }

    @Override
    public AcceptCorrectionAccomplice detail(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        return this.queryAcceptCorrectionAccomplice(acceptCorrectionAccompliceParam);
    }

    /**
     * 获取矫正对象同案犯信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:23
     */
    private AcceptCorrectionAccomplice queryAcceptCorrectionAccomplice(AcceptCorrectionAccompliceParam acceptCorrectionAccompliceParam) {
        AcceptCorrectionAccomplice acceptCorrectionAccomplice = this.getById(acceptCorrectionAccompliceParam.getId());
        if (ObjectUtil.isNull(acceptCorrectionAccomplice)) {
            throw new ServiceException(AcceptCorrectionAccompliceExceptionEnum.NOT_EXIST);
        }
        return acceptCorrectionAccomplice;
    }
}
