package com.concise.gen.accepttemporarilyoutsideprison.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseAcceptEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 暂予监外接收表
 *
 * <AUTHOR>
 * @date 2022-12-01 10:33:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("accept_temporarily_outside_prison")
public class AcceptTemporarilyOutsidePrison extends BaseAcceptEntity {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**罪犯编号*/
    private String zfbh;
    /**
     * 审核结果
     */
    private String shjgName;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 案件编号
     */
    private String ajbs;

    /**
     * 反馈法院机构code
     */
    private String fkFyCode;
    /**
     * 反馈看守所机构code
     */
    private String fkKssCode;
    /**
     * 反馈检察院机构code
     */
    private String fkJcyCode;
    /**
     * 反馈公安机构code
     */
    private String fkGaCode;

    /**
     * 案件名称
     */
    private String ajmc;

    /**
     * 公安嫌疑人编号
     */
    private String gaxyrbh;

    /**
     * 检察院嫌疑人编号
     */
    private String jcyxyrbh;


    /**
     * 矫正机构名称
     */
    private String jzjgName;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 民族
     */
    private String mz;

    /**
     * 出生地
     */
    private String csd;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 决定书文号
     */
    private String jdswh;

    /**
     * 是否决定暂予监外执行
     */
    private String sfjdzyjwzx;

    /**
     * 决定（不）暂予监外执行日期
     */
    private Date jdzyjwzxrq;

    /**
     * 暂予监外执行决定机关
     */
    private String zyjwzxjdjg;

    /**
     * （不）暂予监外执行原因
     */
    private String zyjwzxyy;

    /**
     * 矫正机构
     */
    private String jzjg;

    /**
     * 交付执行日期
     */
    private Date jfzxrq;

    /**
     * 移交罪犯机关类型
     */
    private String yjzfjglx;

    /**
     * 移交罪犯机关名称
     */
    private String yjzfjgmc;

    /**
     * 矫正期限
     */
    private String jzqx;

    /**
     * 暂予监外执行起日
     */
    private Date zyjwzxqr;

    /**
     * 暂予监外执行止日
     */
    private Date zyjwzxzr;

    /**
     * 生效判决机关
     */
    private String sxpjjg;

    /**
     * 判决文书文号
     */
    private String pjwswh;

    /**
     * 判决日期
     */
    private Date pjrq;

    /**
     * 判决罪名
     */
    private String pjzm;

    /**
     * 判决其他罪名
     */
    private String pjqtzm;

    /**
     * 主刑
     */
    private String zx;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 原刑期起日
     */
    private Date yxqqr;

    /**
     * 原刑期止日
     */
    private Date yxqzr;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 附加刑具体情况
     */
    private String fjxjtqk;

    /**
     * 缓刑考验期
     */
    private String hxkyq;

    /**
     * 财产性判项
     */
    private String ccxpx;

    /**
     * 罚金金额（万元）
     */
    private String fjjewy;

    /**
     * 没收财产金额（万元）
     */
    private String msccjewy;

    /**
     * 其他财产性判项金额（万元）
     */
    private String qtccxpxjewy;

    /**
     * 剥夺政治权利期限
     */
    private String bdzzqlqx;

    /**
     * 未能入矫原因
     **/
    private String wnrjyy;


}
