package com.concise.gen.dataCenter.arrest. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.dataCenter.arrest. param.ArrestDcParam;
import com.concise.gen.dataCenter.arrest. service.ArrestDcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 提请逮捕控制器
 *
 * <AUTHOR>
 * @date 2023-09-19 17:30:07
 */
@Api(tags = "提请逮捕")
@RestController
public class ArrestDcController {

    @Resource
    private ArrestDcService arrestDcService;

    /**
     * 查询提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    @Permission
    @GetMapping("/arrestDc/page")
    @ApiOperation("提请逮捕_分页查询")
    public ResponseData page(ArrestDcParam arrestDcParam) {
        return new SuccessResponseData(arrestDcService.page(arrestDcParam));
    }

    /**
     * 添加提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    @Permission
    @PostMapping("/arrestDc/add")
    @ApiOperation("提请逮捕_增加")
    @BusinessLog(title = "提请逮捕_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(ArrestDcParam.add.class) ArrestDcParam arrestDcParam) {
        arrestDcService.add(arrestDcParam);
        return new SuccessResponseData();
    }

    /**
     * 删除提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    @Permission
    @PostMapping("/arrestDc/delete")
    @ApiOperation("提请逮捕_删除")
    @BusinessLog(title = "提请逮捕_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(ArrestDcParam.delete.class) ArrestDcParam arrestDcParam) {
        arrestDcService.delete(arrestDcParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    @Permission
    @PostMapping("/arrestDc/edit")
    @ApiOperation("提请逮捕_编辑")
    @BusinessLog(title = "提请逮捕_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(ArrestDcParam.edit.class) ArrestDcParam arrestDcParam) {
        arrestDcService.edit(arrestDcParam);
        return new SuccessResponseData();
    }

    /**
     * 查看提请逮捕
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    @Permission
    @GetMapping("/arrestDc/detail")
    @ApiOperation("提请逮捕_查看")
    public ResponseData detail(@Validated(ArrestDcParam.detail.class) ArrestDcParam arrestDcParam) {
        return new SuccessResponseData(arrestDcService.detail(arrestDcParam));
    }

    /**
     * 提请逮捕列表
     *
     * <AUTHOR>
     * @date 2023-09-19 17:30:07
     */
    @Permission
    @GetMapping("/arrestDc/list")
    @ApiOperation("提请逮捕_列表")
    public ResponseData list(ArrestDcParam arrestDcParam) {
        return new SuccessResponseData(arrestDcService.list(arrestDcParam));
    }

}
