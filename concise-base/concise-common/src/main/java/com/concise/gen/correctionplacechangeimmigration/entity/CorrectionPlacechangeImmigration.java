package com.concise.gen.correctionplacechangeimmigration.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.pojo.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 执行地变更_外省迁入
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("correction_placechange_immigration")
public class CorrectionPlacechangeImmigration extends BaseEntity {

    /**
     * 受理时间
     */
    private Date slsj;
    /**
     * 受理说明
     */
    private String slsm;
    /**
     * 受理文书
     */
    private String slws;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 委托编号
     */
    private String wtbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 拟适用社区矫正人员类型
     */
    private String nsysqjzrylx;

    /**
     * 性别
     */
    private String xb;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date csrq;

    /**
     * 居住地地址(省)
     */
    private String jzddzP;

    /**
     * 居住地地址(市)
     */
    private String jzddzC;

    /**
     * 居住地地址
     */
    private String jzddz;

    /**
     * 是否有居住地地址明细
     */
    private String sfyjzddzmx;

    /**
     * 居住地地址明细
     */
    private String jzddzmx;

    /**
     * 户籍是否与居住地相同
     */
    private String hjsfyjzdxt;

    /**
     * 户籍地址(省)
     */
    private String hjdzP;

    /**
     * 户籍地址(市)
     */
    private String hjdzC;

    /**
     * 户籍地址
     */
    private String hjdz;

    /**
     * 是否有户籍地址明细
     */
    private String sfyhjdzmx;

    /**
     * 户籍地址明细
     */
    private String hjdzmx;

    /**
     * 工作单位
     */
    private String hzdw;

    /**
     * 拟适用矫正类别
     */
    private String nsyjzlb;

    /**
     * 是否有原判刑期
     */
    private String sfyypxq;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 原判刑期开始日期
     */
    @Excel(name = "原判刑期开始日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date ypxqksrq;

    /**
     * 原判刑期结束日期
     */
    @Excel(name = "原判刑期结束日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date ypxqjsrq;

    /**
     * 原判刑罚
     */
    private String ypxf;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 判决机关
     */
    private String pjjg;

    /**
     * 判决日期
     */
    @Excel(name = "判决日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pjrq;

    /**
     * 指派调查单位
     */
    private String zpdcdwmc;

    /**
     * 指派调查单位ID
     */
    private String zpdcdwId;

    /**
     * 指派人
     */
    private String zpr;

    /**
     * 指派时间
     */
    @Excel(name = "指派时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date zpsj;

    /**
     * 指派备注
     */
    private String zpbz;

    /**
     * 家庭和社会关系
     */
    private String jthshgx;

    /**
     * 社会危险性、对所居住社区的影响等情况
     */
    private String shwxxyx;

    /**
     * 拟禁止的事项
     */
    private String njzdsx;

    /**
     * 犯罪行为的后果和影响
     */
    private String fzxwdhghyx;

    /**
     * 居住地村（居）民委员会和被害人意见
     */
    private String wyhhbhryj;

    /**
     * 调查评估意见
     */
    private String dcpgyj;

    /**
     * 调查人
     */
    private String dcr;

    /**
     * 调查单位
     */
    private String dcdw;

    /**
     * 调查单位ID
     */
    private String dcdwId;

    /**
     * 指派矫正单位
     */
    private String zpjzdw;

    /**
     * 指派矫正单位ID
     */
    private String zpjzdwId;

    /**
     * 调查意见审核人
     */
    private String dcyjshr;

    /**
     * 附件1
     */
    private String file1;

    /**
     * 是否按时报到
     */
    private String sfasbd;

    /**
     * 报到时间
     */
    @Excel(name = "报到时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date bdsj;

    /**
     * 反馈时间
     */
    @Excel(name = "反馈时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date fksj;

    /**
     * 反馈备注
     */
    private String fkbz;

    /**
     * 迁入原因
     */
    private String qryy;
    /**
     * 迁出省（市）
     */
    private String qcss;
    /**
     * 申请时间
     */
    private Date sqsj;

    /**
     * 调查状态
     */
    private String dczt;
    /**
     * 接收状态
     */
    private String jszt;

}
