package com.concise.gen.acceptrecommit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptrecommit.entity.AcceptRecommit;
import com.concise.gen.acceptrecommit.param.AcceptRecommitParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 公安再犯罪协同接收表service接口
 *
 * <AUTHOR>
 * @date 2023-08-02 11:42:25
 */
public interface AcceptRecommitService extends IService<AcceptRecommit> {

    /**
     * 查询公安再犯罪协同接收表
     *
     * <AUTHOR>
     * @date 2023-08-02 11:42:25
     */
    PageResult<AcceptRecommit> page(AcceptRecommitParam acceptRecommitParam);

    /**
     * 添加公安再犯罪协同接收表
     *
     * <AUTHOR>
     * @date 2023-08-02 11:42:25
     */
    List<AcceptCorrectionDocParam> add(AcceptRecommitParam acceptRecommitParam);

    /**
     * 编辑公安再犯罪协同接收表
     *
     * <AUTHOR>
     * @date 2023-08-02 11:42:25
     */
    void edit(AcceptRecommitParam acceptRecommitParam);

    void export(AcceptRecommitParam param);

    /**
     * 发送日志
     * @param id        矫正人员id
     * @param xm xm
     * @param jzjgId    机构id
     * @param jzjgName jzjgName
     * @param jsdw jsdw
     * @param zhuangtai 矫正状态
     */
    void sendLog(String id, String xm, String jzjgId, String jzjgName, String jsdw, String zhuangtai);

    /**
     * 日志列表
     *
     * @param id        矫正人员id
     * @param xm xm
     * @param jzjgSet    机构id
     * @param deleted
     * @return List
     */
    PageResult<Map<String, Object>> logPage(String id, String fsrq, String xm, Set<String> jzjgSet, int deleted);
    void logExport(String xm, Set<String> jzjgSet);

    /**
     * 获取未签章pdf
     *
     * @param acceptRecommit acceptRecommit
     * @param response response
     */
    void getNoSignPdf(AcceptRecommit acceptRecommit, HttpServletResponse response);

    /**
     * 获取已签章pdf
     *
     * @param acceptRecommit acceptRecommit
     * @param response response
     */
    void getSignedPdf(AcceptRecommit acceptRecommit, HttpServletResponse response);

    /**
     * 生成文书编号
     *
     * @param acceptRecommit
     * @return
     */
    MultipartFile getSignedPdfFile(AcceptRecommit acceptRecommit);

    /**
     * 生成文书编号
     *
     * @param acceptRecommit acceptRecommit
     * @return AcceptRecommit
     */
    String generateDocumentNumber(AcceptRecommit acceptRecommit);

    String getZfrq(String id);

    AcceptRecommit generateFeedbackInfo(AcceptRecommitParam param, String userName);
}
