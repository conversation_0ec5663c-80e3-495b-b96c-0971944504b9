package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP5;
import com.concise.gen.ciInvestigate.param.InvestigateP5Param;
import java.util.List;

/**
 * 迁入调查评估_初审集体评议service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:21
 */
public interface InvestigateP5Service extends IService<InvestigateP5> {

    /**
     * 查询迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    PageResult<InvestigateP5> page(InvestigateP5Param investigateP5Param);

    /**
     * 迁入调查评估_初审集体评议列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    List<InvestigateP5> list(InvestigateP5Param investigateP5Param);

    /**
     * 添加迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    void add(InvestigateP5Param investigateP5Param);

    /**
     * 删除迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    void delete(InvestigateP5Param investigateP5Param);

    /**
     * 编辑迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
    void edit(InvestigateP5Param investigateP5Param);

    /**
     * 查看迁入调查评估_初审集体评议
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:21
     */
     InvestigateP5 detail(InvestigateP5Param investigateP5Param);
}
