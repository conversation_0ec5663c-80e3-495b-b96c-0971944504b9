package com.concise.gen.coordinatesupervisesubmit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinatesupervisesubmit.entity.CoordinateSuperviseSubmit;
import com.concise.gen.coordinatesupervisesubmit.param.CoordinateSuperviseSubmitParam;
import java.util.List;

/**
 * 提请外出监管协同service接口
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
 */
public interface CoordinateSuperviseSubmitService extends IService<CoordinateSuperviseSubmit> {

    /**
     * 查询提请外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
    PageResult<CoordinateSuperviseSubmit> page(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam);

    /**
     * 提请外出监管协同列表
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
    List<CoordinateSuperviseSubmit> list(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam);

    /**
     * 添加提请外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
    void add(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam);

    /**
     * 删除提请外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
    void delete(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam);

    /**
     * 编辑提请外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
    void edit(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam);

    /**
     * 查看提请外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:04
     */
     CoordinateSuperviseSubmit detail(CoordinateSuperviseSubmitParam coordinateSuperviseSubmitParam);
}
