package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP6Param;
import com.concise.gen.ciInvestigate. service.InvestigateP6Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估_审批控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:23
 */
@Api(tags = "迁入调查评估_审批")
@RestController
public class InvestigateP6Controller {

    @Resource
    private InvestigateP6Service investigateP6Service;

    /**
     * 查询迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    @Permission
    @GetMapping("/investigateP6/page")
    @ApiOperation("迁入调查评估_审批_分页查询")
    @BusinessLog(title = "迁入调查评估_审批_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP6Param investigateP6Param) {
        return new SuccessResponseData(investigateP6Service.page(investigateP6Param));
    }

    /**
     * 添加迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    @Permission
    @PostMapping("/investigateP6/add")
    @ApiOperation("迁入调查评估_审批_增加")
    @BusinessLog(title = "迁入调查评估_审批_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP6Param.add.class) InvestigateP6Param investigateP6Param) {
        investigateP6Service.add(investigateP6Param);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    @Permission
    @PostMapping("/investigateP6/delete")
    @ApiOperation("迁入调查评估_审批_删除")
    @BusinessLog(title = "迁入调查评估_审批_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP6Param.delete.class) InvestigateP6Param investigateP6Param) {
        investigateP6Service.delete(investigateP6Param);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    @Permission
    @PostMapping("/investigateP6/edit")
    @ApiOperation("迁入调查评估_审批_编辑")
    @BusinessLog(title = "迁入调查评估_审批_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP6Param.edit.class) InvestigateP6Param investigateP6Param) {
        investigateP6Service.edit(investigateP6Param);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估_审批
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    @Permission
    @GetMapping("/investigateP6/detail")
    @ApiOperation("迁入调查评估_审批_查看")
    @BusinessLog(title = "迁入调查评估_审批_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP6Param.detail.class) InvestigateP6Param investigateP6Param) {
        return new SuccessResponseData(investigateP6Service.detail(investigateP6Param));
    }

    /**
     * 迁入调查评估_审批列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:23
     */
    @Permission
    @GetMapping("/investigateP6/list")
    @ApiOperation("迁入调查评估_审批_列表")
    @BusinessLog(title = "迁入调查评估_审批_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP6Param investigateP6Param) {
        return new SuccessResponseData(investigateP6Service.list(investigateP6Param));
    }

}
