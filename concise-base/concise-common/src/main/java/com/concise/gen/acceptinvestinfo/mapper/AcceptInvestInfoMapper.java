package com.concise.gen.acceptinvestinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.concise.gen.acceptbaseinfo.vo.StatVo;
import com.concise.gen.acceptinvestinfo.entity.AcceptInvestInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 调查评估信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
 */
public interface AcceptInvestInfoMapper extends BaseMapper<AcceptInvestInfo> {

    /**
     * 统计 省
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel1(@Param("start") String start, @Param("end") String end);

    /**
     * 统计 市
     * @param orgId orgId
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel2(@Param("orgId") String orgId, @Param("start") String start, @Param("end") String end);
    /**
     * 统计 区县
     * @param orgId orgId
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel3(@Param("orgId") String orgId, @Param("start") String start, @Param("end") String end);
    /**
     * 统计 司法所
     * @param orgId orgId
     * @param start start
     * @param end end
     * @return StatVo
     */
    List<StatVo> statisticsLevel4(@Param("orgId") String orgId, @Param("start") String start, @Param("end") String end);
}
