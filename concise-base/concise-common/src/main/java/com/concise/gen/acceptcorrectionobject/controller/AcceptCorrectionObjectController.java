package com.concise.gen.acceptcorrectionobject. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.response.ErrorResponseData;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.acceptbaseinfo.entity.AcceptBaseInfo;
import com.concise.gen.acceptbaseinfo.service.AcceptBaseInfoService;
import com.concise.gen.acceptcorrectionobject.entity.AcceptCorrectionObject;
import com.concise.gen.acceptcorrectionobject.param.AcceptCorrectionObjectParam;
import com.concise.gen.acceptcorrectionobject.service.AcceptCorrectionObjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 矫正对象信息接收表控制器
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:05
 */
@Api(tags = "矫正对象信息接收表")
@RestController
public class AcceptCorrectionObjectController {

    @Resource
    private AcceptCorrectionObjectService acceptCorrectionObjectService;
    @Resource
    private AcceptBaseInfoService acceptBaseInfoService;
    @Resource
    private SysFileInfoService sysFileInfoService;

    /**
     * 查询矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     */
    @Permission
    @GetMapping("/acceptCorrectionObject/page")
    @ApiOperation("矫正对象信息接收表_分页查询")
    public ResponseData page(AcceptCorrectionObjectParam acceptCorrectionObjectParam) {
        return new SuccessResponseData(acceptCorrectionObjectService.page(acceptCorrectionObjectParam));
    }

    /**
     * 编辑矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     */
    @Permission
    @PostMapping("/acceptCorrectionObject/feedback")
    @ApiOperation("矫正对象信息_反馈")
    @BusinessLog(title = "矫正对象信息_反馈", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData feedback(@RequestBody @Validated(AcceptCorrectionObjectParam.edit.class) AcceptCorrectionObjectParam param) {
        if (acceptCorrectionObjectService.feedback(param)) {
            return new SuccessResponseData();
        }else {
            return new ErrorResponseData(500,"网络问题请稍后再试");
        }
    }


    @PostMapping("/acceptCorrectionObject/terminate")
    @BusinessLog(title = "流程终止", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData terminate(@RequestBody @Validated(AcceptCorrectionObjectParam.detail.class) AcceptCorrectionObjectParam param) {
        acceptCorrectionObjectService.lambdaUpdate()
                .set(AcceptCorrectionObject::getZt, "98")
                .eq(AcceptCorrectionObject::getId, param.getId())
                .update();
        acceptBaseInfoService.lambdaUpdate()
                .set(AcceptBaseInfo::getTerminateReason, param.getTerminateReason())
                .set(AcceptBaseInfo::getZt, "98")
                .eq(AcceptBaseInfo::getId, param.getId())
                .update();
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象信息接收表
     *
     * <AUTHOR>
     * @date 2022-06-26 21:21:05
     */
    @Permission
    @GetMapping("/acceptCorrectionObject/detail")
    @ApiOperation("矫正对象信息接收表_查看")
    public ResponseData detail(@Validated(AcceptCorrectionObjectParam.detail.class) AcceptCorrectionObjectParam param) {
        return new SuccessResponseData(acceptCorrectionObjectService.detail(param.getId()));
    }


    @GetMapping("/acceptCorrectionObject/sendwd")
    public ResponseData send(@Validated(AcceptCorrectionObjectParam.detail.class) AcceptCorrectionObjectParam param) {
        acceptCorrectionObjectService.sendWd(param.getId());
        return new SuccessResponseData();
    }

    @GetMapping("/acceptCorrectionObject/gethzcl")
    public ResponseData gethzcl(AcceptCorrectionObjectParam param) {
        AcceptCorrectionObject byId = acceptCorrectionObjectService.getById(param.getId());
        if (byId != null) {
            return new SuccessResponseData(sysFileInfoService.getDetailByIds(byId.getHzcl()));
        }else {
            return new SuccessResponseData();
        }
    }

    @PostMapping("/acceptCorrectionObject/getNoSignPdf")
    public void getNoSignPdf(@RequestBody AcceptCorrectionObjectParam param) {
        acceptCorrectionObjectService.getNoSignPdf(param);
    }
    @PostMapping("/acceptCorrectionObject/getSignedPdf")
    public void getSignedPdf(@RequestBody AcceptCorrectionObjectParam param) {
        acceptCorrectionObjectService.getSignedPdf(param);
    }
}
