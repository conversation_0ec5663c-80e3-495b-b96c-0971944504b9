package com.concise.gen.coordinateinvestigate.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP2;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateP2ExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateP2Mapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP2Param;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP2Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估协查_接收service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:51
 */
@Service
public class CoordinateInvestigateP2ServiceImpl extends ServiceImpl<CoordinateInvestigateP2Mapper, CoordinateInvestigateP2> implements CoordinateInvestigateP2Service {

    @Override
    public PageResult<CoordinateInvestigateP2> page(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        QueryWrapper<CoordinateInvestigateP2> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(coordinateInvestigateP2Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP2Param.getPid())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP2::getPid, coordinateInvestigateP2Param.getPid());
            }
            // 根据接收状态 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP2Param.getZt())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP2::getZt, coordinateInvestigateP2Param.getZt());
            }
            // 根据接收人 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP2Param.getJsr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP2::getJsr, coordinateInvestigateP2Param.getJsr());
            }
            // 根据接收时间 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP2Param.getJssj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP2::getJssj, coordinateInvestigateP2Param.getJssj());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP2Param.getBz())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP2::getBz, coordinateInvestigateP2Param.getBz());
            }
            // 根据退回理由 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP2Param.getThly())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP2::getThly, coordinateInvestigateP2Param.getThly());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateP2> list(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        CoordinateInvestigateP2 coordinateInvestigateP2 = new CoordinateInvestigateP2();
        BeanUtil.copyProperties(coordinateInvestigateP2Param, coordinateInvestigateP2);
        this.save(coordinateInvestigateP2);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        this.removeById(coordinateInvestigateP2Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        CoordinateInvestigateP2 coordinateInvestigateP2 = this.queryCoordinateInvestigateP2(coordinateInvestigateP2Param);
        BeanUtil.copyProperties(coordinateInvestigateP2Param, coordinateInvestigateP2);
        this.updateById(coordinateInvestigateP2);
    }

    @Override
    public CoordinateInvestigateP2 detail(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        return this.queryCoordinateInvestigateP2(coordinateInvestigateP2Param);
    }

    /**
     * 获取调查评估协查_接收
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:51
     */
    private CoordinateInvestigateP2 queryCoordinateInvestigateP2(CoordinateInvestigateP2Param coordinateInvestigateP2Param) {
        CoordinateInvestigateP2 coordinateInvestigateP2 = this.getById(coordinateInvestigateP2Param.getId());
        if (ObjectUtil.isNull(coordinateInvestigateP2)) {
            throw new ServiceException(CoordinateInvestigateP2ExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateP2;
    }

}
