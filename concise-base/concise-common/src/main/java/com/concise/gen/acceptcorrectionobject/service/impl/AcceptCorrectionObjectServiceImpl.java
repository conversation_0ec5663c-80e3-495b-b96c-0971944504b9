package com.concise.gen.acceptcorrectionobject.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.base.enums.OrgTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.util.ElectronicSignatureUtil;
import com.concise.common.util.HttpServletUtil;
import com.concise.common.util.PdfFillUtil;
import com.concise.common.util.ResponseUtil;
import com.concise.gen.acceptbaseinfo.entity.AcceptBaseInfo;
import com.concise.gen.acceptbaseinfo.param.AcceptBaseInfoParam;
import com.concise.gen.acceptbaseinfo.service.AcceptBaseInfoService;
import com.concise.gen.acceptchargesinfo.entity.AcceptChargesInfo;
import com.concise.gen.acceptchargesinfo.service.AcceptChargesInfoService;
import com.concise.gen.acceptcorrectionaccomplice.entity.AcceptCorrectionAccomplice;
import com.concise.gen.acceptcorrectionaccomplice.service.AcceptCorrectionAccompliceService;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptcorrectionfamily.entity.AcceptCorrectionFamily;
import com.concise.gen.acceptcorrectionfamily.service.AcceptCorrectionFamilyService;
import com.concise.gen.acceptcorrectionforbid.entity.AcceptCorrectionForbid;
import com.concise.gen.acceptcorrectionforbid.service.AcceptCorrectionForbidService;
import com.concise.gen.acceptcorrectionobject.entity.AcceptCorrectionObject;
import com.concise.gen.acceptcorrectionobject.enums.AcceptCorrectionObjectExceptionEnum;
import com.concise.gen.acceptcorrectionobject.mapper.AcceptCorrectionObjectMapper;
import com.concise.gen.acceptcorrectionobject.param.AcceptCorrectionObjectParam;
import com.concise.gen.acceptcorrectionobject.service.AcceptCorrectionObjectService;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.gen.signaturemaintenance.service.SignatureMaintenanceService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.webservice.service.SendCorrectionObjectService;
import com.concise.gen.ywxtcount.service.YwxtCountService;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.property.TextAlignment;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.List;

import static com.concise.common.consts.SymbolConstant.COMMA;

/**
 * 矫正对象信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-26 21:21:05
 */
@Service
public class AcceptCorrectionObjectServiceImpl extends ServiceImpl<AcceptCorrectionObjectMapper, AcceptCorrectionObject> implements AcceptCorrectionObjectService {


    @Resource
    private AcceptReturnInfoService acceptReturnInfoService;
    @Resource
    private OrgCommonService orgService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private AcceptCorrectionAccompliceService acceptCorrectionAccompliceService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private AcceptCorrectionFamilyService acceptCorrectionFamilyService;
    @Resource
    private AcceptCorrectionForbidService acceptCorrectionForbidService;
    @Resource
    private AcceptBaseInfoService acceptBaseInfoService;
    @Resource
    private AcceptChargesInfoService acceptChargesInfoService;
    @Resource
    private SendCorrectionObjectService sendService;
    @Resource
    private YwxtNoticeService ywxtNoticeService;
    @Resource
    private YwxtCountService ywxtCountService;
    @Resource
    private SignatureMaintenanceService signatureMaintenanceService;


    @Value("${electronicSignature.fontKaiPath}")
    private String fontKaiPath;

    @Value("${electronicSignature.fontPath}")
    private String fontPath;

    @Resource
    private ElectronicSignatureUtil electronicSignatureUtil;

    @Override
    public PageResult<AcceptCorrectionObject> page(AcceptCorrectionObjectParam param) {
        QueryWrapper<AcceptCorrectionObject> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(param.getJsdw())) {
                queryWrapper.lambda().and(i -> i.eq(AcceptCorrectionObject::getJsdwId, param.getJsdw()).or().like(AcceptCorrectionObject::getJsdwPids, param.getJsdw()));
            }
            // 数据状态 查询
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(AcceptCorrectionObject::getZt, param.getZt());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(AcceptCorrectionObject::getSdsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(AcceptCorrectionObject::getSdsj, param.getSearchEndTime());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(AcceptCorrectionObject::getXm, param.getXm());
            }

        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptCorrectionObjectParam param) {
        AcceptCorrectionObject one = this.lambdaQuery()
                .eq(AcceptCorrectionObject::getJsdw, param.getJsdw())
                .eq(AcceptCorrectionObject::getZjhm, param.getZjhm())
                .orderByDesc(AcceptCorrectionObject::getSdsj)
                .last("limit 1")
                .one();

        if (one != null && one.getSdsj() != null && !"2".equals(one.getZt())) {
            List<AcceptCorrectionDoc> oldDocList = acceptCorrectionDocService.list(one.getId());
            Boolean check = acceptReturnInfoService.check(one.getSdsj(), param.getDocList(), oldDocList);
            if (check) {
                if ("0".equals(one.getZt())){
                    //旧数据未接收时退回旧数据
                    acceptReturnInfoService.save(param.getXtbh(), param.getId(), param.getTyfh(), DateUtil.parse(param.getTssj()), one.getSdsj(), one.getXm(), one.getJsdw(), one.getJsdwId(), one.getJsdwPids(), one.getJsdwmc());
                    one.setZt("2");
                    one.setShbz("系统识别为重复案件，自动退回");
                    one.setShjg("26");
                    one.setShjgName("其他");
                    one.setShry("数据质检");
                    one.setShsj(DateUtil.date());
                    this.updateById(one);

                    sendService.sendMessage(one,"数据质检");
                    acceptBaseInfoService.lambdaUpdate()
                            .set(AcceptBaseInfo::getZt, "2")
                            .set(AcceptBaseInfo::getReturned, "1")
                            .eq(AcceptBaseInfo::getId, one.getId()).update();
                }else {
                    //自动退回
                    param.setZt("2");
                    param.setReturned("1");
                    param.setShbz("系统识别为重复案件，自动退回");
                    param.setShjg("26");
                    param.setShjgName("其他");
                    param.setShry("数据质检");
                    param.setShsj(DateUtil.now());
                }
            }

        }
        AcceptCorrectionObject acceptCorrectionObject = new AcceptCorrectionObject();
        acceptCorrectionObject.setJdwssxrq(DateUtil.parse(param.getTssj()));
        BeanUtil.copyProperties(param, acceptCorrectionObject);
        if (param.getAccompliceList() != null) {
            param.getAccompliceList().forEach(accomplice -> acceptCorrectionAccompliceService.add(accomplice));
        }
        if (param.getFamilyList() != null) {
            param.getFamilyList().forEach(family -> acceptCorrectionFamilyService.add(family));
        }
        if (param.getForbidList() != null) {
            param.getForbidList().forEach(forbid -> acceptCorrectionForbidService.add(forbid));
        }
        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));
        this.save(acceptCorrectionObject);
        if ("2".equals(param.getZt())) {
            // 自动退回的消息
            sendService.sendMessage(acceptCorrectionObject,"数据质检");
        }

        AcceptBaseInfo baseInfo = new AcceptBaseInfo();
        BeanUtil.copyProperties(param, baseInfo);
        baseInfo.setXtlx("05");
        baseInfo.setXtlxmc("矫正人员接收");
        baseInfo.setSjlylxmc(OrgTypeEnum.getEnumByCode(baseInfo.getSjlylx()).getName());
        acceptBaseInfoService.save(baseInfo);

        if (ObjectUtil.isNotEmpty(param.getXm())) {
            ywxtNoticeService.buildNoticeByOrgId(
                    YwxtNoticeTypeEnum.ADD_CORRECTION_01,
                    param.getJsdwmc()+","+DateUtil.formatDate(acceptCorrectionObject.getSdsj())+","+param.getTsdwmc()+","+param.getXm(),
                    param.getTsdwmc()+","+param.getXm(),
                    param.getJsdwId());
        }
        int count1 = this.lambdaQuery().eq(AcceptCorrectionObject::getJsdw, acceptCorrectionObject.getJsdw())
                .apply("date_format (sdsj,'%Y-%m-%d') = date_format('" + acceptCorrectionObject.getSdsj() + "','%Y-%m-%d')")
                .count();
        OrgCommon org1 = orgService.getOrgByCode(acceptCorrectionObject.getJsdw());
        ywxtCountService.saveOrUpdateCount("T04", DateUtil.formatDate(acceptCorrectionObject.getSdsj()), org1.getId(), count1,true);
        return param.getDocList();
    }

    @Override
    public void delete(String taskId) {
        List<AcceptCorrectionObject> list = this.lambdaQuery().eq(AcceptCorrectionObject::getTaskId, taskId).list();
        for (AcceptCorrectionObject object : list) {
            acceptCorrectionAccompliceService.lambdaUpdate().eq(AcceptCorrectionAccomplice::getContactId,object.getId()).remove();
            acceptCorrectionFamilyService.lambdaUpdate().eq(AcceptCorrectionFamily::getContactId,object.getId()).remove();
            acceptCorrectionForbidService.lambdaUpdate().eq(AcceptCorrectionForbid::getContactId,object.getId()).remove();
            List<AcceptCorrectionDoc> docList = acceptCorrectionDocService.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, object.getId()).list();
            for (AcceptCorrectionDoc doc : docList) {
                sysFileInfoService.lambdaUpdate().eq(SysFileInfo::getBizId,doc.getId()).remove();
                acceptCorrectionDocService.removeById(doc.getId());
            }
            if (ObjectUtil.isNotEmpty(object.getHzcl())) {
                for (String fileId : object.getHzcl().split(COMMA)) {
                    sysFileInfoService.removeById(fileId);
                }
            }
            this.removeById(object.getId());
        }
    }

    @Override
    public boolean feedback(AcceptCorrectionObjectParam param) {
        AcceptCorrectionObject byId = this.getById(param.getId());
        BeanUtil.copyProperties(param, byId);
        byId.setFjx(ArrayUtil.join(param.getFjxList(),COMMA));
        byId.setSfwd(ArrayUtil.join(param.getSfwdList(),COMMA));
        byId.setSfws(ArrayUtil.join(param.getSfwsList(),COMMA));
        byId.setSfyss(ArrayUtil.join(param.getSfyssList(),COMMA));
        this.updateById(byId);
        if (ObjectUtil.isNotEmpty(param.getChargeList())) {
            acceptChargesInfoService.lambdaUpdate().eq(AcceptChargesInfo::getContactId,param.getId()).remove();
            acceptChargesInfoService.saveBatch(param.getChargeList());
        }

        AcceptCorrectionObject feedback = this.getById(param.getId());
        OrgCommon org = orgService.getById(feedback.getJzjg());
        if (org == null) {
            return false;
        }
        // 反馈
        boolean success = sendService.request4005(feedback, sysFileInfoService.getDocListByIds(feedback.getHzcl()),org);
        if (success){
            feedback.setZt("4");
            this.updateById(feedback);
            AcceptBaseInfo baseInfo = new AcceptBaseInfo();
            BeanUtil.copyProperties(param, baseInfo);
            baseInfo.setZt("4");
            acceptBaseInfoService.updateById(baseInfo);
        }
        return success;
    }

    @Override
    public void edit(AcceptBaseInfoParam param) {
        AcceptCorrectionObjectParam obj = param.getObj();
        if (obj == null) {
            return;
        }
        if (obj.getShjg().startsWith("1")) {
            obj.setZt("1");
        }else {
            obj.setZt("2");
        }

        AcceptCorrectionObject byId = this.getById(param.getId());
        BeanUtil.copyProperties(obj, byId);
        byId.setId(param.getId());
        byId.setFjx(ArrayUtil.join(obj.getFjxList(),COMMA));
        byId.setSfwd(ArrayUtil.join(obj.getSfwdList(),COMMA));
        byId.setSfws(ArrayUtil.join(obj.getSfwsList(),COMMA));
        byId.setSfyss(ArrayUtil.join(obj.getSfyssList(),COMMA));
        this.updateById(byId);

        if (ObjectUtil.isNotEmpty(obj.getChargeList())) {
            acceptChargesInfoService.lambdaUpdate().eq(AcceptChargesInfo::getContactId,param.getId()).remove();
            acceptChargesInfoService.saveBatch(obj.getChargeList());
        }

        AcceptBaseInfo baseInfo = new AcceptBaseInfo();
        baseInfo.setId(param.getId());
        baseInfo.setZt(obj.getZt());
        acceptBaseInfoService.updateById(baseInfo);

        sendService.sendMessage(this.getById(param.getId()),obj.getShry());

        if ("2".equals(obj.getZt())) {
            // 退回
            return;
        }
        //接收
        if ("all".equals(param.getSendWd())) {
            // 接收时发送给万达
            AcceptCorrectionObject sendObj = this.getById(param.getId());
            sendObj.setAccompliceList(acceptCorrectionAccompliceService.list(sendObj.getId()));
            sendObj.setForbidList(acceptCorrectionForbidService.list(sendObj.getId()));
            sendObj.setFamilyList(acceptCorrectionFamilyService.list(sendObj.getId()));
            if (!sendService.sendToWd(sendObj)) {
                this.lambdaUpdate().set(AcceptCorrectionObject::getZt, "1")
                        .eq(AcceptCorrectionObject::getId,sendObj.getId()).update();
                throw new ServiceException(AcceptCorrectionObjectExceptionEnum.SYNC_ERROR);
            }
            acceptCorrectionDocService.sendDocToWd(sendObj.getId());
        }else if(ObjectUtil.isNotEmpty(param.getSendWd())){
            acceptCorrectionDocService.sendDocToWd(param.getId(),param.getSendWd());
        }
    }

    @Override
    public AcceptCorrectionObject detail(String id) {
        AcceptCorrectionObject byId = this.getById(id);
        if (byId != null) {
            byId.setAccompliceList(acceptCorrectionAccompliceService.list(id));
            byId.setForbidList(acceptCorrectionForbidService.list(id));
            byId.setFamilyList(acceptCorrectionFamilyService.list(id));
            byId.setDocList(acceptCorrectionDocService.list(id));
            byId.setHzclList(sysFileInfoService.getDetailByIds(byId.getHzcl()));
            byId.setChargeList(acceptChargesInfoService.list(id));
            return byId;
        }
        return new AcceptCorrectionObject();
    }

    @Override
    public void sendWd(String id) {
        AcceptCorrectionObject sendWd = this.getById(id);
        sendWd.setAccompliceList(acceptCorrectionAccompliceService.list(sendWd.getId()));
        sendWd.setForbidList(acceptCorrectionForbidService.list(sendWd.getId()));
        sendWd.setFamilyList(acceptCorrectionFamilyService.list(sendWd.getId()));
        sendService.sendToWd(sendWd);
        acceptCorrectionDocService.sendDocToWd(sendWd.getId());
    }

    @Override
    public void getNoSignPdf(AcceptCorrectionObjectParam param) {

        AcceptCorrectionObject model = this.getById(param.getId());
        model.setZxtzshzwh(param.getZxtzshzwh());
        model.setJdwssxrq(DateUtil.parse(param.getJdwssxrq(),"yyyy-MM-dd HH:mm:ss"));
        model.setRjrq(DateUtil.parse(param.getRjrq(),"yyyy-MM-dd HH:mm:ss"));

        HttpServletResponse response = HttpServletUtil.getResponse();
        try {
            byte[] bytes = getNoSignPdfBytes(model);
            MultipartFile multipartFile = new MockMultipartFile("fileName.pdf", bytes);
            byte[] fileBytes = multipartFile.getBytes();
            //设置contentType
            response.setContentType("application/pdf");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            outputStream.close();
        } catch (Exception e) {
            throw new RuntimeException("获取签章pdf异常，" + e.getMessage());
        }
    }

    private byte[] getNoSignPdfBytes(AcceptCorrectionObject model) throws IOException {

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        PdfWriter pdfWriter=new PdfWriter(os);
        //2、创建文档对象
        PdfDocument pdfDocument=new PdfDocument(pdfWriter);
        //3、创建内容文档对象
        Document document=new Document(pdfDocument, PageSize.A4);
        //设置字体，解决中文显示问题
        PdfFont fontKai= PdfFontFactory.createFont(IOUtils.toByteArray(Files.newInputStream(Paths.get(fontKaiPath))), "Identity-H", true);
        PdfFont fontFs= PdfFontFactory.createFont(IOUtils.toByteArray(Files.newInputStream(Paths.get(fontPath))), "Identity-H", true);
        //创建内容
        document.add(new Paragraph(model.getTsdwmc()).setFont(fontKai).setFontSize(21.5F).setTextAlignment(TextAlignment.CENTER));
        document.add(new Paragraph("执行通知书（回执）").setFont(fontKai).setFontSize(21.5F).setTextAlignment(TextAlignment.CENTER));
        document.add(new Paragraph(model.getZxtzshzwh()).setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.RIGHT));
        document.add(new Paragraph(model.getTsdwmc()+":").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setUnderline());
        document.add(new Paragraph("你院于"+DateUtil.format(model.getJdwssxrq(),"yyyy年M月d日")+"生效的"+ model.getZxtzshzwh()+"执行通知书和所附执行依据均已收到。").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setFirstLineIndent(30));
        document.add(new Paragraph("我局已于"+DateUtil.format(model.getRjrq(),"yyyy年M月d日")+"对罪犯"+ model.getXm()+"予以考察。").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setTextAlignment(TextAlignment.LEFT).setFirstLineIndent(30));
        document.add(new Paragraph(DateUtil.format(DateUtil.date(),"yyyy年M月d日")).setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.RIGHT).setFixedPosition(60,400,500));
        document.add(new Paragraph("（公章）").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.RIGHT).setFixedPosition(60,350,500));
        document.add(new Paragraph("此联由司法行政机关填写并加盖公章后退回法院入卷").setFont(fontFs).setFontSize(15.5F).setTextAlignment(TextAlignment.LEFT).setFixedPosition(40,200,500));
        document.close();

        return os.toByteArray();
    }

    @Override
    public void getSignedPdf(AcceptCorrectionObjectParam param) {
        HttpServletResponse response = HttpServletUtil.getResponse();
        String sealNo = null;
        AcceptCorrectionObject model = this.getById(param.getId());
        model.setZxtzshzwh(param.getZxtzshzwh());
        model.setJdwssxrq(DateUtil.parse(param.getJdwssxrq(),"yyyy-MM-dd HH:mm:ss"));
        model.setRjrq(DateUtil.parse(param.getRjrq(),"yyyy-MM-dd HH:mm:ss"));
        SignatureMaintenance signatureMaintenance = signatureMaintenanceService.lambdaQuery()
                .eq(SignatureMaintenance::getJzjg, model.getJsdwId())
                .eq(SignatureMaintenance::getSealType, param.getSealType())
                .eq(SignatureMaintenance::getEnabled, 0)
                .one();
        if (ObjectUtil.isNotEmpty(signatureMaintenance)) {
            sealNo = signatureMaintenance.getSealNo();
        } else {
            try {
                ResponseUtil.responseExceptionError(response, 500, "未维护社区矫正机构章编码，请联系技术人员或手动上传", null);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return;
        }

        try {

            String base64 = Base64.getEncoder().encodeToString(getNoSignPdfBytes(model));
            String signedBase64 = electronicSignatureUtil.signedBase64(base64, sealNo, model.getXm() + "回执文书.pdf", "1", "510", "370");

            MultipartFile multipartFile = PdfFillUtil.convertBase64ToMultipartFile(signedBase64, model.getXm() + "回执文书.pdf");

            SysFileInfo sysFileInfo = sysFileInfoService.uploadFileOss(multipartFile, null, null);
            model.setHzcl(String.valueOf(sysFileInfo.getId()));
            this.updateById(model);

            byte[] fileBytes = multipartFile.getBytes();
            //设置contentType
            response.setContentType("application/pdf");
            //获取outputStream
            ServletOutputStream outputStream = response.getOutputStream();
            //输出
            IoUtil.write(outputStream, true, fileBytes);
            outputStream.close();

        }catch (Exception e){
            throw new RuntimeException("获取签章pdf异常，" + e.getMessage());
        }
    }


}
