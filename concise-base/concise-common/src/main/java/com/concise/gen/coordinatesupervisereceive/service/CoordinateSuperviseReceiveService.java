package com.concise.gen.coordinatesupervisereceive.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinatesupervisereceive.entity.CoordinateSuperviseReceive;
import com.concise.gen.coordinatesupervisereceive.param.CoordinateSuperviseReceiveParam;
import java.util.List;

/**
 * 接收外出监管协同service接口
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:03
 */
public interface CoordinateSuperviseReceiveService extends IService<CoordinateSuperviseReceive> {

    /**
     * 查询接收外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
    PageResult<CoordinateSuperviseReceive> page(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam);

    /**
     * 接收外出监管协同列表
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
    List<CoordinateSuperviseReceive> list(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam);

    /**
     * 添加接收外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
    void add(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam);

    /**
     * 删除接收外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
    void delete(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam);

    /**
     * 编辑接收外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
    void edit(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam);

    /**
     * 查看接收外出监管协同
     *
     * <AUTHOR>
     * @date 2022-07-05 15:46:03
     */
     CoordinateSuperviseReceive detail(CoordinateSuperviseReceiveParam coordinateSuperviseReceiveParam);
}
