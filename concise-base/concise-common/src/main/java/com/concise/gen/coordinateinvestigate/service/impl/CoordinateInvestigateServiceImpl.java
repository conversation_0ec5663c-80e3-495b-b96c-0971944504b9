package com.concise.gen.coordinateinvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigate;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateMapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateParam;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 调查评估协查service接口实现类
 *
 * <AUTHOR>
 * @date 2022-06-14 10:23:31
 */
@Service
public class CoordinateInvestigateServiceImpl extends ServiceImpl<CoordinateInvestigateMapper, CoordinateInvestigate> implements CoordinateInvestigateService {

    @Resource
    private SysFileInfoService sysFileInfoService;
    @Override
    public PageResult<CoordinateInvestigate> page(CoordinateInvestigateParam param) {
        QueryWrapper<CoordinateInvestigate> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据矫正单位
            if (param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(CoordinateInvestigate::getWtdwId, param.getOrgs());
            }

            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().eq(CoordinateInvestigate::getZt, param.getZt());
            }

            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(CoordinateInvestigate::getXm, param.getXm());
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper.lambda().ge(CoordinateInvestigate::getSdwtsj, param.getSearchBeginTime());
                queryWrapper.lambda().le(CoordinateInvestigate::getSdwtsj, param.getSearchEndTime());
            }
        }
        queryWrapper.lambda().orderByDesc(CoordinateInvestigate::getSdwtsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigate> list(CoordinateInvestigateParam coordinateInvestigateParam) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateParam coordinateInvestigateParam) {
        CoordinateInvestigate coordinateInvestigate = new CoordinateInvestigate();
        BeanUtil.copyProperties(coordinateInvestigateParam, coordinateInvestigate);
        this.save(coordinateInvestigate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateParam coordinateInvestigateParam) {
        this.removeById(coordinateInvestigateParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateParam coordinateInvestigateParam) {
        CoordinateInvestigate coordinateInvestigate = this.queryCoordinateInvestigate(coordinateInvestigateParam);
        BeanUtil.copyProperties(coordinateInvestigateParam, coordinateInvestigate);
        this.updateById(coordinateInvestigate);
    }

    @Override
    public CoordinateInvestigate detail(CoordinateInvestigateParam coordinateInvestigateParam) {
        CoordinateInvestigate detail = this.queryCoordinateInvestigate(coordinateInvestigateParam);

        detail.setWtdcsList(sysFileInfoService.getDetailByIds(detail.getWtdcs()));
        detail.setWtwsfjList(sysFileInfoService.getDetailByIds(detail.getWtwsfj()));
        return detail;
    }

    /**
     * 获取调查评估协查
     *
     * <AUTHOR>
     * @date 2022-06-14 10:23:31
     */
    private CoordinateInvestigate queryCoordinateInvestigate(CoordinateInvestigateParam coordinateInvestigateParam) {
        CoordinateInvestigate coordinateInvestigate = this.getById(coordinateInvestigateParam.getId());
        if (ObjectUtil.isNull(coordinateInvestigate)) {
            throw new ServiceException(CoordinateInvestigateExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigate;
    }
}
