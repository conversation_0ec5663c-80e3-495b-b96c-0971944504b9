package com.concise.gen.coordinateinvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP3;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateP3ExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateP3Mapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP3Param;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP3RecordService;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP3Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 调查评估协查_调查service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:53
 */
@Service
public class CoordinateInvestigateP3ServiceImpl extends ServiceImpl<CoordinateInvestigateP3Mapper, CoordinateInvestigateP3> implements CoordinateInvestigateP3Service {

    @Resource
    private CoordinateInvestigateP3RecordService p3RecordService;
    @Override
    public PageResult<CoordinateInvestigateP3> page(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        QueryWrapper<CoordinateInvestigateP3> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(coordinateInvestigateP3Param)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3Param.getPid())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3::getPid, coordinateInvestigateP3Param.getPid());
            }

        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateP3> list(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        CoordinateInvestigateP3 coordinateInvestigateP3 = new CoordinateInvestigateP3();
        BeanUtil.copyProperties(coordinateInvestigateP3Param, coordinateInvestigateP3);
        this.save(coordinateInvestigateP3);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        this.removeById(coordinateInvestigateP3Param.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        CoordinateInvestigateP3 coordinateInvestigateP3 = this.queryCoordinateInvestigateP3(coordinateInvestigateP3Param);
        BeanUtil.copyProperties(coordinateInvestigateP3Param, coordinateInvestigateP3);

        if (coordinateInvestigateP3Param.getRecords() !=null) {
            coordinateInvestigateP3Param.getRecords().forEach(record -> {
                record.setStepId(coordinateInvestigateP3Param.getId());
                record.setPid(coordinateInvestigateP3Param.getPid());
                p3RecordService.add(record);
            });
        }
        this.updateById(coordinateInvestigateP3);
    }

    @Override
    public CoordinateInvestigateP3 detail(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        return this.queryCoordinateInvestigateP3(coordinateInvestigateP3Param);
    }

    /**
     * 获取调查评估协查_调查
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
    private CoordinateInvestigateP3 queryCoordinateInvestigateP3(CoordinateInvestigateP3Param coordinateInvestigateP3Param) {
        CoordinateInvestigateP3 coordinateInvestigateP3 = this.getById(coordinateInvestigateP3Param.getId());
        if (ObjectUtil.isNull(coordinateInvestigateP3)) {
            throw new ServiceException(CoordinateInvestigateP3ExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateP3;
    }
}
