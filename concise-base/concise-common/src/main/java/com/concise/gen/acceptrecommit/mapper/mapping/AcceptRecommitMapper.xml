<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.acceptrecommit.mapper.AcceptRecommitMapper">

    <insert id="sendLog">
        replace INTO send_base_info_log (id, psn_id, xm, jzjg, jzjg_name, jsdw, zhaungtai, fasongshijian)
        VALUES (concat(#{param1},date_format (current_time,'%Y-%m-%d')),#{param1},#{param2},#{param3},#{param4},#{param5},#{param5},current_time)
    </insert>
    <delete id="deleteLog">
        update send_base_info_log set deleted = 1 where psn_id = #{psnId}
    </delete>
    <select id="logPage" resultType="java.util.Map">
        select * from send_base_info_log
        <where>
            <if test="fsrq != null and fsrq!= ''"> and date_format (fasongshijian,'%Y-%m-%d') = date_format (#{fsrq},'%Y-%m-%d') </if>
            <if test="xm != null and xm!= ''"> and xm like concat('%',#{xm},'%')</if>
            <if test="jzjgSet != null"> and jzjg in
                <foreach collection="jzjgSet" index="index" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
            <if test="id != null and id!= ''"> and psn_id = #{id} </if>
            <if test="deleted != null"> and deleted = #{deleted} </if>
        </where>
        order by fasongshijian desc
    </select>
    <select id="getMaxNumberByYear" resultType="java.lang.String">
        SELECT wsh
        FROM accept_recommit
        WHERE WSH LIKE concat('%',#{pattern},'%')
        ORDER BY CAST( SUBSTRING( WSH, LOCATE( '[', WSH ) + 6, LOCATE( '号', WSH ) - LOCATE( '[', WSH ) - 6 ) AS UNSIGNED ) DESC
            LIMIT 1;

    </select>
    <select id="logExport" resultType="com.concise.gen.acceptrecommit.param.SendLogExport">
        select * from send_base_info_log
        <where>
            <if test="xm != null and xm!= ''"> and xm like concat('%',#{xm},'%')</if>
            <if test="deleted != null"> and deleted = #{deleted} </if>
            <if test="jzjgSet != null"> and jzjg in
                <foreach collection="jzjgSet" index="index" item="i" open="(" separator="," close=")">
                    #{i}
                </foreach>
            </if>
        </where>
        order by fasongshijian desc
    </select>
</mapper>
