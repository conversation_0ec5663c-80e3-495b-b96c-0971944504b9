package com.concise.gen.acceptcorrectionaccomplice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 矫正对象同案犯信息信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:23
 */
@Data
@TableName("accept_correction_accomplice")
public class AcceptCorrectionAccomplice{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String contactId;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 出生日期
     */
    private Date csrq;

    /**
     * 罪名
     */
    private String zm;
    /**
     * 刑种
     */
    private String xz;
    /**
     * 刑期
     */
    private String xq;

    /**
     * 被判处刑罚及所在监所
     */
    private String bpcxfjszjs;

}
