package com.concise.gen.acceptdischargedinfo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcorrectionaccomplice.service.AcceptCorrectionAccompliceService;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptcorrectionfamily.service.AcceptCorrectionFamilyService;
import com.concise.gen.acceptcriminalrecord.service.AcceptCriminalRecordService;
import com.concise.gen.acceptdischargedinfo.entity.AcceptDischargedInfo;
import com.concise.gen.acceptdischargedinfo.enums.AcceptDischargedInfoExceptionEnum;
import com.concise.gen.acceptdischargedinfo.mapper.AcceptDischargedInfoMapper;
import com.concise.gen.acceptdischargedinfo.param.AcceptDischargedInfoParam;
import com.concise.gen.acceptdischargedinfo.service.AcceptDischargedInfoService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 释放信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
@Service
public class AcceptDischargedInfoServiceImpl extends ServiceImpl<AcceptDischargedInfoMapper, AcceptDischargedInfo> implements AcceptDischargedInfoService {
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private OrgCommonService orgCommonService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private AcceptCorrectionAccompliceService acceptCorrectionAccompliceService;
    @Resource
    private AcceptCorrectionFamilyService acceptCorrectionFamilyService;
    @Resource
    private AcceptCriminalRecordService acceptCriminalRecordService;
    @Override
    public PageResult<AcceptDischargedInfo> page(AcceptDischargedInfoParam acceptDischargedInfoParam) {
        QueryWrapper<AcceptDischargedInfo> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptDischargedInfoParam)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getTaskId())) {
                queryWrapper.lambda().eq(AcceptDischargedInfo::getTaskId, acceptDischargedInfoParam.getTaskId());
            }
            // 根据数据状态 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getZt())) {
                queryWrapper.lambda().eq(AcceptDischargedInfo::getZt, acceptDischargedInfoParam.getZt());
            }
            // 根据数据来源(机构类型) 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getSjlylx())) {
                queryWrapper.lambda().eq(AcceptDischargedInfo::getSjlylx, acceptDischargedInfoParam.getSjlylx());
            }
            // 根据接收单位 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getJsdw())) {
                queryWrapper.lambda().and(i -> i.eq(AcceptDischargedInfo::getJsdwId, acceptDischargedInfoParam.getJsdw()).or().like(AcceptDischargedInfo::getJsdwPids, acceptDischargedInfoParam.getJsdw()));
            }
            // 根据推送单位 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getTsdw())) {
                queryWrapper.lambda().eq(AcceptDischargedInfo::getTsdw, acceptDischargedInfoParam.getTsdw());
            }
            // 根据统一赋号 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getTyfh())) {
                queryWrapper.lambda().eq(AcceptDischargedInfo::getTyfh, acceptDischargedInfoParam.getTyfh());
            }
            // 根据姓名 查询
            if (ObjectUtil.isNotEmpty(acceptDischargedInfoParam.getXm())) {
                queryWrapper.lambda().like(AcceptDischargedInfo::getXm, acceptDischargedInfoParam.getXm());
            }
            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(acceptDischargedInfoParam.getSearchBeginTime(), acceptDischargedInfoParam.getSearchEndTime())) {
                queryWrapper.lambda().ge(AcceptDischargedInfo::getSdsj, acceptDischargedInfoParam.getSearchBeginTime());
                queryWrapper.lambda().le(AcceptDischargedInfo::getSdsj, acceptDischargedInfoParam.getSearchEndTime());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptDischargedInfo> list(AcceptDischargedInfoParam acceptDischargedInfoParam) {
        return this.list();
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptDischargedInfoParam param) {
        AcceptDischargedInfo acceptDischargedInfo = new AcceptDischargedInfo();
        OrgCommon org = orgCommonService.getOrgByCode(param.getJsdw());
        param.setJsdwmc(org.getName());
        param.setJsdwId(org.getId());
        param.setJsdwPids(org.getPids());
        param.setTsdwmc(extOrgInfoService.getExtOrgName(param.getSjlylx(),param.getTsdw()));
        param.getAccompliceList().forEach(acceptCorrectionAccompliceParam -> acceptCorrectionAccompliceService.add(acceptCorrectionAccompliceParam));
        param.getFamilyList().forEach(family -> acceptCorrectionFamilyService.add(family));
        param.getCriminalRecordList().forEach(criminalRecord -> acceptCriminalRecordService.add(criminalRecord));
        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));
        BeanUtil.copyProperties(param, acceptDischargedInfo);
        this.save(acceptDischargedInfo);
        return param.getDocList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptDischargedInfoParam acceptDischargedInfoParam) {
        this.removeById(acceptDischargedInfoParam.getId());
    }

    @Override
    public void delete(String taskId) {

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptDischargedInfoParam acceptDischargedInfoParam) {
        AcceptDischargedInfo acceptDischargedInfo = this.queryAcceptDischargedInfo(acceptDischargedInfoParam);
        BeanUtil.copyProperties(acceptDischargedInfoParam, acceptDischargedInfo);
        this.updateById(acceptDischargedInfo);
    }

    @Override
    public AcceptDischargedInfo detail(AcceptDischargedInfoParam acceptDischargedInfoParam) {
        return this.queryAcceptDischargedInfo(acceptDischargedInfoParam);
    }

    /**
     * 获取释放信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:42
     */
    private AcceptDischargedInfo queryAcceptDischargedInfo(AcceptDischargedInfoParam acceptDischargedInfoParam) {
        AcceptDischargedInfo acceptDischargedInfo = this.getById(acceptDischargedInfoParam.getId());
        if (ObjectUtil.isNull(acceptDischargedInfo)) {
            throw new ServiceException(AcceptDischargedInfoExceptionEnum.NOT_EXIST);
        }
        return acceptDischargedInfo;
    }
}
