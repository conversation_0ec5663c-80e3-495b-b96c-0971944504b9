package com.concise.gen.ciInvestigate.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.param.SysFileInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 迁入调查评估_调查_记录
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:18
 */
@Data
@TableName("correction_immigration_investigate_p3_record")
public class InvestigateP3Record{
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 调查评估id
     */
    private String pid;

    /**
     * 调查步骤id
     */
    private String stepId;

    /**
     * 被调查人姓名
     */
    private String xm;

    /**
     * 与被告人（罪犯）关系
     */
    private String gx;

    /**
     * 调查时间
     */
    @Excel(name = "调查时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date dcsj;

    /**
     * 调查地点
     */
    private String dcdd;

    /**
     * 调查事项
     */
    private String dcsx;

    /**
     * 调查笔录
     */
    private String dcbl;
    @TableField(exist = false)
    private List<SysFileInfoVO> dcblFileList;

    /**
     * 调查人(jsonarray)
     */
    private String dcr;

}
