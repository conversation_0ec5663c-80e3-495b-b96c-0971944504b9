package com.concise.gen.investigationotheruser.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.investigationotheruser.entity.InvestigationOtherUser;
import com.concise.gen.investigationotheruser.param.InvestigationOtherUserParam;
import com.concise.gen.investigationsign.param.InvestigationSignParam;

import java.util.List;

/**
 * 调查评估-其他用户service接口
 *
 * <AUTHOR>
 * @date 2025-08-01 11:02:13
 */
public interface InvestigationOtherUserService extends IService<InvestigationOtherUser> {

    /**
     * 查询调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    PageResult<InvestigationOtherUser> page(InvestigationOtherUserParam investigationOtherUserParam);

    /**
     * 调查评估-其他用户列表
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    List<InvestigationOtherUser> list(InvestigationOtherUserParam investigationOtherUserParam);

    /**
     * 添加调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    void add(InvestigationOtherUserParam investigationOtherUserParam);

    /**
     * 删除调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    void delete(InvestigationOtherUserParam investigationOtherUserParam);

    /**
     * 编辑调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    void edit(InvestigationOtherUserParam investigationOtherUserParam);

    /**
     * 查看调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    InvestigationOtherUser detail(InvestigationOtherUserParam investigationOtherUserParam);

    /**
     * 添加调查评估-其他用户
     *
     * <AUTHOR>
     * @date 2025-08-01 11:02:13
     */
    void addOtherUser(List<InvestigationSignParam> zlbUserList);
}
