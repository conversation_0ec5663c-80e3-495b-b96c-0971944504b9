package com.concise.gen.acceptinvestinfo.param;

import com.concise.common.pojo.base.param.BaseParam;
import com.concise.gen.acceptchargesinfo.entity.AcceptChargesInfo;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 调查评估信息接收表参数类
 *
 * <AUTHOR>
 * @date 2022-07-22 13:34:07
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class AcceptInvestInfoParam extends BaseParam {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**回执材料*/
    private String hzcl;
    private String xtbh;

    /**
     * 数据来源
     * 1：省内
     * 2：省外
     * */
    private String dataFrom;
    /**是否速裁  1:是,0:否*/
    private String sfsc;
    /**流程终止原因*/
    private String terminateReason;

    /**签章类型（1-矫正机构章、2-司法局章、3-个人签名章、4-司法所章）*/
    private String sealType;
    private String wsDckssj;
    private String wsDcjssj;
    private String wsWtdw;
    private String wsRylx;
    private String wsDcygqk;

    /**推送时间*/
    private String tssj;

    /**
     * 审核时间
     */
    private String shsj;

    /**
     * 审核人员
     */
    private String shry;

    /**
     * 审核矫正机构
     */
    private String shjzjg;

    /**
     * 审核结果
     */
    private String shjg;
    /** 审核结果 */
    private String shjgName;
    /** 是否自动退回 */
    private String returned;
    /**
     * 审核备注
     */
    private String shbz;




    /**
     *
     */

    private String taskId;

    /**
     * 调查对象罪名信息
     */
    private List<String> caseReasonList;
    private List<AcceptChargesInfo> chargesList;
    private List<AcceptCorrectionDocParam> docList;

    /**
     * 数据状态
     */

    private String zt;

    /**
     * 数据来源(机构类型)
     */

    private String sjlylx;

    /**
     * 接收单位
     */

    private String jsdw;

    /**
     * 接收单位名称
     */

    private String jsdwmc;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 推送单位
     */

    private String tsdw;

    /**
     * 推送单位名称
     */

    private String tsdwmc;

    /**
     * 送达时间
     */
    private String sdsj;

    /**
     * 反馈人
     */
    private String fkr;
    /**
     * 反馈时间
     */
    private String fksj;
    /**
     * 抄送单位代码
     */
    private String csdwdm;

    /**
     * 区县司法局审核结果
     */

    private String qxsfjshjg;

    /**
     * 备注
     */

    private String jsbz;

    /**
     * 拒接收原因
     */

    private String jjsyy;

    /**
     * 指派人
     */

    private String zprbz;

    /**
     * 指派时间
     */
    private String zpsj;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 案件编号(监狱案件编号JYAJBH/法院案件标识FYAJBS/检察院部门受案号JCYBMSAH)
     */
    private String ajbh;

    /**
     * 案件名称
     */

    private String ajmc;

    /**法院案号*/
    private String fyah;

    /**
     * 罪犯编号
     * XTBH31007时为/检察院统一受案号JCYTYSAH
     */

    private String zfbh;

    /**
     * 公安嫌疑人编号
     */

    private String gaxyrbh;

    /**
     * 是否为未成年人
     */

    private String sfwcn;

    /**
     * 姓名
     */

    private String bgrxm;

    /**
     * 性别
     */

    private String bgrxb;

    /**
     * 出生日期
     */
    private String bgrcsrq;

    /**
     * 证件类型
     * 一体化字典
     */
    private String zjlx;

    /**
     * 证件号码
     */

    private String zjhm;

    /**
     * 联系电话
     */

    private String lxdh;

    /**
     * 住所地
     */

    private String zsd;

    /**
     * 住所地详细地址
     */

    private String zsdxxdz;

    /**
     * 户籍所在地
     */

    private String hjszd;

    /**
     * 户籍地址明细
     */

    private String hjdzmx;

    /**
     * 工作单位
     */

    private String gzdw;

    /**
     * 主要犯罪事实
     */
    private String zyfzss;

    /**
     * 拟适用社区矫正人员类型
     */

    private String nsysqjzrylx;

    /**
     * 拟适用矫正类别
     */

    private String nsyjzlb;

    /**是否有原判刑期
     * 0:无;1:有
     * */
    private String sfyypxq;
    /**
     * 原判刑期
     */

    private String ypxq;

    /**
     * 原判刑期开始日期
     */
    private String ypxqksrq;

    /**
     * 原判刑期结束日期
     */
    private String ypxqjsrq;

    /**
     * 原判刑罚
     */

    private String ypxf;

    /**
     * 附加刑
     */

    private String fjx;
    /** 罚金(附加刑)**/
    private String fjxFj;
    /**剥夺政治权利(附加刑)**/
    private String fjxBdzzql;
    /**没收财产(附加刑)**/
    private String fjxMscc;

    /**
     * 判决机关
     */

    private String pjjg;

    /**
     * 判决日期
     */

    private String pjrq;

    /**
     * 调查对象家属监护人或保证人姓名
     */

    private String dcdxjhrxm;

    /**
     * 调查对象家属监护人或保证人住址
     */

    private String dcdxjhrzz;

    /**
     * 调查对象家属监护人或保证人联系方式
     */

    private String dcdxjhrlxfs;

    /**
     * 委托编号
     */

    private String wtbh;

    /**
     * 调查评估标识
     */

    private String dcpgbs;

    /**
     * 委托单位
     */

    private String wtdw;

    /**
     * 委托单位地址
     */

    private String wtdwdd;

    /**
     * 委托时间
     */
    private String wtsj;

    /**
     * 委托方联系人
     */

    private String wtflxr;

    /**
     * 委托方联系电话
     */

    private String wtflxdh;

    /**
     * 调查单位
     */

    private String qxdcdw;

    /**
     * 调查单位
     */

    private String dcdw;

    /**
     * 调查单位地址区划
     */

    private String dcdwdzqh;

    /**
     * 调查单位地址明细
     */

    private String dcdwdzmx;

    /**
     * 调查单位联系人
     */

    private String dcdwlxr;

    /**
     * 调查单位联系电话
     */

    private String dcdwlxdh;

    /**
     * 调查评估文书号
     */

    private String dcpgwsh;

    /**
     * 调查评估结论
     */

    private String dcpgjl;

    /**
     * 调查评估日期
     */
    private String dcpgrq;

    /**
     * 实际居住地区划
     */

    private String sjjzdqh;

    /**
     * 实际居住地明细
     */

    private String sjjzdmx;

    /**
     * 备注
     */

    private String bz;

    /**
     * 监护人姓名
     */

    private String jhrxm;

    /**
     * 监护人与罪犯关系
     */

    private String jhryzfgx;

    /**
     * 监护人住址区划
     */

    private String jhrzzqh;

    /**
     * 监护人住址明细
     */

    private String jhrzzmx;

    /**
     * 监护人联系电话
     */

    private String jhrlxdh;

    /**
     * 监护人家庭经济情况
     */

    private String jhrjjqk;

    /**被调查人姓名*/
    private String bdrxm;
    /**被调查人与被告人（罪犯）关系*/
    private String bdcrybgrgx;
    /**调查事项*/
    private String dcsx;
    /**调查时间*/
    private String dcsj;
    /**调查地点*/
    private String dcdd;
}
