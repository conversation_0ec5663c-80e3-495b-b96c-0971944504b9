package com.concise.gen.accepttemporarilyoutsideprison.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.base.enums.OrgTypeEnum;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptbaseinfo.entity.AcceptBaseInfo;
import com.concise.gen.acceptbaseinfo.param.AcceptBaseInfoParam;
import com.concise.gen.acceptbaseinfo.service.AcceptBaseInfoService;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.acceptreturninfo.service.AcceptReturnInfoService;
import com.concise.gen.accepttemporarilyoutsideprison.entity.AcceptTemporarilyOutsidePrison;
import com.concise.gen.accepttemporarilyoutsideprison.enums.AcceptTemporarilyOutsidePrisonExceptionEnum;
import com.concise.gen.accepttemporarilyoutsideprison.mapper.AcceptTemporarilyOutsidePrisonMapper;
import com.concise.gen.accepttemporarilyoutsideprison.param.AcceptTemporarilyOutsidePrisonParam;
import com.concise.gen.accepttemporarilyoutsideprison.service.AcceptTemporarilyOutsidePrisonService;
import com.concise.gen.extorginfo.service.ExtOrgInfoService;
import com.concise.gen.notice.enums.YwxtNoticeTypeEnum;
import com.concise.gen.notice.service.YwxtNoticeService;
import com.concise.gen.sysorg.entity.OrgCommon;
import com.concise.gen.sysorg.service.OrgCommonService;
import com.concise.gen.webservice.service.SendTemporarilyOutsidePrisonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.concise.common.consts.SymbolConstant.COMMA;

/**
 * 暂予监外接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-12-01 10:33:30
 */
@Service
public class AcceptTemporarilyOutsidePrisonServiceImpl extends ServiceImpl<AcceptTemporarilyOutsidePrisonMapper, AcceptTemporarilyOutsidePrison> implements AcceptTemporarilyOutsidePrisonService {

    @Resource
    private SendTemporarilyOutsidePrisonService sendService;
    @Resource
    private AcceptReturnInfoService acceptReturnInfoService;
    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private OrgCommonService orgCommonService;
    @Resource
    private ExtOrgInfoService extOrgInfoService;
    @Resource
    private AcceptBaseInfoService acceptBaseInfoService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private YwxtNoticeService ywxtNoticeService;
    @Override
    public PageResult<AcceptTemporarilyOutsidePrison> page(AcceptTemporarilyOutsidePrisonParam param) {
        QueryWrapper<AcceptTemporarilyOutsidePrison> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(param)) {

            // 根据 查询
            if (ObjectUtil.isNotEmpty(param.getTaskId())) {
                queryWrapper.lambda().eq(AcceptTemporarilyOutsidePrison::getTaskId, param.getTaskId());
            }
            if (ObjectUtil.isNotEmpty(param.getXm())) {
                queryWrapper.lambda().like(AcceptTemporarilyOutsidePrison::getXm, param.getXm());
            }
            if (ObjectUtil.isNotEmpty(param.getZt())) {
                queryWrapper.lambda().like(AcceptTemporarilyOutsidePrison::getZt, param.getZt());
            }
            if (ObjectUtil.isNotEmpty(param.getOrgs()) && param.getOrgs().size() < 1000) {
                queryWrapper.lambda().in(AcceptTemporarilyOutsidePrison::getSqjzjg, param.getOrgs());
            }else if ("xb".equals(param.getSearchValue())) {
                queryWrapper.lambda().isNotNull(AcceptTemporarilyOutsidePrison::getSqjzjg);
                queryWrapper.lambda().ne(AcceptTemporarilyOutsidePrison::getSqjzjg,"");
            }
        }
        queryWrapper.lambda().orderByDesc(AcceptTemporarilyOutsidePrison::getSdsj);
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCorrectionDocParam> add(AcceptTemporarilyOutsidePrisonParam param) {

        AcceptTemporarilyOutsidePrison one = this.lambdaQuery()
                .eq(AcceptTemporarilyOutsidePrison::getJsdw, param.getJsdw())
                .eq(AcceptTemporarilyOutsidePrison::getZjhm, param.getZjhm())
                .orderByDesc(AcceptTemporarilyOutsidePrison::getSdsj)
                .last("limit 1")
                .one();
        if (one != null && one.getSdsj() != null && !"2".equals(one.getZt())) {
            List<AcceptCorrectionDoc> oldDocList = acceptCorrectionDocService.list(one.getId());
            Boolean check = acceptReturnInfoService.check(one.getSdsj(), param.getDocList(), oldDocList);
            if (check) {
                if ("0".equals(one.getZt())){
                    //旧数据未接收时退回旧数据
                    acceptReturnInfoService.save(param.getXtbh(), param.getId(), param.getTyfh(), DateUtil.parse(param.getTssj()), one.getSdsj(), one.getXm(), one.getJsdw(), one.getJsdwId(), one.getJsdwPids(), one.getJsdwmc());
                    one.setZt("2");
                    one.setShbz("系统识别为重复案件，自动退回");
                    one.setShjg("26");
                    one.setShjgName("其他");
                    one.setShry("数据质检");
                    one.setShsj(DateUtil.date());
                    this.updateById(one);
                    acceptBaseInfoService.lambdaUpdate()
                            .set(AcceptBaseInfo::getZt, "2")
                            .set(AcceptBaseInfo::getReturned, "1")
                            .eq(AcceptBaseInfo::getId, one.getId()).update();
                    sendService.sendMessage(one,"数据质检");
                }else {
                    param.setZt("2");
                    param.setReturned("1");
                    param.setShbz("系统识别为重复案件，自动退回");
                    param.setShjg("26");
                    param.setShjgName("其他");
                    param.setShry("数据质检");
                    param.setShsj(DateUtil.now());
                }
            }
        }
        AcceptTemporarilyOutsidePrison acceptTemporarilyOutsidePrison = new AcceptTemporarilyOutsidePrison();
        BeanUtil.copyProperties(param, acceptTemporarilyOutsidePrison);

        param.getDocList().forEach(doc -> acceptCorrectionDocService.add(doc));

        this.save(acceptTemporarilyOutsidePrison);
        if ("2".equals(param.getZt())) {
            // 自动退回的消息
            sendService.sendMessage(acceptTemporarilyOutsidePrison,"数据质检");
        }
        AcceptBaseInfo baseInfo = new AcceptBaseInfo();
        BeanUtil.copyProperties(param, baseInfo);
        baseInfo.setXtlx("04");
        baseInfo.setXtlxmc("决定暂外");
        baseInfo.setSjlylxmc(OrgTypeEnum.getEnumByCode(baseInfo.getSjlylx()).getName());
        acceptBaseInfoService.save(baseInfo);

        ywxtNoticeService.buildNoticeByOrgId(
                YwxtNoticeTypeEnum.ADD_CORRECTION_01,
                param.getJsdwmc()+","+DateUtil.formatDate(acceptTemporarilyOutsidePrison.getSdsj())+","+param.getTsdwmc()+","+param.getXm(),
                param.getTsdwmc()+","+param.getXm(),
                param.getJsdwId());
        return param.getDocList();
    }


    @Override
    public void delete(String taskId) {
        List<AcceptTemporarilyOutsidePrison> list = this.lambdaQuery().eq(AcceptTemporarilyOutsidePrison::getTaskId, taskId).list();
        for (AcceptTemporarilyOutsidePrison acceptTemporarilyOutsidePrison : list) {
            List<AcceptCorrectionDoc> docList = acceptCorrectionDocService.lambdaQuery().eq(AcceptCorrectionDoc::getContactId, acceptTemporarilyOutsidePrison.getId()).list();
            for (AcceptCorrectionDoc doc : docList) {
                sysFileInfoService.lambdaUpdate().eq(SysFileInfo::getBizId,doc.getId()).remove();
                acceptCorrectionDocService.removeById(doc.getId());
            }
            if (ObjectUtil.isNotEmpty(acceptTemporarilyOutsidePrison.getHzcl())) {
                for (String fileId : acceptTemporarilyOutsidePrison.getHzcl().split(COMMA)) {
                    sysFileInfoService.removeById(fileId);
                }
            }
            this.removeById(acceptTemporarilyOutsidePrison.getId());
        }
    }

    @Override
    public void edit(AcceptBaseInfoParam param) {
        AcceptTemporarilyOutsidePrisonParam top = param.getTop();
        if (!"4".equals(top.getZt())) {
            if (top.getShjg().startsWith("1")) {
                top.setZt("1");
            }else {
                top.setZt("2");
            }
        }
        AcceptTemporarilyOutsidePrison byId = this.getById(param.getId());
        BeanUtil.copyProperties(top, byId);
        byId.setId(param.getId());
        OrgCommon org = orgCommonService.getById(byId.getJzjg());
        if (org != null) {
            byId.setJzjgName(org.getName());
        }
        this.updateById(byId);

        sendService.sendMessage(this.getById(param.getId()),top.getShry());

        AcceptBaseInfo baseInfo = new AcceptBaseInfo();
        baseInfo.setId(param.getId());
        baseInfo.setZt(top.getZt());
        acceptBaseInfoService.updateById(baseInfo);

        if ("all".equals(param.getSendWd())) {
            if (!sendService.sendToWd(byId)) {
                throw new ServiceException(AcceptTemporarilyOutsidePrisonExceptionEnum.SYNC_ERROR);
            }
            acceptCorrectionDocService.sendDocToWd(byId.getId());
        }else if(ObjectUtil.isNotEmpty(param.getSendWd())){
            acceptCorrectionDocService.sendDocToWd(param.getId(),param.getSendWd());
        }
    }

    @Override
    public AcceptTemporarilyOutsidePrison detail(String id) {
        AcceptTemporarilyOutsidePrison detail = this.getById(id);
        if (detail != null) {
            detail.setJzjg(orgCommonService.getOrgByCode(detail.getJsdw()).getId());
            detail.setZyjwzxjdjg(extOrgInfoService.getExtOrgName(detail.getSjlylx(),detail.getZyjwzxjdjg()));
            detail.setYjzfjglx("监管场所");
            detail.setYjzfjgmc(extOrgInfoService.getExtOrgName(OrgTypeEnum.JIAN_GUAN_CHANG_SUO.getCode(),detail.getYjzfjgmc()));
            detail.setHzclList(sysFileInfoService.getDetailByIds(detail.getHzcl()));
            detail.setDocList(acceptCorrectionDocService.list(detail.getId()));
            return detail;
        }
        return new AcceptTemporarilyOutsidePrison();
    }

    @Override
    public void feedBack(AcceptTemporarilyOutsidePrisonParam param) {
        param.setJzjg(null);
        param.setZyjwzxjdjg(null);
        param.setYjzfjglx(null);
        param.setYjzfjgmc(null);

        AcceptTemporarilyOutsidePrison data = this.getById(param.getId());

        BeanUtil.copyProperties(param, data);
        data.setJzjgName(orgCommonService.getById(data.getSqjzjg()).getName());
        this.updateById(data);
        AcceptBaseInfo baseInfo = new AcceptBaseInfo();
        BeanUtil.copyProperties(param, baseInfo);
        acceptBaseInfoService.updateById(baseInfo);

        SysFileInfo hzcl = sysFileInfoService.getById(data.getHzcl());
        data.setDocList(new ArrayList<AcceptCorrectionDoc>(){{
            AcceptCorrectionDoc doc = new AcceptCorrectionDoc();
            doc.setWs(hzcl.getFileOriginName());
            doc.setWsdm(data.getZxtzshzwh());
            doc.setUri(hzcl.getExtFilePath());
            add(doc);}});

        data.setSqjzjg(orgCommonService.getById(data.getSqjzjg()).getCode());
        if (OrgTypeEnum.FA_YUAN.getCode().equals(data.getSjlylx())) {
            sendService.xtbh4058_1234(data);
        }
        if (OrgTypeEnum.GONG_AN.getCode().equals(data.getSjlylx())) {
            sendService.xtbh4073_1(data);
        }
        if (OrgTypeEnum.JIAN_YU.getCode().equals(data.getSjlylx())) {
            sendService.xtbh4023(data);
        }
    }

}
