package com.concise.gen.coordinateinvestigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP3Record;
import com.concise.gen.coordinateinvestigate.enums.CoordinateInvestigateP3RecordExceptionEnum;
import com.concise.gen.coordinateinvestigate.mapper.CoordinateInvestigateP3RecordMapper;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP3RecordParam;
import com.concise.gen.coordinateinvestigate.service.CoordinateInvestigateP3RecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 调查评估协查_调查_记录service接口实现类
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:55
 */
@Service
public class CoordinateInvestigateP3RecordServiceImpl extends ServiceImpl<CoordinateInvestigateP3RecordMapper, CoordinateInvestigateP3Record> implements CoordinateInvestigateP3RecordService {

    @Override
    public PageResult<CoordinateInvestigateP3Record> page(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        QueryWrapper<CoordinateInvestigateP3Record> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(coordinateInvestigateP3RecordParam)) {

            // 根据调查评估id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getPid())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getPid, coordinateInvestigateP3RecordParam.getPid());
            }
            // 根据调查步骤id 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getStepId())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getStepId, coordinateInvestigateP3RecordParam.getStepId());
            }
            // 根据被调查人姓名 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getXm())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getXm, coordinateInvestigateP3RecordParam.getXm());
            }
            // 根据与被告人（罪犯）关系 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getGx())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getGx, coordinateInvestigateP3RecordParam.getGx());
            }
            // 根据调查时间 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getDcsj())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getDcsj, coordinateInvestigateP3RecordParam.getDcsj());
            }
            // 根据调查地点 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getDcdd())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getDcdd, coordinateInvestigateP3RecordParam.getDcdd());
            }
            // 根据调查事项 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getDcsx())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getDcsx, coordinateInvestigateP3RecordParam.getDcsx());
            }
            // 根据调查笔录 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getDcbl())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getDcbl, coordinateInvestigateP3RecordParam.getDcbl());
            }
            // 根据调查人(jsonarray) 查询
            if (ObjectUtil.isNotEmpty(coordinateInvestigateP3RecordParam.getDcr())) {
                queryWrapper.lambda().eq(CoordinateInvestigateP3Record::getDcr, coordinateInvestigateP3RecordParam.getDcr());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<CoordinateInvestigateP3Record> list(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        return this.list();
    }

    @Override
    public void add(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        CoordinateInvestigateP3Record coordinateInvestigateP3Record = new CoordinateInvestigateP3Record();
        BeanUtil.copyProperties(coordinateInvestigateP3RecordParam, coordinateInvestigateP3Record);
        this.save(coordinateInvestigateP3Record);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        this.removeById(coordinateInvestigateP3RecordParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        CoordinateInvestigateP3Record coordinateInvestigateP3Record = this.queryCoordinateInvestigateP3Record(coordinateInvestigateP3RecordParam);
        BeanUtil.copyProperties(coordinateInvestigateP3RecordParam, coordinateInvestigateP3Record);
        this.updateById(coordinateInvestigateP3Record);
    }

    @Override
    public CoordinateInvestigateP3Record detail(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        return this.queryCoordinateInvestigateP3Record(coordinateInvestigateP3RecordParam);
    }

    /**
     * 获取调查评估协查_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:55
     */
    private CoordinateInvestigateP3Record queryCoordinateInvestigateP3Record(CoordinateInvestigateP3RecordParam coordinateInvestigateP3RecordParam) {
        CoordinateInvestigateP3Record coordinateInvestigateP3Record = this.getById(coordinateInvestigateP3RecordParam.getId());
        if (ObjectUtil.isNull(coordinateInvestigateP3Record)) {
            throw new ServiceException(CoordinateInvestigateP3RecordExceptionEnum.NOT_EXIST);
        }
        return coordinateInvestigateP3Record;
    }
}
