package com.concise.gen.acceptbaseinfo.enums;

/**
 * 中国省市简称枚举
 */
public enum ChinaProvince {
    BEIJING("北京市", "京"),
    TIANJIN("天津市", "津"),
    HEBEI("河北省", "冀"),
    SHANXI("山西省", "晋"),
    NEIMENGGU("内蒙古自治区", "蒙"),
    LIAONING("辽宁省", "辽"),
    JILIN("吉林省", "吉"),
    HEILONGJIANG("黑龙江省", "黑"),
    SHANGHAI("上海市", "沪"),
    JIANGSU("江苏省", "苏"),
    ZHEJIANG("浙江省", "浙"),
    ANHUI("安徽省", "皖"),
    FUJIAN("福建省", "闽"),
    JIANGXI("江西省", "赣"),
    SHANDONG("山东省", "鲁"),
    HENAN("河南省", "豫"),
    HUBEI("湖北省", "鄂"),
    HUNAN("湖南省", "湘"),
    GUANGDONG("广东省", "粤"),
    GUANGXI("广西壮族自治区", "桂"),
    HAINAN("海南省", "琼"),
    CHONGQING("重庆市", "渝"),
    SICHUAN("四川省", "川"),
    GUIZHOU("贵州省", "黔"),
    YUNNAN("云南省", "滇"),
    XIZANG("西藏自治区", "藏"),
    SHAANXI("陕西省", "陕"),
    GANSU("甘肃省", "甘"),
    QINGHAI("青海省", "青"),
    NINGXIA("宁夏回族自治区", "宁"),
    XINJIANG("新疆维吾尔自治区", "新"),
    TAIWAN("台湾省", "台"),
    XIANGGANG("香港特别行政区", "港"),
    AOMEN("澳门特别行政区", "澳");

    /**
     *
     * 省份或市的全名
     */
    private final String fullName;
    /**
     * 简称
     */
    private final String abbreviation;

    ChinaProvince(String fullName, String abbreviation) {
        this.fullName = fullName;
        this.abbreviation = abbreviation;
    }

    public String getFullName() {
        return fullName;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    // 根据省份或市的名称查找简称
    public static String findAbbreviationByName(String name) {
        for (ChinaProvince province : values()) {
            if (province.fullName.equals(name) || (province.fullName + "省").equals(name) || (province.fullName + "市").equals(name)) {
                return province.abbreviation;
            }
        }
        return null;
    }
}
