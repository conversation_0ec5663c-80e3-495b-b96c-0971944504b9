package com.concise.gen.acceptdischargedinfo.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 释放信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:42
 */
@Data
@TableName("accept_discharged_info")
public class AcceptDischargedInfo {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String taskId;

    /**
     * 数据状态
     */
    private String zt;

    /**
     * 数据来源(机构类型)
     */
    private String sjlylx;

    /**
     * 接收单位
     */
    private String jsdw;
    private String jsdwId;
    private String jsdwPids;

    /**
     * 接收单位名称
     */
    private String jsdwmc;

    /**
     * 推送单位
     */
    private String tsdw;

    /**
     * 推送单位名称
     */
    private String tsdwmc;

    /**
     * 送达时间
     */
    @Excel(name = "送达时间", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date sdsj;

    /**
     * 统一赋号
     */
    private String tyfh;

    /**
     * 监狱案件编号
     */
    private String jyajbh;

    /**
     * 罪犯编号
     */
    private String zfbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 曾用名
     */
    private String cym;

    /**
     * 性别
     */
    private String xb;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date csrq;

    /**
     * 犯罪时是否未成年
     */
    private String fzssfwcn;

    /**
     * 未成年
     */
    private String wcn;

    /**
     * 是否有精神病
     */
    private String sfyjsb;

    /**
     * 鉴定机构
     */
    private String jdjg;

    /**
     * 是否有传染病
     */
    private String sfycrb;

    /**
     * 传染病类型
     */
    private String crblx;

    /**
     * 国籍
     */
    private String gj;

    /**
     * 民族
     */
    private String mz;

    /**
     * 工作单位/所在学校
     */
    private String gzdwszxx;

    /**
     * 单位/学校所在地
     */
    private String dwxxszd;

    /**
     * 单位联系电话
     */
    private String dwlxdh;

    /**
     * 个人联系电话
     */
    private String grlxdh;

    /**
     * 出生地
     */
    private String csd;

    /**
     * 户籍地
     */
    private String hjd;

    /**
     * 户籍地详址
     */
    private String hjdxz;

    /**
     * 现住地
     */
    private String xzd;

    /**
     * 现住地详址
     */
    private String xzdxz;

    /**
     * 文化程度
     */
    private String whcd;

    /**
     * 婚姻状况
     */
    private String hyzk;

    /**
     * 身份
     */
    private String sf;

    /**
     * 政治面貌
     */
    private String zzmm;

    /**
     * 职业
     */
    private String zy;

    /**
     * 职务
     */
    private String zw;

    /**
     * 职级
     */
    private String zj;

    /**
     * 是否累犯
     */
    private String sflf;

    /**
     * 是否暴力犯
     */
    private String sfblf;

    /**
     * 是否老年犯
     */
    private String sflnf;

    /**
     * 是否残疾
     */
    private String sfcj;

    /**
     * 是否病犯
     */
    private String sfbf;

    /**
     * 生效判决机关
     */
    private String sxpjjg;

    /**
     * 判决文书文号
     */
    private String pjwswh;

    /**
     * 判决日期
     */
    @Excel(name = "判决日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date pjrq;

    /**
     * 判决罪名
     */
    private String pjzm;

    /**
     * 判决其他罪名
     */
    private String pjqtzm;

    /**
     * 是否数罪并罚
     */
    private String sfszbf;

    /**
     * 主刑
     */
    private String zx;

    /**
     * 原判刑期
     */
    private String ypxq;

    /**
     * 原刑期起日
     */
    @Excel(name = "原刑期起日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date yxqqr;

    /**
     * 原刑期止日
     */
    @Excel(name = "原刑期止日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date yxqzr;

    /**
     * 附加刑
     */
    private String fjx;

    /**
     * 附加刑具体情况
     */
    private String fjxjtqk;

    /**
     * 缓刑考验期
     */
    private String hxkyq;

    /**
     * 财产性判项
     */
    private String ccxpx;

    /**
     * 罚金金额（万元）
     */
    private String fjjewy;

    /**
     * 没收财产金额（万元）
     */
    private String msccjewy;

    /**
     * 其他财产性判项金额（万元）
     */
    private String qtccxpxjewy;

    /**
     * 剥夺政治权利期限
     */
    private String bdzzqlqx;

    /**
     * 是否“五独”
     */
    private String sfwd;

    /**
     * 是否“五涉”
     */
    private String sfws;

    /**
     * 是否有“四史”
     */
    private String sfyss;

    /**
     * 刑罚执行日期
     */
    @Excel(name = "刑罚执行日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date xfzxrq;

    /**
     * 入监日期
     */
    @Excel(name = "入监日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date rjrq;

    /**
     * 现刑期
     */
    private String xxq;

    /**
     * 现刑期起日
     */
    @Excel(name = "现刑期起日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date xxqqr;

    /**
     * 现刑期止日
     */
    @Excel(name = "现刑期止日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date xxqzr;

    /**
     * 已服刑期
     */
    private String yfxq;

    /**
     * 剩余刑期
     */
    private String syxq;

    /**
     * 释放类别
     */
    private String sflb;

    /**
     * 假释考验期起日
     */
    @Excel(name = "假释考验期起日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jskyqqr;

    /**
     * 假释考验期止日
     */
    @Excel(name = "假释考验期止日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date jskyqzr;

    /**
     * 是否剥政
     */
    private String sfbz;

    /**
     * 现剥政期限
     */
    private String xbzqx;

    /**
     * 现剥政起日
     */
    @Excel(name = "现剥政起日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date xbzqr;

    /**
     * 现剥政止日
     */
    @Excel(name = "现剥政止日", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date xbzzr;

    /**
     * 技术特长及等级证书
     */
    private String jstcjdjzs;

    /**
     * 服刑期间奖惩情况
     */
    private String fxqjjcqk;

    /**
     * 服刑期间刑种、刑期变动情况
     */
    private String fxxqbdqk;

    /**
     * 服刑期间表现
     */
    private String fxqjbx;

    /**
     * 释放后住址
     */
    private String sfhzz;

    /**
     * 安置帮教工作办公室
     */
    private String azbjgzbgs;

    /**
     * 帮教人员类型
     */
    private String bjrylx;

    /**
     * 出监日期
     */
    @Excel(name = "出监日期", databaseFormat = "yyyy-MM-dd HH:mm:ss", format = "yyyy-MM-dd", width = 20)
    private Date cjrq;

}
