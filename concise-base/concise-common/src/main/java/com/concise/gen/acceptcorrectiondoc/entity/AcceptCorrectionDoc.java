package com.concise.gen.acceptcorrectiondoc.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.concise.common.file.util.OssSignedUrlUtil;
import lombok.Data;

/**
 * 矫正对象法律文书信息信息接收表
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
 */
@Data
@TableName("accept_correction_doc")
public class AcceptCorrectionDoc{

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     *
     */
    private String contactId;
    private String xtbh;

    /**
     * 文书名称
     */
    private String ws;

    /**
     * 文书代码
     */
    private String wsdm;

    /**
     * URI
     */
    private String uri;

    /**
     *
     */
    private String ossUrl;
    private Long fileId;
    public String getSignedUrl() {
        if (ObjectUtil.isNotEmpty(ossUrl)) {
            return OssSignedUrlUtil.generatePresignedUrlWithPublicDomain(ossUrl);
        }
        return null;
    }
}
