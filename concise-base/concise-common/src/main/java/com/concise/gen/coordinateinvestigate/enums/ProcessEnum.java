package com.concise.gen.coordinateinvestigate.enums;

/**
 * <AUTHOR>
 */

public enum ProcessEnum {

    /**
     *
     */
    REJECT("0", "退回")
    ,PASS("1", "通过");

    private final String code;

    private final String message;

    ProcessEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }
}
