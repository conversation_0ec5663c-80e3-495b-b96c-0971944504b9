package com.concise.gen.acceptcriminalrecord.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.concise.common.exception.ServiceException;
import com.concise.common.factory.PageFactory;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.acceptcriminalrecord.entity.AcceptCriminalRecord;
import com.concise.gen.acceptcriminalrecord.enums.AcceptCriminalRecordExceptionEnum;
import com.concise.gen.acceptcriminalrecord.mapper.AcceptCriminalRecordMapper;
import com.concise.gen.acceptcriminalrecord.param.AcceptCriminalRecordParam;
import com.concise.gen.acceptcriminalrecord.service.AcceptCriminalRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 前科信息接收表service接口实现类
 *
 * <AUTHOR>
 * @date 2022-07-26 17:53:43
 */
@Service
public class AcceptCriminalRecordServiceImpl extends ServiceImpl<AcceptCriminalRecordMapper, AcceptCriminalRecord> implements AcceptCriminalRecordService {

    @Override
    public PageResult<AcceptCriminalRecord> page(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        QueryWrapper<AcceptCriminalRecord> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(acceptCriminalRecordParam)) {

            queryWrapper.lambda().eq(AcceptCriminalRecord::getContactId, acceptCriminalRecordParam.getContactId());
            // 根据刑种 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getXz())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getXz, acceptCriminalRecordParam.getXz());
            }
            // 根据判决日期 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getPjrq())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getPjrq, acceptCriminalRecordParam.getPjrq());
            }
            // 根据判决法院 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getPjfy())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getPjfy, acceptCriminalRecordParam.getPjfy());
            }
            // 根据罪名 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getZm())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getZm, acceptCriminalRecordParam.getZm());
            }
            // 根据原判刑期 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getYpxq())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getYpxq, acceptCriminalRecordParam.getYpxq());
            }
            // 根据执行机关 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getZxjg())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getZxjg, acceptCriminalRecordParam.getZxjg());
            }
            // 根据执行刑期 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getZxxq())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getZxxq, acceptCriminalRecordParam.getZxxq());
            }
            // 根据备注 查询
            if (ObjectUtil.isNotEmpty(acceptCriminalRecordParam.getBz())) {
                queryWrapper.lambda().eq(AcceptCriminalRecord::getBz, acceptCriminalRecordParam.getBz());
            }
        }
        return new PageResult<>(this.page(PageFactory.defaultPage(), queryWrapper));
    }

    @Override
    public List<AcceptCriminalRecord> list(String contactId) {
        QueryWrapper<AcceptCriminalRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcceptCriminalRecord::getContactId, contactId);
        return this.list(queryWrapper);
    }

    @Override
    public void add(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        AcceptCriminalRecord acceptCriminalRecord = new AcceptCriminalRecord();
        BeanUtil.copyProperties(acceptCriminalRecordParam, acceptCriminalRecord);
        this.save(acceptCriminalRecord);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        this.removeById(acceptCriminalRecordParam.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        AcceptCriminalRecord acceptCriminalRecord = this.queryAcceptCriminalRecord(acceptCriminalRecordParam);
        BeanUtil.copyProperties(acceptCriminalRecordParam, acceptCriminalRecord);
        this.updateById(acceptCriminalRecord);
    }

    @Override
    public AcceptCriminalRecord detail(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        return this.queryAcceptCriminalRecord(acceptCriminalRecordParam);
    }

    /**
     * 获取前科信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-26 17:53:43
     */
    private AcceptCriminalRecord queryAcceptCriminalRecord(AcceptCriminalRecordParam acceptCriminalRecordParam) {
        AcceptCriminalRecord acceptCriminalRecord = this.getById(acceptCriminalRecordParam.getId());
        if (ObjectUtil.isNull(acceptCriminalRecord)) {
            throw new ServiceException(AcceptCriminalRecordExceptionEnum.NOT_EXIST);
        }
        return acceptCriminalRecord;
    }
}
