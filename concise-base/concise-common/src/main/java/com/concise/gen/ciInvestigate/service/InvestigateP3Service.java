package com.concise.gen.ciInvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.ciInvestigate.entity.InvestigateP3;
import com.concise.gen.ciInvestigate.param.InvestigateP3Param;
import java.util.List;

/**
 * 迁入调查评估_调查service接口
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:16
 */
public interface InvestigateP3Service extends IService<InvestigateP3> {

    /**
     * 查询迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    PageResult<InvestigateP3> page(InvestigateP3Param investigateP3Param);

    /**
     * 迁入调查评估_调查列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    List<InvestigateP3> list(InvestigateP3Param investigateP3Param);

    /**
     * 添加迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    void add(InvestigateP3Param investigateP3Param);

    /**
     * 删除迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    void delete(InvestigateP3Param investigateP3Param);

    /**
     * 编辑迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
    void edit(InvestigateP3Param investigateP3Param);

    /**
     * 查看迁入调查评估_调查
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:16
     */
     InvestigateP3 detail(InvestigateP3Param investigateP3Param);
}
