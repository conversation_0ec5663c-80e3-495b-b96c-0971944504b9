package com.concise.gen.dataCenter.correctionban. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.dataCenter.correctionban. param.CorrectionBanDcParam;
import com.concise.gen.dataCenter.correctionban. service.CorrectionBanDcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 禁止令控制器
 *
 * <AUTHOR>
 * @date 2023-09-14 13:47:51
 */
@Api(tags = "禁止令")
@RestController
public class CorrectionBanDcController {

    @Resource
    private CorrectionBanDcService correctionBanDcService;

    /**
     * 查询禁止令
     *
     * <AUTHOR>
     * @date 2023-09-14 13:47:51
     */
    @Permission
    @GetMapping("/correctionBanDc/page")
    @ApiOperation("禁止令_分页查询")
    public ResponseData page(CorrectionBanDcParam correctionBanDcParam) {
        return new SuccessResponseData(correctionBanDcService.page(correctionBanDcParam));
    }

    /**
     * 添加禁止令
     *
     * <AUTHOR>
     * @date 2023-09-14 13:47:51
     */
    @Permission
    @PostMapping("/correctionBanDc/add")
    @ApiOperation("禁止令_增加")
    @BusinessLog(title = "禁止令_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(CorrectionBanDcParam.add.class) CorrectionBanDcParam correctionBanDcParam) {
        correctionBanDcService.add(correctionBanDcParam);
        return new SuccessResponseData();
    }

    /**
     * 删除禁止令
     *
     * <AUTHOR>
     * @date 2023-09-14 13:47:51
     */
    @Permission
    @PostMapping("/correctionBanDc/delete")
    @ApiOperation("禁止令_删除")
    @BusinessLog(title = "禁止令_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(CorrectionBanDcParam.delete.class) CorrectionBanDcParam correctionBanDcParam) {
        correctionBanDcService.delete(correctionBanDcParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑禁止令
     *
     * <AUTHOR>
     * @date 2023-09-14 13:47:51
     */
    @Permission
    @PostMapping("/correctionBanDc/edit")
    @ApiOperation("禁止令_编辑")
    @BusinessLog(title = "禁止令_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(CorrectionBanDcParam.edit.class) CorrectionBanDcParam correctionBanDcParam) {
        correctionBanDcService.edit(correctionBanDcParam);
        return new SuccessResponseData();
    }

    /**
     * 查看禁止令
     *
     * <AUTHOR>
     * @date 2023-09-14 13:47:51
     */
    @Permission
    @GetMapping("/correctionBanDc/detail")
    @ApiOperation("禁止令_查看")
    public ResponseData detail(@Validated(CorrectionBanDcParam.detail.class) CorrectionBanDcParam correctionBanDcParam) {
        return new SuccessResponseData(correctionBanDcService.detail(correctionBanDcParam));
    }

    /**
     * 禁止令列表
     *
     * <AUTHOR>
     * @date 2023-09-14 13:47:51
     */
    @Permission
    @GetMapping("/correctionBanDc/list")
    @ApiOperation("禁止令_列表")
    public ResponseData list(CorrectionBanDcParam correctionBanDcParam) {
        return new SuccessResponseData(correctionBanDcService.list(correctionBanDcParam));
    }

}
