package com.concise.gen.coordinatesupervisesubmit.param;

import com.concise.common.pojo.base.param.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Set;

/**
* 提请外出监管协同参数类
 *
 * <AUTHOR>
 * @date 2022-07-05 15:46:04
*/
@EqualsAndHashCode(callSuper = true)
@Data
public class CoordinateSuperviseSubmitParam extends BaseParam {

    private Set<String> orgs;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空，请检查id参数", groups = {edit.class, delete.class, detail.class})
    private String id;

    /**
     * 委托单位
     */
    @NotBlank(message = "委托单位不能为空，请检查wtdwmc参数", groups = {add.class, edit.class})
    private String wtdwmc;

    /**
     * 委托单位id
     */
    @NotBlank(message = "委托单位id不能为空，请检查wtdwId参数", groups = {add.class, edit.class})
    private String wtdwId;

    /**
     * 收到委托时间
     */
    @NotNull(message = "收到委托时间不能为空，请检查sdwtsj参数", groups = {add.class, edit.class})
    private String sdwtsj;

    /**
     * 委托编号
     */
    @NotBlank(message = "委托编号不能为空，请检查wtbh参数", groups = {add.class, edit.class})
    private String wtbh;

    /**
     * 社区矫正人员ID
     */
    @NotBlank(message = "社区矫正人员ID不能为空，请检查sqjzryId参数", groups = {add.class, edit.class})
    private String sqjzryId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空，请检查xm参数", groups = {add.class, edit.class})
    private String xm;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空，请检查sfzh参数", groups = {add.class, edit.class})
    private String sfzh;

    /**
     * 矫正单位
     */
    @NotBlank(message = "矫正单位不能为空，请检查jzdw参数", groups = {add.class, edit.class})
    private String jzdw;

    /**
     * 矫正单位ID
     */
    @NotBlank(message = "矫正单位ID不能为空，请检查jzdwId参数", groups = {add.class, edit.class})
    private String jzdwId;

    /**
     * 外出申请编号
     */
    @NotBlank(message = "外出申请编号不能为空，请检查wcsqbh参数", groups = {add.class, edit.class})
    private String wcsqbh;

    /**
     * 外出申请时间
     */
    @NotNull(message = "外出申请时间不能为空，请检查wcsqsj参数", groups = {add.class, edit.class})
    private String wcsqsj;

    /**
     * 外出目的地
     */
    @NotBlank(message = "外出目的地不能为空，请检查wcmdd参数", groups = {add.class, edit.class})
    private String wcmdd;

    /**
     * 外出开始时间
     */
    @NotNull(message = "外出开始时间不能为空，请检查wckssj参数", groups = {add.class, edit.class})
    private String wckssj;

    /**
     * 外出结束时间
     */
    @NotNull(message = "外出结束时间不能为空，请检查wcjssj参数", groups = {add.class, edit.class})
    private String wcjssj;

    /**
     * 外出原因
     */
    @NotBlank(message = "外出原因不能为空，请检查wcyy参数", groups = {add.class, edit.class})
    private String wcyy;

    /**
     * 外出天数
     */
    @NotNull(message = "外出天数不能为空，请检查wcts参数", groups = {add.class, edit.class})
    private Integer wcts;

    /**
     * 状态（字典 0正常 1停用 2删除)
     */
    @NotNull(message = "状态（字典 0正常 1停用 2删除)不能为空，请检查zt参数", groups = {add.class, edit.class})
    private Integer zt;

}
