package com.concise.gen.coordinateinvestigate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.concise.common.pojo.page.PageResult;
import com.concise.gen.coordinateinvestigate.entity.CoordinateInvestigateP3;
import com.concise.gen.coordinateinvestigate.param.CoordinateInvestigateP3Param;

import java.util.List;

/**
 * 调查评估协查_调查service接口
 *
 * <AUTHOR>
 * @date 2023-06-12 20:47:53
 */
public interface CoordinateInvestigateP3Service extends IService<CoordinateInvestigateP3> {

    /**
     * 查询调查评估协查_调查
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
    PageResult<CoordinateInvestigateP3> page(CoordinateInvestigateP3Param coordinateInvestigateP3Param);

    /**
     * 调查评估协查_调查列表
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
    List<CoordinateInvestigateP3> list(CoordinateInvestigateP3Param coordinateInvestigateP3Param);

    /**
     * 添加调查评估协查_调查
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
    void add(CoordinateInvestigateP3Param coordinateInvestigateP3Param);

    /**
     * 删除调查评估协查_调查
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
    void delete(CoordinateInvestigateP3Param coordinateInvestigateP3Param);

    /**
     * 编辑调查评估协查_调查
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
    void edit(CoordinateInvestigateP3Param coordinateInvestigateP3Param);

    /**
     * 查看调查评估协查_调查
     *
     * <AUTHOR>
     * @date 2023-06-12 20:47:53
     */
     CoordinateInvestigateP3 detail(CoordinateInvestigateP3Param coordinateInvestigateP3Param);
}
