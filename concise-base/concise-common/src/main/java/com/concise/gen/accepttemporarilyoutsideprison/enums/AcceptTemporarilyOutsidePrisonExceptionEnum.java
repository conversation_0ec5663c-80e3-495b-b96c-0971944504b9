package com.concise.gen.accepttemporarilyoutsideprison.enums;

import com.concise.common.annotion.ExpEnumType;
import com.concise.common.consts.SysExpEnumConstant;
import com.concise.common.exception.enums.abs.AbstractBaseExceptionEnum;
import com.concise.common.factory.ExpEnumCodeFactory;

/**
 * 暂予监外接收表
 *
 * <AUTHOR>
 * @date 2022-12-01 10:33:30
 */
@ExpEnumType(module = SysExpEnumConstant.SNOWY_SYS_MODULE_EXP_CODE, kind = SysExpEnumConstant.SYS_POS_EXCEPTION_ENUM)
public enum AcceptTemporarilyOutsidePrisonExceptionEnum implements AbstractBaseExceptionEnum {

    /**
     * 数据不存在
     */
    NOT_EXIST(1, "此数据不存在"),
    SYNC_ERROR(2, "同步给执法办案失败，请联系工作人员");

    private final Integer code;

    private final String message;
        AcceptTemporarilyOutsidePrisonExceptionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return ExpEnumCodeFactory.getExpEnumCode(this.getClass(), code);
    }

    @Override
    public String getMessage() {
        return message;
    }

}
