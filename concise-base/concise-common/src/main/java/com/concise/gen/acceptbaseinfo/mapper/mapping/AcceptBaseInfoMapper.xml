<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.acceptbaseinfo.mapper.AcceptBaseInfoMapper">
    <insert id="insertExtOrgStatistics">
        replace into ext_org_statistics  (id,sys_org_id,ext_org_code,ext_org_type,accepted,returned,`sum`)
        select jsdw_id
             ,jsdw_id
             ,tsdw
             ,max(type)
             ,sum(if(zt = '4',c,0))
             ,sum(if(zt = '2' or zt = '3',c,0))
             ,sum(c)
        from (select jsdw_id
                   , tsdw
                   , max(sjlylx) as type
                   , zt
                   , count(*)    as c
              from accept_invest_info where zt != '99'
              group by jsdw_id,tsdw, zt
              union all
              select jsdw_id
                   , tsdw
                   , max(sjlylx) as type
                   , zt
                   , count(*)    as c
              from accept_base_info where zt != '99'
              group by jsdw_id,tsdw, zt
             ) temp where jsdw_id is not null
        group by jsdw_id,tsdw
    </insert>
    <update id="updateExtOrgStatisticsOrgName">
        update ext_org_statistics
            left join ext_org_info on ext_org_info.org_code = ext_org_statistics.ext_org_code and `type` = ext_org_type
            set ext_org_statistics.ext_org_name = ext_org_info.org_name
        where
            ext_org_statistics.ext_org_name is null
    </update>

    <select id="distName" resultType="java.lang.String">
        select dist_name
        from dist where code = #{dist}
            limit 1
        ;
    </select>

    <select id="extDictTrans" resultType="java.lang.String">
        select target_value
        from ext_dict_map
        where map_type = #{param1}
          and dict_type = #{param2}
          and source_value = #{param3}
            limit 1
    </select>
    <select id="indicatorCollaboration" resultType="com.concise.gen.acceptbaseinfo.vo.StatisticsVo">
        SELECT
        COUNT(*) AS amount,
        zt AS type
        FROM accept_base_info
        WHERE
         DATE_FORMAT( sdsj, '%Y-%m' )= #{month}
        AND (jsdw_pids like concat('%',#{orgId},'%') or jsdw_id = #{orgId})
        GROUP BY zt
    </select>
    <select id="rankExtOrgStatistics" resultType="com.concise.gen.acceptbaseinfo.vo.ExtOrgStatisticsVo">
        select
        ext_org_code
        ,max(ext_org_name) as ext_org_name
        ,sum(accepted) as accepted
        ,sum(returned) as returned
        ,sum(`sum`) as sum
        from ext_org_statistics
        where ext_org_type = #{param1}
        <if test="param2!=null">
            and sys_org_id in
            <foreach collection="param2" index="index" item="i" open="(" separator="," close=")">
                #{i}
            </foreach>
        </if>
        group by ext_org_code
        order by ext_org_code asc
    </select>
    <select id="trend" resultType="com.concise.gen.acceptbaseinfo.vo.StatisticsVo">
        select sys_org.name as y
        , amount     from
        (select max(org_id) as org_id
             , sum(c)    as amount
        from (
            <if test="level == 1">
                select substr(jsdw_pids,41,32) as org_id, count(*) as c
                from accept_base_info
                <if test="time != null and time != ''">where date_format (sdsj,'%Y-%m-%d') >= date_format (#{time},'%Y-%m-%d') </if>
                group by substr(jsdw_pids,41,32)
                union all
                select substr(jsdw_pids,41,32) as org_id, count(*) as c
                from accept_invest_info
                <if test="time != null and time != ''">where date_format (sdsj,'%Y-%m-%d') >= date_format (#{time},'%Y-%m-%d') </if>
                group by substr(jsdw_pids,41,32)
            </if>
            <if test="level == 2">
                select jsdw_id as org_id, count(*) as c
                from accept_base_info
                where jsdw_pids like concat('%',#{orgId},'%')
                <if test="time != null and time != ''"> and date_format (sdsj,'%Y-%m-%d') >= date_format (#{time},'%Y-%m-%d') </if>
                group by jsdw_id
                union all
                select jsdw_id as org_id, count(*) as c
                from accept_invest_info
                where jsdw_pids like concat('%',#{orgId},'%')
                <if test="time != null and time != ''"> and date_format (sdsj,'%Y-%m-%d') >= date_format (#{time},'%Y-%m-%d') </if>
                group by jsdw_id
            </if>
            <if test="level == 3 or level == 4">
                select jzjg as org_id, count(*) as c
                from accept_base_info
                where (jzjg in (select id from sys_org where pid = #{orgId}) or jzjg = #{orgId})
                <if test="time != null and time != ''"> and date_format (sdsj,'%Y-%m-%d') >= date_format (#{time},'%Y-%m-%d') </if>
                group by jzjg
                union all
                select QXDCDW as org_id, count(*) as c
                from accept_invest_info
                where (QXDCDW in (select id from sys_org where pid = #{orgId}) or QXDCDW = #{orgId})
                <if test="time != null and time != ''"> and date_format (sdsj,'%Y-%m-%d') >= date_format (#{time},'%Y-%m-%d') </if>
                group by QXDCDW
            </if>
             ) temp group by org_id) temp1 right join sys_org on sys_org.id = temp1.org_id
        where sys_org.pid = #{orgId} and sys_org.type = 'sfs'
    </select>
    <select id="statisticsLevel1" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select `name`      as org_name
             , id          as org_id
             , sum(t2.zt1) as zt1
             , sum(t2.zt2) as zt2
             , sum(t2.zt3) as zt3
             , sum(t2.zt4) as zt4
             , sum(t2.zt5) as zt5
        from (select pid, temp.*
              from (select jsdw_id
                         , count(1)                            as zt1
                         , sum(if(zt = '0', 1, 0))             as zt2
                         , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                         , sum(if(zt = '1', 1, 0))             as zt4
                         , sum(if(zt = '4', 1, 0))             as zt5
                    from accept_base_info
                    where zt != '99'
                    <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                    <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
                    group by jsdw_id) temp
                       left join sys_org on jsdw_id = id) t2
                 left join sys_org on t2.pid = id
        where `level` = 2
        group by id;
    </select>
    <select id="statisticsLevel2" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select `name` as org_name
             , temp.*
        from (select jsdw_id                             as org_id
                   , count(1)                            as zt1
                   , sum(if(zt = '0', 1, 0))             as zt2
                   , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                   , sum(if(zt = '1', 1, 0))             as zt4
                   , sum(if(zt = '4', 1, 0))             as zt5
              from accept_base_info
              where zt != '99'
                  and jsdw_pids like concat('%',#{orgId}, '%')
                <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
              group by jsdw_id) temp
                 left join sys_org on org_id = id
        where `level` = 3
    </select>
    <select id="statisticsLevel3" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select `name` as org_name
             , temp.*
        from (select jzjg                                as org_id
                   , count(1)                            as zt1
                   , sum(if(zt = '0', 1, 0))             as zt2
                   , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                   , sum(if(zt = '1', 1, 0))             as zt4
                   , sum(if(zt = '4', 1, 0))             as zt5
              from accept_base_info
              where zt != '99'
                and jsdw_id = #{orgId}
                <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
              group by jzjg) temp
                 left join sys_org on org_id = id
        where `level` = 4
    </select>
    <select id="statisticsLevel4" resultType="com.concise.gen.acceptbaseinfo.vo.StatVo">
        select name as org_name
             , temp.*
        from (select jzjg                                as org_id
                   , count(1)                            as zt1
                   , sum(if(zt = '0', 1, 0))             as zt2
                   , sum(if(zt = '2' or zt = '3', 1, 0)) as zt3
                   , sum(if(zt = '1', 1, 0))             as zt4
                   , sum(if(zt = '4', 1, 0))             as zt5
              from accept_base_info
              where zt != '99'
                and jzjg = #{orgId}
                <if test="start != null and start != ''"> and sdsj >= #{start}</if>
                <if test="end != null and end != ''"> <![CDATA[ and sdsj <= #{end} ]]></if>
              group by jzjg) temp
                 left join sys_org on org_id = id
        where `level` = 4
    </select>
</mapper>
