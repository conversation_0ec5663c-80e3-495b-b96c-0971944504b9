<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.concise.gen.accepttemporarilyoutsideprison.mapper.AcceptTemporarilyOutsidePrisonMapper">

    <select id="getByTyfh" resultType="com.concise.gen.accepttemporarilyoutsideprison.entity.AcceptTemporarilyOutsidePrison">
        select *
        from accept_temporarily_outside_prison where TYFH = #{tyfh}
            limit 1
        ;
    </select>
</mapper>
