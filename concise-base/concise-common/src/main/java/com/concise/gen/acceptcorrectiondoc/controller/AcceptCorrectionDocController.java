package com.concise.gen.acceptcorrectiondoc. controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.acceptcorrectiondoc.entity.AcceptCorrectionDoc;
import com.concise.gen.acceptcorrectiondoc.param.AcceptCorrectionDocParam;
import com.concise.gen.acceptcorrectiondoc.service.AcceptCorrectionDocService;
import com.concise.gen.dataCenter.correctionobjectinformation.entity.CorrectionObjectInformation;
import com.concise.gen.petitionletterinfo.utils.LetterUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 矫正对象法律文书信息信息接收表控制器
 *
 * <AUTHOR>
 * @date 2022-07-12 13:33:33
 */
@Api(tags = "矫正对象法律文书信息信息接收表")
@RestController
public class AcceptCorrectionDocController {

    @Resource
    private AcceptCorrectionDocService acceptCorrectionDocService;
    @Resource
    private SysFileInfoService sysFileInfoService;

    /**
     * 查询矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    @GetMapping("/acceptCorrectionDoc/page")
    @ApiOperation("矫正对象法律文书信息信息接收表_分页查询")
    public ResponseData page(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        return new SuccessResponseData(acceptCorrectionDocService.page(acceptCorrectionDocParam));
    }

    /**
     * 添加矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    @PostMapping("/acceptCorrectionDoc/add")
    @ApiOperation("矫正对象法律文书信息信息接收表_增加")
    @BusinessLog(title = "矫正对象法律文书信息信息接收表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(AcceptCorrectionDocParam.add.class) AcceptCorrectionDocParam acceptCorrectionDocParam) {
        acceptCorrectionDocService.add(acceptCorrectionDocParam);
        return new SuccessResponseData();
    }

    /**
     * 删除矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    @PostMapping("/acceptCorrectionDoc/delete")
    @ApiOperation("矫正对象法律文书信息信息接收表_删除")
    @BusinessLog(title = "矫正对象法律文书信息信息接收表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(AcceptCorrectionDocParam.delete.class) AcceptCorrectionDocParam acceptCorrectionDocParam) {
        acceptCorrectionDocService.delete(acceptCorrectionDocParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    @PostMapping("/acceptCorrectionDoc/edit")
    @ApiOperation("矫正对象法律文书信息信息接收表_编辑")
    @BusinessLog(title = "矫正对象法律文书信息信息接收表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(AcceptCorrectionDocParam.edit.class) AcceptCorrectionDocParam acceptCorrectionDocParam) {
        acceptCorrectionDocService.edit(acceptCorrectionDocParam);
        return new SuccessResponseData();
    }

    /**
     * 查看矫正对象法律文书信息信息接收表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    @GetMapping("/acceptCorrectionDoc/detail")
    @ApiOperation("矫正对象法律文书信息信息接收表_查看")
    public ResponseData detail(@Validated(AcceptCorrectionDocParam.detail.class) AcceptCorrectionDocParam acceptCorrectionDocParam) {
        return new SuccessResponseData(acceptCorrectionDocService.detail(acceptCorrectionDocParam));
    }

    /**
     * 矫正对象法律文书信息信息接收表列表
     *
     * <AUTHOR>
     * @date 2022-07-12 13:33:33
     */
    @GetMapping("/acceptCorrectionDoc/list")
    @ApiOperation("矫正对象法律文书信息信息接收表_列表")
    public ResponseData list(AcceptCorrectionDocParam acceptCorrectionDocParam) {
        return new SuccessResponseData(acceptCorrectionDocService.list(acceptCorrectionDocParam.getContactId()));
    }


    @GetMapping("/acceptCorrectionDoc/reLoadFileFromOss")
    public ResponseData reLoadFileFromOss(String id,String contactId) {
        if (ObjectUtil.isNotEmpty(id)) {
            return new SuccessResponseData(acceptCorrectionDocService.reLoadFileFromOss(id));
        }else if (ObjectUtil.isNotEmpty(contactId)){
            return new SuccessResponseData(acceptCorrectionDocService.reLoadFileListFromOss(contactId));
        }
        return new SuccessResponseData();
    }

    @GetMapping("/acceptCorrectionDoc/batchDownLoad")
    public void batchDownLoad(String contactId,String xm) {
        acceptCorrectionDocService.batchDownLoad(contactId,xm);
    }

    @GetMapping("/acceptCorrectionDoc/updateFilename")
    public void updateFilename() {
        QueryWrapper<AcceptCorrectionDoc> queryWrapper = new QueryWrapper<>();
        Page<AcceptCorrectionDoc> page = new Page<>(1, 50);
        PageResult<AcceptCorrectionDoc> pageResult = new PageResult<>(acceptCorrectionDocService.page(page, queryWrapper));
        while (pageResult.getTotalPage() >= pageResult.getPageNo()) {
            System.out.println(pageResult.getPageNo()+"/"+pageResult.getTotalPage());
            pageResult.getRows().forEach(c ->{
                if (ObjectUtil.isNotEmpty(c.getFileId()) &&  ObjectUtil.isNotEmpty(c.getWs())) {
                    sysFileInfoService.lambdaUpdate().set(SysFileInfo::getFileOriginName,c.getWs()).eq(SysFileInfo::getId,c.getFileId()).update();
                }
            });
            page.setCurrent(pageResult.getPageNo()+1);
            pageResult = new PageResult<>(acceptCorrectionDocService.page(page, queryWrapper));
        }
    }
}
