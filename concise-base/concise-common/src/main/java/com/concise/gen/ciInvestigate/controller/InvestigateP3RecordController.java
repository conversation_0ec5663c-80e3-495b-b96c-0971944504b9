package com.concise.gen.ciInvestigate. controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.annotion.Permission;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.gen.ciInvestigate. param.InvestigateP3RecordParam;
import com.concise.gen.ciInvestigate. service.InvestigateP3RecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

/**
 * 迁入调查评估_调查_记录控制器
 *
 * <AUTHOR>
 * @date 2023-06-15 18:11:18
 */
@Api(tags = "迁入调查评估_调查_记录")
@RestController
public class InvestigateP3RecordController {

    @Resource
    private InvestigateP3RecordService investigateP3RecordService;

    /**
     * 查询迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    @Permission
    @GetMapping("/investigateP3Record/page")
    @ApiOperation("迁入调查评估_调查_记录_分页查询")
    @BusinessLog(title = "迁入调查评估_调查_记录_分页查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData page(InvestigateP3RecordParam investigateP3RecordParam) {
        return new SuccessResponseData(investigateP3RecordService.page(investigateP3RecordParam));
    }

    /**
     * 添加迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    @Permission
    @PostMapping("/investigateP3Record/add")
    @ApiOperation("迁入调查评估_调查_记录_增加")
    @BusinessLog(title = "迁入调查评估_调查_记录_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(InvestigateP3RecordParam.add.class) InvestigateP3RecordParam investigateP3RecordParam) {
        investigateP3RecordService.add(investigateP3RecordParam);
        return new SuccessResponseData();
    }

    /**
     * 删除迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    @Permission
    @PostMapping("/investigateP3Record/delete")
    @ApiOperation("迁入调查评估_调查_记录_删除")
    @BusinessLog(title = "迁入调查评估_调查_记录_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(InvestigateP3RecordParam.delete.class) InvestigateP3RecordParam investigateP3RecordParam) {
        investigateP3RecordService.delete(investigateP3RecordParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    @Permission
    @PostMapping("/investigateP3Record/edit")
    @ApiOperation("迁入调查评估_调查_记录_编辑")
    @BusinessLog(title = "迁入调查评估_调查_记录_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(InvestigateP3RecordParam.edit.class) InvestigateP3RecordParam investigateP3RecordParam) {
        investigateP3RecordService.edit(investigateP3RecordParam);
        return new SuccessResponseData();
    }

    /**
     * 查看迁入调查评估_调查_记录
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    @Permission
    @GetMapping("/investigateP3Record/detail")
    @ApiOperation("迁入调查评估_调查_记录_查看")
    @BusinessLog(title = "迁入调查评估_调查_记录_查看", opType = LogAnnotionOpTypeEnum.DETAIL)
    public ResponseData detail(@Validated(InvestigateP3RecordParam.detail.class) InvestigateP3RecordParam investigateP3RecordParam) {
        return new SuccessResponseData(investigateP3RecordService.detail(investigateP3RecordParam));
    }

    /**
     * 迁入调查评估_调查_记录列表
     *
     * <AUTHOR>
     * @date 2023-06-15 18:11:18
     */
    @Permission
    @GetMapping("/investigateP3Record/list")
    @ApiOperation("迁入调查评估_调查_记录_列表")
    @BusinessLog(title = "迁入调查评估_调查_记录_列表", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(InvestigateP3RecordParam investigateP3RecordParam) {
        return new SuccessResponseData(investigateP3RecordService.list(investigateP3RecordParam));
    }

}
