package com.concise.modular.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.exception.ServiceException;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.gen.investigation.param.InvestigationFeedbackParam;
import com.concise.gen.signatureauthorization.enums.SignatureAuthorizationExceptionEnum;
import com.concise.gen.signatureauthorization.param.SignatureAuthorizationParam;
import com.concise.gen.signatureauthorization.service.SignatureAuthorizationService;
import com.concise.gen.signaturemaintenance.entity.SignatureMaintenance;
import com.concise.gen.signaturemaintenance.mapper.SignatureMaintenanceMapper;
import com.concise.modular.util.WordUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.io.File;

/**
 * 签章授权表控制器
 *
 * <AUTHOR>
 * @date 2025-07-31 11:04:05
 */
@Slf4j
@Api(tags = "签章授权表")
@RestController
public class SignatureAuthorizationController {

    @Resource
    private SignatureAuthorizationService signatureAuthorizationService;

    @Resource
    private SysFileInfoService sysFileInfoService;

    @Value("${electronicSignature.sqsPath}")
    private String sqsPath;

    @Resource
    private SignatureMaintenanceMapper signatureMaintenanceMapper;

    /**
     * 查询签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    @GetMapping("/signatureAuthorization/page")
    @ApiOperation("签章授权表_分页查询")
    public ResponseData page(SignatureAuthorizationParam signatureAuthorizationParam) {
        return new SuccessResponseData(signatureAuthorizationService.page(signatureAuthorizationParam));
    }

    /**
     * 添加签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    @PostMapping("/signatureAuthorization/add")
    @ApiOperation("签章授权表_增加")
    @BusinessLog(title = "签章授权表_增加", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody @Validated(SignatureAuthorizationParam.add.class) SignatureAuthorizationParam signatureAuthorizationParam) {
        signatureAuthorizationParam.setUserName(LoginContextHolder.me().getSysLoginUser().getName());
        signatureAuthorizationService.add(signatureAuthorizationParam);
        return new SuccessResponseData();
    }

    /**
     * 删除签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    @PostMapping("/signatureAuthorization/delete")
    @ApiOperation("签章授权表_删除")
    @BusinessLog(title = "签章授权表_删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody @Validated(SignatureAuthorizationParam.delete.class) SignatureAuthorizationParam signatureAuthorizationParam) {
        signatureAuthorizationService.delete(signatureAuthorizationParam);
        return new SuccessResponseData();
    }

    /**
     * 编辑签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    @PostMapping("/signatureAuthorization/edit")
    @ApiOperation("签章授权表_编辑")
    @BusinessLog(title = "签章授权表_编辑", opType = LogAnnotionOpTypeEnum.EDIT)
    public ResponseData edit(@RequestBody @Validated(SignatureAuthorizationParam.edit.class) SignatureAuthorizationParam signatureAuthorizationParam) {
        signatureAuthorizationService.edit(signatureAuthorizationParam);
        return new SuccessResponseData();
    }

    /**
     * 查看签章授权表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    @GetMapping("/signatureAuthorization/detail")
    @ApiOperation("签章授权表_查看")
    public ResponseData detail(@Validated(SignatureAuthorizationParam.detail.class) SignatureAuthorizationParam signatureAuthorizationParam) {
        return new SuccessResponseData(signatureAuthorizationService.detail(signatureAuthorizationParam));
    }

    /**
     * 签章授权表列表
     *
     * <AUTHOR>
     * @date 2025-07-31 11:04:05
     */
    @GetMapping("/signatureAuthorization/list")
    @ApiOperation("签章授权表_列表")
    public ResponseData list(SignatureAuthorizationParam signatureAuthorizationParam) {
        return new SuccessResponseData(signatureAuthorizationService.list(signatureAuthorizationParam));
    }

    /**
     * 根据签章id获取授权记录
     */
    @GetMapping("/signatureAuthorization/listBySignatureId")
    @ApiOperation("根据签章id获取授权记录")
    public ResponseData getBySignatureId(@RequestParam String signatureId) {
        return new SuccessResponseData(signatureAuthorizationService.listBySignatureId(signatureId));
    }

    /**
     * 判断当前用户是否有权限使用该印章
     *
     * @param signatureId 签章id
     * @return
     */
    @GetMapping("/signatureAuthorization/hasAuthorization")
    @ApiOperation("判断当前用户是否有权限使用该印章")
    public ResponseData hasAuthorization(String signatureId) {
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        return new SuccessResponseData(signatureAuthorizationService.hasAuthorization(signatureId, sysLoginUser.getId(), sysLoginUser.getName()));
    }

    /**
     * 制作授权书
     */
    @PostMapping("/signatureAuthorization/makeAuthorizationBook")
    @ApiOperation("制作授权书")
    public ResponseData makeAuthorizationBook(@RequestBody SignatureAuthorizationParam signatureAuthorizationParam) {
        //参数校验
        if (ObjectUtil.isEmpty(signatureAuthorizationParam)) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.PARAM_ERROR);
        }
        if (ObjectUtil.isEmpty(signatureAuthorizationParam.getSignatureId())) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.SIGNATURE_ID_NOT_EXIST);
        }
        if (ObjectUtil.isEmpty(signatureAuthorizationParam.getUserNames())) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.USER_NAME_NOT_EXIST);
        }
        //开始时间和结束时间校验
        if (ObjectUtil.isEmpty(signatureAuthorizationParam.getBeginTime())) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.START_TIME_NOT_EXIST);
        }
        if (ObjectUtil.isEmpty(signatureAuthorizationParam.getEndTime())) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.END_TIME_NOT_EXIST);
        }
        //获取签章文件路径
        SignatureMaintenance signatureMaintenance = signatureMaintenanceMapper.selectById(signatureAuthorizationParam.getSignatureId());
        if (ObjectUtil.isEmpty(signatureMaintenance)) {
            throw new ServiceException(SignatureAuthorizationExceptionEnum.SIGNATURE_NOT_EXIST);
        } else {
            signatureAuthorizationParam.setSealNo(signatureMaintenance.getSealNo());
        }
        //获取当前登录用户
        signatureAuthorizationParam.setUserName(LoginContextHolder.me().getSysLoginUser().getName());
        signatureAuthorizationParam.setNowDate(DateUtil.format(DateUtil.date(), DatePattern.CHINESE_DATE_PATTERN));
        try {
            // 调用Service层获取填充好的数据

            // 生成文件名
            String fileName = LoginContextHolder.me().getSysLoginUser().getName() + "的签章授权书.pdf";

            // 创建临时文件目录
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            File tempWordFile = File.createTempFile(fileName, ".docx", tempDir);
            File tempPdfFile = File.createTempFile(fileName, ".pdf", tempDir);

            try {
                // 使用WordUtil填充模板并生成Word文档
                WordUtil.fillTemplateWithObject(sqsPath, tempWordFile.getAbsolutePath(), signatureAuthorizationParam);

                // 将Word转换为PDF
                WordUtil.convertWordToPdfWithLineSpacing(tempWordFile.getAbsolutePath(), tempPdfFile.getAbsolutePath(), 1.5f);


                // 将PDF文件上传到存储系统
                SysFileInfo sysFileInfo = sysFileInfoService.uploadTemplatedFileOss(fileName, tempPdfFile);
//                String pdfPath = sysFileInfo.getFilePath();

                // 返回文件路径
                return new SuccessResponseData(sysFileInfo);

            } finally {
                // 清理临时文件
                if (tempWordFile.exists()) {
                    tempWordFile.delete();
                }
                if (tempPdfFile.exists()) {
                    tempPdfFile.delete();
                }
            }

        } catch (Exception e) {
            log.error("生成授权书PDF失败", e);
            return ResponseData.error("生成授权书PDF失败");
        }
    }

    /**
     * 盖章
     */
    @GetMapping("/signatureAuthorization/seal")
    @ApiOperation("盖章")
    @BusinessLog(title = "盖章", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData seal(@RequestParam String sealNo, String pdfId) {
        return new SuccessResponseData(signatureAuthorizationService.seal(sealNo, pdfId));
    }

}
