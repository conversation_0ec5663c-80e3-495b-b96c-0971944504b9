package com.concise.modular.util;

import java.awt.Color;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.xwpf.usermodel.LineSpacingRule;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.UnderlinePatterns;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.concise.gen.investigation.param.InvestigationRecordParam;
import com.concise.gen.papertopic.entity.PaperTopic;
import com.concise.gen.papertopicitem.entity.PaperTopicItem;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.BaseFont;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import fr.opensagres.poi.xwpf.converter.pdf.PdfConverter;
import fr.opensagres.poi.xwpf.converter.pdf.PdfOptions;
import fr.opensagres.xdocreport.itext.extension.font.IFontProvider;

/**
 * Word文档处理工具类
 * 包含文件操作、文档处理、PDF转换等基础功能
 *
 * <AUTHOR>
 * @date 2025-03-24 14:10:00
 */
@Component
public class WordUtil {

    @Value("${electronicSignature.fontPath}")
    private String fontPath; // 仿宋字体

    @Value("${electronicSignature.fontSongPath}")
    private String fontSongPath; // 宋体

    // 字体路径静态变量
    private static String FONT_PATH; // 仿宋字体路径
    private static String FONT_SONG_PATH; // 宋体路径

    public static final int FONT_SIZE = 12; // 默认字体大小

    // 字体类型枚举
    public enum FontType {
        DEFAULT, // 默认字体（仿宋）
        SONG     // 宋体
    }

    @PostConstruct
    public void init() {
        // 设置字体路径
        FONT_PATH = fontPath; // 仿宋字体
        FONT_SONG_PATH = fontSongPath; // 宋体
        logger.debug("字体路径设置成功，fontPath(仿宋): {}, fontSongPath(宋体): {}", FONT_PATH, FONT_SONG_PATH);
    }

    private static final Logger logger = LoggerFactory.getLogger(WordUtil.class);

    /**
     * 将题目和选项导出到Word文档
     *
     * @param response   HTTP响应对象
     * @param fileName   文件名
     * @param paperTitle 试卷标题
     * @param topicList  题目列表
     * @throws IOException IO异常
     */
    public static void exportQuestionsToWord(HttpServletResponse response, String fileName, String paperTitle, List<PaperTopic> topicList) throws IOException {
        // 创建Word文档
        XWPFDocument document = new XWPFDocument();

        // 设置文档标题
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(paperTitle);
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.addBreak();

        // 遍历题目列表
        for (int i = 0; i < topicList.size(); i++) {
            PaperTopic topic = topicList.get(i);

            // 创建题目段落
            XWPFParagraph topicParagraph = document.createParagraph();
            topicParagraph.setAlignment(ParagraphAlignment.LEFT);
            XWPFRun topicRun = topicParagraph.createRun();
            topicRun.setText((i + 1) + ". " + topic.getTopicName());
            topicRun.setBold(true);
            topicRun.setFontSize(FONT_SIZE);
            ;

            // 如果有指标，添加指标信息
            if (topic.getIndexName() != null && !topic.getIndexName().isEmpty()) {
                XWPFParagraph indexParagraph = document.createParagraph();
                indexParagraph.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun indexRun = indexParagraph.createRun();
                indexRun.setText("指标：" + topic.getIndexName());
                indexRun.setItalic(true);
                indexRun.setFontSize(FONT_SIZE);
                ;
            }

            // 如果有分数，添加分数信息
            if (topic.getTopicScore() != null) {
                XWPFParagraph scoreParagraph = document.createParagraph();
                scoreParagraph.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun scoreRun = scoreParagraph.createRun();
                scoreRun.setText("分数：" + topic.getTopicScore());
                scoreRun.setFontSize(FONT_SIZE);
                ;
            }

            // 如果有选项，添加选项信息
            List<PaperTopicItem> itemList = topic.getItemList();
            if (itemList != null && !itemList.isEmpty()) {
                for (int j = 0; j < itemList.size(); j++) {
                    PaperTopicItem item = itemList.get(j);

                    // 创建选项段落
                    XWPFParagraph itemParagraph = document.createParagraph();
                    itemParagraph.setIndentationLeft(500); // 缩进
                    itemParagraph.setAlignment(ParagraphAlignment.LEFT);
                    XWPFRun itemRun = itemParagraph.createRun();

                    // 使用字母作为选项标识
                    char optionChar = (char) ('A' + j);
                    itemRun.setText(optionChar + ". " + item.getContent());
                    itemRun.setFontSize(FONT_SIZE);
                    ;

                    // 如果有分数，添加分数信息
                    if (item.getItemScore() != null) {
                        itemRun.setText(" (" + item.getItemScore() + "分)");
                    }
                }
            }

            // 如果有备注，添加备注信息
            if (topic.getRemark() != null && !topic.getRemark().isEmpty()) {
                XWPFParagraph remarkParagraph = document.createParagraph();
                remarkParagraph.setAlignment(ParagraphAlignment.LEFT);
                XWPFRun remarkRun = remarkParagraph.createRun();
                remarkRun.setText("备注：" + topic.getRemark());
                remarkRun.setItalic(true);
                remarkRun.setFontSize(FONT_SIZE);
                ;
            }

            // 添加空行
            XWPFParagraph emptyParagraph = document.createParagraph();
            emptyParagraph.createRun().addBreak();
        }

        // 使用统一的方法设置文件下载头信息
        setFileDownloadHeader(response, fileName, "docx", true);

        // 写入响应流
        document.write(response.getOutputStream());
        document.close();
    }

    /**
     * 从Word模板填充内容并导出
     *
     * @param response     HTTP响应对象
     * @param fileName     导出文件名
     * @param templatePath 模板文件路径
     * @param paperTitle   试卷标题
     * @param topicList    题目列表
     * @throws IOException IO异常
     */
    public static void exportFromTemplate(HttpServletResponse response, String fileName, String templatePath, String paperTitle, List<PaperTopic> topicList) throws IOException {
        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 替换文档中的占位符
            replaceText(document, "${TITLE}", paperTitle);

            // 构建题目内容
            StringBuilder questionsContent = new StringBuilder();
            for (int i = 0; i < topicList.size(); i++) {
                PaperTopic topic = topicList.get(i);

                // 添加题目
                questionsContent.append(i + 1).append(". ").append(topic.getTopicName()).append("\n");

                // 添加指标（如果有）
                if (topic.getIndexName() != null && !topic.getIndexName().isEmpty()) {
                    questionsContent.append("指标：").append(topic.getIndexName()).append("\n");
                }

                // 添加分数（如果有）
                if (topic.getTopicScore() != null) {
                    questionsContent.append("分数：").append(topic.getTopicScore()).append("\n");
                }

                // 添加选项（如果有）
                List<PaperTopicItem> itemList = topic.getItemList();
                if (itemList != null && !itemList.isEmpty()) {
                    for (int j = 0; j < itemList.size(); j++) {
                        PaperTopicItem item = itemList.get(j);
                        char optionChar = (char) ('A' + j);
                        questionsContent.append("    ").append(optionChar).append(". ").append(item.getContent());

                        // 添加选项分数（如果有）
                        if (item.getItemScore() != null) {
                            questionsContent.append(" (").append(item.getItemScore()).append("分)");
                        }
                        questionsContent.append("\n");
                    }
                }

                // 添加备注（如果有）
                if (topic.getRemark() != null && !topic.getRemark().isEmpty()) {
                    questionsContent.append("备注：").append(topic.getRemark()).append("\n");
                }

                // 添加空行
                questionsContent.append("\n");
            }

            // 替换题目内容占位符
            replaceText(document, "${QUESTIONS}", questionsContent.toString());

            // 使用统一的方法设置文件下载头信息
            setFileDownloadHeader(response, fileName, "docx", true);

            // 写入响应流
            writeDocumentToResponse(document, response);
        }
    }


    /**
     * 将问答题填充到Word模板中并保存到文件
     *
     * @param outputFilePath     输出文件路径
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)和备注(remark)字段
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersToTemplate(String outputFilePath,
                                                            String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList) throws IOException {
        logger.debug("开始将问答题填充到Word模板并保存到文件，模板文件：{}，输出文件：{}", templatePath, outputFilePath);

        // 确保输出目录存在
        File outputFile = new File(outputFilePath);
        ensureDirectoryExists(outputFile);

        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 替换文档中的标题占位符
            replaceText(document, "${TITLE}", paperTitle);

            // 查找内容占位符所在的段落
            XWPFParagraph contentParagraph = findPlaceholderParagraph(document, "${CONTENT}");

            // 如果没有找到占位符，则创建新段落
            if (contentParagraph == null) {
                logger.warn("在模板中未找到${CONTENT}占位符，将创建新段落");
                contentParagraph = document.createParagraph();
            }

            // 在占位符段落中创建所有问题
            if (!questionAnswerList.isEmpty()) {
                // 生成问答内容
                String content = generateQuestionAnswerContentWithAnswers(questionAnswerList, 3);

                // 将内容添加到段落中
                addQuestionAnswerContentToParagraph(document, contentParagraph, content);
            }

            // 清理文档中所有未替换的占位符
            clearRemainingPlaceholders(document);

            // 将文档保存到文件
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                document.write(fos);
            }

            logger.debug("问答题填充到Word模板并保存到文件完成");
        }
    }

    /**
     * 将问答题填充到Word模板中并导出
     *
     * @param response           HTTP响应对象
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)和备注(remark)字段
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersToTemplate(HttpServletResponse response, String fileName,
                                                            String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList) throws IOException {
        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 替换文档中的标题占位符
            replaceText(document, "${TITLE}", paperTitle);

            // 查找内容占位符所在的段落
            XWPFParagraph contentParagraph = findPlaceholderParagraph(document, "${CONTENT}");

            // 如果没有找到占位符，则创建新段落
            if (contentParagraph == null) {
                logger.warn("在模板中未找到${CONTENT}占位符，将创建新段落");
                contentParagraph = document.createParagraph();
            }

            // 在占位符段落中创建所有问题
            if (!questionAnswerList.isEmpty()) {
                // 生成问答内容
                String content = generateQuestionAnswerContentWithAnswers(questionAnswerList, 3);

                // 将内容添加到段落中
                addQuestionAnswerContentToParagraph(document, contentParagraph, content);
            }

            // 清理文档中所有未替换的占位符
            clearRemainingPlaceholders(document);

            // 使用统一的方法设置文件下载头信息
            setFileDownloadHeader(response, fileName, "docx", true);

            // 写入响应流
            writeDocumentToResponse(document, response);
        }
    }


    /**
     * 根据任意对象的字段填充Word模板中的占位符
     * 占位符格式为${字段名}，例如${name}、${age}等
     * 支持嵌套对象，使用点号分隔，例如${user.name}、${user.address.city}等
     * 支持集合/数组类型，使用索引访问，例如${users[0].name}、${addresses[1].city}等
     *
     * @param templatePath 模板文件路径
     * @param outputPath   输出文件路径
     * @param dataObject   数据对象，可以是任意类型的对象
     * @throws IOException IO异常
     */
    public static void fillTemplateWithObject(String templatePath, String outputPath, Object dataObject) throws IOException {
        logger.debug("开始根据对象填充Word模板，模板文件：{}，输出文件：{}", templatePath, outputPath);

        // 确保输出目录存在
        File outputFile = new File(outputPath);
        ensureDirectoryExists(outputFile);

        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 使用反射获取对象的所有字段和值
            Map<String, Object> fieldMap = extractFieldsFromObject(dataObject, "");

            // 替换文档中的所有占位符
            for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                Object value = entry.getValue();
                String valueStr = (value != null) ? value.toString() : "";

                replaceText(document, placeholder, valueStr);
            }

            // 将文档保存到文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                document.write(fos);
            }

            logger.debug("Word模板填充完成，已保存到：{}", outputPath);
        }
    }

    /**
     * 根据任意对象的字段填充Word模板中的占位符并导出到HTTP响应
     * 占位符格式为${字段名}，例如${name}、${age}等
     *
     * @param response     HTTP响应对象
     * @param fileName     导出文件名（不含扩展名）
     * @param templatePath 模板文件路径
     * @param dataObject   数据对象，可以是任意类型的对象
     * @throws IOException IO异常
     */
    public static void fillTemplateWithObject(HttpServletResponse response, String fileName,
                                              String templatePath, Object dataObject) throws IOException {
        logger.debug("开始根据对象填充Word模板并导出，模板文件：{}", templatePath);

        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempFile = File.createTempFile("word_", ".docx", tempDir);

        try {
            // 先填充模板并保存到临时文件
            fillTemplateWithObject(templatePath, tempFile.getAbsolutePath(), dataObject);

            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "docx", true);

            // 将文件写入响应流
            try (FileInputStream fis = new FileInputStream(tempFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    response.getOutputStream().write(buffer, 0, bytesRead);
                }
            }

            logger.debug("Word文档已成功生成并输出到响应流");
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 根据任意对象的字段填充Word模板中的占位符并转换为PDF导出
     * 使用默认字体
     *
     * @param response     HTTP响应对象
     * @param fileName     导出文件名（不含扩展名）
     * @param templatePath 模板文件路径
     * @param dataObject   数据对象，可以是任意类型的对象
     * @throws IOException IO异常
     */
    public static void fillTemplateWithObjectAsPdf(HttpServletResponse response, String fileName,
                                                   String templatePath, Object dataObject) throws IOException {
        // 调用带字体类型参数的方法，使用默认字体
        fillTemplateWithObjectAsPdf(response, fileName, templatePath, dataObject, FontType.DEFAULT);
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后转换为PDF导出
     * 使用默认字体
     *
     * @param response           HTTP响应对象
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表
     * @param dataObject         要填充的数据对象
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectAsPdf(HttpServletResponse response, String fileName,
                                                                String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList, Object dataObject) throws IOException {
        // 调用带字体类型参数的方法，使用默认字体
        exportQuestionsWithAnswersAndObjectAsPdf(response, fileName, templatePath, paperTitle, questionAnswerList, dataObject, FontType.DEFAULT);
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后转换为PDF导出（使用精确替换方法）
     *
     * @param response           HTTP响应对象
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表
     * @param dataObject         要填充的数据对象
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectAsPdfExact(HttpServletResponse response, String fileName,
                                                                     String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList, Object dataObject) throws IOException {
        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempWordFile = File.createTempFile("word_", ".docx", tempDir);
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 先填充模板并保存到临时Word文件（使用精确替换方法）
            exportQuestionsWithAnswersAndObjectToTemplateExact(tempWordFile.getAbsolutePath(), templatePath, paperTitle, questionAnswerList, dataObject);

            // 将Word转换为PDF
            convertWordToPdf(tempWordFile.getAbsolutePath(), tempPdfFile.getAbsolutePath());

            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);

            logger.debug("PDF文档已成功生成并输出到响应流（使用精确替换方法）");
        } finally {
            // 清理临时文件
            deleteFileIfExists(tempWordFile);
            deleteFileIfExists(tempPdfFile);
        }
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后转换为PDF导出（使用精确替换方法和指定字体）
     *
     * @param response           HTTP响应对象
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表
     * @param dataObject         要填充的数据对象
     * @param fontType           要使用的字体类型
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectAsPdfExact(HttpServletResponse response, String fileName,
                                                                     String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList,
                                                                     Object dataObject, FontType fontType) throws IOException {
        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempWordFile = File.createTempFile("word_", ".docx", tempDir);
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 先填充模板并保存到临时Word文件（使用精确替换方法）
            exportQuestionsWithAnswersAndObjectToTemplateExact(tempWordFile.getAbsolutePath(), templatePath, paperTitle, questionAnswerList, dataObject);

            // 将Word转换为PDF，使用指定字体
            convertWordToPdfWithFont(tempWordFile.getAbsolutePath(), tempPdfFile.getAbsolutePath(), fontType);

            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);

            logger.debug("PDF文档已成功生成并输出到响应流（使用精确替换方法和{}字体）", fontType);
        } finally {
            // 清理临时文件
            deleteFileIfExists(tempWordFile);
            deleteFileIfExists(tempPdfFile);
        }
    }

    /**
     * 根据任意对象的字段填充Word模板中的占位符并转换为PDF导出
     *
     * @param response     HTTP响应对象
     * @param fileName     导出文件名（不含扩展名）
     * @param templatePath 模板文件路径
     * @param dataObject   数据对象，可以是任意类型的对象
     * @param fontType     指定使用的字体类型
     * @throws IOException IO异常
     */
    public static void fillTemplateWithObjectAsPdf(HttpServletResponse response, String fileName,
                                                   String templatePath, Object dataObject, FontType fontType) throws IOException {
        logger.debug("开始根据对象填充Word模板并转换为PDF导出（使用{}字体），模板文件：{}", fontType, templatePath);

        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempWordFile = File.createTempFile("word_", ".docx", tempDir);
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 先填充模板并保存到临时Word文件
            fillTemplateWithObject(templatePath, tempWordFile.getAbsolutePath(), dataObject);

            // 将Word转换为PDF，使用指定字体
            convertWordToPdfWithFont(tempWordFile.getAbsolutePath(), tempPdfFile.getAbsolutePath(), fontType);

            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);

            logger.debug("PDF文档已成功生成并输出到响应流（使用{}字体）", fontType);
        } finally {
            // 清理临时文件
            deleteFileIfExists(tempWordFile);
            deleteFileIfExists(tempPdfFile);
        }
    }

    /**
     * 递归提取对象中的所有字段和值
     *
     * @param obj    要提取字段的对象
     * @param prefix 字段名前缀，用于处理嵌套对象
     * @return 包含所有字段和值的Map
     */
    private static Map<String, Object> extractFieldsFromObject(Object obj, String prefix) {
        Map<String, Object> fieldMap = new HashMap<>();

        if (obj == null) {
            return fieldMap;
        }

        // 处理基本类型、字符串和日期类型
        if (obj instanceof String || obj instanceof Number || obj instanceof Boolean ||
                obj instanceof Date || obj instanceof Character) {
            if (!prefix.isEmpty()) {
                fieldMap.put(prefix, obj);
            }
            return fieldMap;
        }

        // 处理Map类型
        if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                String key = entry.getKey().toString();
                String fieldName = prefix.isEmpty() ? key : prefix + "." + key;

                Object value = entry.getValue();
                if (isSimpleValue(value)) {
                    fieldMap.put(fieldName, value);
                } else {
                    fieldMap.putAll(extractFieldsFromObject(value, fieldName));
                }
            }
            return fieldMap;
        }

        // 处理集合和数组类型
        if (obj instanceof Collection || obj.getClass().isArray()) {
            Collection<?> collection;
            if (obj.getClass().isArray()) {
                collection = Arrays.asList((Object[]) obj);
            } else {
                collection = (Collection<?>) obj;
            }

            int index = 0;
            for (Object item : collection) {
                String fieldName = prefix.isEmpty() ? "[" + index + "]" : prefix + "[" + index + "]";

                if (isSimpleValue(item)) {
                    fieldMap.put(fieldName, item);
                } else {
                    fieldMap.putAll(extractFieldsFromObject(item, fieldName));
                }

                index++;
            }
            return fieldMap;
        }

        // 处理普通Java对象，使用反射获取字段
        Class<?> clazz = obj.getClass();
        for (Field field : getAllFields(clazz)) {
            field.setAccessible(true);
            String fieldName = prefix.isEmpty() ? field.getName() : prefix + "." + field.getName();

            try {
                Object value = field.get(obj);
                if (value == null) {
                    fieldMap.put(fieldName, "");
                } else if (isSimpleValue(value)) {
                    fieldMap.put(fieldName, value);
                } else {
                    fieldMap.putAll(extractFieldsFromObject(value, fieldName));
                }
            } catch (IllegalAccessException e) {
                logger.warn("无法访问字段：{}，异常信息：{}", fieldName, e.getMessage());
            }
        }

        return fieldMap;
    }

    /**
     * 获取类的所有字段，包括父类的字段
     *
     * @param clazz 类
     * @return 所有字段的列表
     */
    private static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            fields.addAll(Arrays.asList(currentClass.getDeclaredFields()));
            currentClass = currentClass.getSuperclass();
        }

        return fields;
    }

    /**
     * 判断一个值是否为简单值类型（基本类型、字符串、日期等）
     *
     * @param value 要判断的值
     * @return 如果是简单值类型则返回true，否则返回false
     */
    private static boolean isSimpleValue(Object value) {
        return value == null ||
                value instanceof String ||
                value instanceof Number ||
                value instanceof Boolean ||
                value instanceof Date ||
                value instanceof Character ||
                value.getClass().isPrimitive();
    }

    /**
     * 确保目录存在，如果不存在则创建
     *
     * @param file 文件对象
     */
    public static void ensureDirectoryExists(File file) {
        if (file.getParentFile() != null && !file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
    }

    /**
     * 替换Word文档中的文本
     *
     * @param document Word文档对象
     * @param oldText  要替换的文本
     * @param newText  替换后的文本
     */
    public static void replaceText(XWPFDocument document, String oldText, String newText) {
        // 替换段落中的文本
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            for (XWPFRun run : paragraph.getRuns()) {
                String text = run.getText(0);
                if (text != null && text.contains(oldText)) {
                    String pattern = Pattern.quote(oldText);
                    // 使用正则表达式替换完全匹配的文本
                    run.setText(text.replaceAll(pattern, newText), 0);
                }
            }
        }

        // 替换表格中的文本
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        for (XWPFRun run : paragraph.getRuns()) {
                            String text = run.getText(0);
                            if (text != null && text.contains(oldText)) {
                                String pattern = Pattern.quote(oldText);
                                // 使用正则表达式替换完全匹配的文本
                                run.setText(text.replaceAll(pattern, newText), 0);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置文件下载的响应头信息，处理文件名编码问题
     * 支持中文文件名，兼容各种浏览器和Postman
     *
     * @param response       HTTP响应对象
     * @param fileName       文件名（不含扩展名）
     * @param fileType       文件类型，例如 "docx" 或 "pdf"
     * @param addCorsHeaders 是否添加CORS头信息（用于Postman等跨域请求）
     */
    public static void setFileDownloadHeader(HttpServletResponse response, String fileName, String fileType, boolean addCorsHeaders) {
        String fullFileName = fileName + "." + fileType;
        try {
            String encodedFileName = URLEncoder.encode(fullFileName, CharsetUtil.UTF_8);
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");

            // 设置响应内容类型
            if ("pdf".equalsIgnoreCase(fileType)) {
                response.setContentType("application/pdf;charset=UTF-8");
            } else {
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");
            }

            // 添加额外的CORS头信息以支持Postman等跨域请求
            if (addCorsHeaders) {
                response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
                response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            }

            logger.debug("设置文件下载头信息成功，文件名：{}", fullFileName);

        } catch (Exception e) {
            logger.error("文件名编码异常", e);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fullFileName + "\"");
        }
    }

    /**
     * 将Word文档转换为PDF
     * 注意：此方法需要添加相关依赖才能使用
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  输出PDF文件路径
     * @throws IOException IO异常
     */
    public static void convertWordToPdf(String wordFilePath, String pdfFilePath) throws IOException {
        // 调用指定字体类型的方法，使用默认字体
        convertWordToPdfWithFont(wordFilePath, pdfFilePath, FontType.DEFAULT);
    }

    /**
     * 将Word文档转换为PDF，并支持自定义行距
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  PDF输出文件路径
     * @param lineSpacing  行距倍数，例如1.5表示1.5倍行距
     * @throws IOException IO异常
     */
    public static void convertWordToPdfWithLineSpacing(String wordFilePath, String pdfFilePath, float lineSpacing) throws IOException {
        logger.debug("开始转换Word文档到PDF(自定义行距)，源文件：{}，目标文件：{}，行距：{}", wordFilePath, pdfFilePath, lineSpacing);

        // 确保输出目录存在
        File pdfFile = new File(pdfFilePath);
        ensureDirectoryExists(pdfFile);

        // 使用POI-XWPF-Converter-PDF进行转换
        try (FileInputStream fis = new FileInputStream(wordFilePath);
             FileOutputStream fos = new FileOutputStream(pdfFilePath)) {

            // 加载Word文档
            XWPFDocument document = new XWPFDocument(fis);

            // 调整文档中所有段落的行距
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                // 修改为固定值30磅的行距
                paragraph.setSpacingLineRule(LineSpacingRule.EXACT);
                paragraph.setSpacingBetween(30, LineSpacingRule.EXACT);
            }

            // 调整表格中所有段落的行距
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            // 修改为固定值30磅的行距
                            paragraph.setSpacingLineRule(LineSpacingRule.EXACT);
                            paragraph.setSpacingBetween(30, LineSpacingRule.EXACT);
                        }
                    }
                }
            }

            // 创建PDF选项并配置字体
            PdfOptions options = PdfOptions.create();

            // 配置字体提供器，解决中文显示问题
            options.fontProvider(new IFontProvider() {
                @Override
                public Font getFont(String familyName, String encoding, float size, int style, Color color) {
                    return createPdfFont(familyName, encoding, size, style, color);
                }
            });

            // 将Word转换为PDF
            PdfConverter.getInstance().convert(document, fos, options);

            // 关闭文档
            document.close();

            logger.debug("Word转PDF转换(自定义行距)完成");
        } catch (Exception e) {
            logger.error("Word转PDF转换(自定义行距)失败", e);
            throw new IOException("Word转PDF转换(自定义行距)失败: " + e.getMessage(), e);
        }
    }


    /**
     * 创建PDF字体
     *
     * @param familyName 字体族名称
     * @param encoding   编码
     * @param size       字体大小
     * @param style      字体样式
     * @param color      字体颜色
     * @return 字体对象
     */
    private static Font createPdfFont(String familyName, String encoding, float size, int style, Color color) {
        try {
            // 根据字体族名称选择合适的字体
            FontType fontType = getFontTypeByFamilyName(familyName);
            BaseFont baseFont = getBaseFontByType(fontType);

            return new Font(baseFont, size, style, color);
        } catch (Exception e) {
            logger.error("配置PDF字体失败", e);
            return null;
        }
    }

    /**
     * 根据字体族名称确定字体类型
     *
     * @param familyName 字体族名称
     * @return 字体类型
     */
    private static FontType getFontTypeByFamilyName(String familyName) {
        if (familyName == null) {
            return FontType.DEFAULT; // 默认使用仿宋
        }

        familyName = familyName.toLowerCase();

        // 识别宋体
        if (familyName.contains("song") || familyName.contains("宋体") || familyName.contains("simsun")) {
            return FontType.SONG;
        }

        // 识别仿宋
        if (familyName.contains("fangsong") || familyName.contains("仿宋") || familyName.contains("fangsong")) {
            return FontType.DEFAULT;
        }

        // 其他字体均使用仿宋作为默认字体
        return FontType.DEFAULT;
    }

    /**
     * 根据字体类型获取BaseFont对象
     *
     * @param fontType 字体类型
     * @return BaseFont对象
     * @throws Exception 如果加载字体失败
     */
    private static BaseFont getBaseFontByType(FontType fontType) throws Exception {
        switch (fontType) {
            case SONG: // 宋体
                if (FONT_SONG_PATH != null && !FONT_SONG_PATH.isEmpty()) {
                    try {
                        return BaseFont.createFont(FONT_SONG_PATH, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                    } catch (Exception e) {
                        logger.warn("加载宋体字体失败: {}", e.getMessage());
                    }
                }
                break;
            case DEFAULT: // 仿宋（默认字体）
            default:
                if (FONT_PATH != null && !FONT_PATH.isEmpty()) {
                    try {
                        return BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                    } catch (Exception e) {
                        logger.warn("加载仿宋字体失败: {}", e.getMessage());
                    }
                }
                break;
        }

        // 备选方案1：尝试使用内置中文字体
        try {
            return BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            logger.warn("加载内置中文字体失败: {}", e.getMessage());
        }

        // 备选方案2：最后的备选方案
        return BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
    }

    /**
     * 将Word文档转换为PDF，使用自定义的中文标点符号处理
     * 解决中文标点符号在换行时位置错误的问题
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  PDF输出文件路径
     * @param fontType     字体类型
     * @throws IOException IO异常
     */
    public static void convertWordToPdfWithChinesePunctuation(String wordFilePath, String pdfFilePath, FontType fontType) throws IOException {
        logger.debug("开始转换Word文档到PDF(中文标点符号优化)，源文件：{}，目标文件：{}，字体：{}", wordFilePath, pdfFilePath, fontType);

        // 确保输出目录存在
        File pdfFile = new File(pdfFilePath);
        ensureDirectoryExists(pdfFile);

        // 使用POI-XWPF-Converter-PDF进行转换
        try (FileInputStream fis = new FileInputStream(wordFilePath);
             FileOutputStream fos = new FileOutputStream(pdfFilePath)) {

            // 加载Word文档
            XWPFDocument document = new XWPFDocument(fis);

            // 创建PDF选项并配置字体和中文标点符号处理
            PdfOptions options = PdfOptions.create();

            // 配置字体提供器，指定特定字体
            options.fontProvider(new IFontProvider() {
                @Override
                public Font getFont(String familyName, String encoding, float size, int style, Color color) {
                    try {
                        // 优先使用指定的字体类型
                        BaseFont baseFont = getBaseFontByType(fontType);
                        return new Font(baseFont, size, style, color);
                    } catch (Exception e) {
                        logger.error("使用指定字体失败，将使用默认字体逻辑: {}", e.getMessage());
                        // 如果指定字体失败，回退到默认字体选择逻辑
                        return createPdfFont(familyName, encoding, size, style, color);
                    }
                }
            });

            // 将Word转换为PDF
            PdfConverter.getInstance().convert(document, fos, options);

            // 关闭文档
            document.close();

            logger.debug("Word转PDF转换(中文标点符号优化)完成");
        } catch (Exception e) {
            logger.error("Word转PDF转换(中文标点符号优化)失败", e);
            throw new IOException("Word转PDF转换(中文标点符号优化)失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用纯iText 5创建PDF，专门处理中文标点符号换行问题
     * 这个方法直接从Word文档内容创建PDF，可以更好地控制中文标点符号的处理
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  PDF输出文件路径
     * @param fontType     字体类型
     * @throws IOException IO异常
     */
    public static void convertWordToPdfWithPureIText(String wordFilePath, String pdfFilePath, FontType fontType) throws IOException {
        logger.debug("开始使用纯iText转换Word文档到PDF(中文标点符号优化)，源文件：{}，目标文件：{}，字体：{}", wordFilePath, pdfFilePath, fontType);

        // 确保输出目录存在
        File pdfFile = new File(pdfFilePath);
        ensureDirectoryExists(pdfFile);

        try (FileInputStream fis = new FileInputStream(wordFilePath);
             FileOutputStream fos = new FileOutputStream(pdfFilePath)) {

            // 加载Word文档
            XWPFDocument document = new XWPFDocument(fis);

            // 创建PDF文档
            com.lowagie.text.Document pdfDocument = new com.lowagie.text.Document(com.lowagie.text.PageSize.A4);
            com.lowagie.text.pdf.PdfWriter writer = com.lowagie.text.pdf.PdfWriter.getInstance(pdfDocument, fos);

            pdfDocument.open();

            // 获取字体
            BaseFont baseFont = getBaseFontByType(fontType);
            com.lowagie.text.Font font = new com.lowagie.text.Font(baseFont, 12f, com.lowagie.text.Font.NORMAL, java.awt.Color.BLACK);

            // 处理Word文档中的段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                String text = paragraph.getText();
                if (text != null && !text.trim().isEmpty()) {
                    // 预处理文本，优化中文标点符号
                    String processedText = preprocessChineseText(text);

                    com.lowagie.text.Paragraph pdfParagraph = new com.lowagie.text.Paragraph(processedText, font);
                    pdfParagraph.setAlignment(com.lowagie.text.Element.ALIGN_LEFT);
                    pdfParagraph.setSpacingAfter(6f);
                    pdfDocument.add(pdfParagraph);
                }
            }

            // 处理表格
            for (XWPFTable table : document.getTables()) {
                com.lowagie.text.pdf.PdfPTable pdfTable = new com.lowagie.text.pdf.PdfPTable(table.getRow(0).getTableCells().size());
                pdfTable.setWidthPercentage(100);

                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        String cellText = cell.getText();
                        if (cellText != null) {
                            // 预处理文本，优化中文标点符号
                            String processedText = preprocessChineseText(cellText);

                            com.lowagie.text.pdf.PdfPCell pdfCell = new com.lowagie.text.pdf.PdfPCell(new com.lowagie.text.Phrase(processedText, font));
                            pdfCell.setPadding(4f);
                            pdfTable.addCell(pdfCell);
                        } else {
                            pdfTable.addCell("");
                        }
                    }
                }
                pdfDocument.add(pdfTable);
                pdfDocument.add(new com.lowagie.text.Paragraph(" ", font)); // 添加空行
            }

            pdfDocument.close();
            document.close();

            logger.debug("使用纯iText转换Word文档到PDF(中文标点符号优化)完成");
        } catch (Exception e) {
            logger.error("使用纯iText转换Word文档到PDF(中文标点符号优化)失败", e);
            throw new IOException("使用纯iText转换Word文档到PDF(中文标点符号优化)失败: " + e.getMessage(), e);
        }
    }

    /**
     * 预处理中文文本，优化标点符号的显示
     *
     * @param text 原始文本
     * @return 处理后的文本
     */
    private static String preprocessChineseText(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 在中文标点符号前添加不换行空格，防止标点符号出现在行首
        // 处理常见的中文标点符号
        text = text.replaceAll("([\\u3000-\\u303F\\uFF00-\\uFFEF])", "\u00A0$1");
        text = text.replaceAll("([，。；：？！])", "\u00A0$1");
        text = text.replaceAll("([（）【】《》、…—·])", "\u00A0$1");

        return text;
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据
     *
     * @param outputFilePath     输出文件路径
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)和备注(remark)字段
     * @param dataObject         要填充的数据对象
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectToTemplate(String outputFilePath,
                                                                     String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList, Object dataObject) throws IOException {
        logger.debug("开始将问答题和对象数据填充到Word模板并保存到文件，模板文件：{}，输出文件：{}", templatePath, outputFilePath);

        // 确保输出目录存在
        File outputFile = new File(outputFilePath);
        ensureDirectoryExists(outputFile);

        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 替换文档中的标题占位符
            replaceText(document, "${TITLE}", paperTitle);

            // 查找内容占位符所在的段落
            XWPFParagraph contentParagraph = findPlaceholderParagraph(document, "${CONTENT}");

            // 如果没有找到占位符，则创建新段落
            if (contentParagraph == null) {
                logger.warn("在模板中未找到${CONTENT}占位符，将创建新段落");
                contentParagraph = document.createParagraph();
            }

            // 在占位符段落中创建所有问题
            if (!questionAnswerList.isEmpty()) {
                // 生成问答内容，并处理答案和选项勾选
                String content = generateQuestionAnswerContentWithAnswers(questionAnswerList, 3);

                // 将内容添加到段落中
                addQuestionAnswerContentToParagraph(document, contentParagraph, content);
            }

            // 使用反射获取对象的所有字段和值
            if (dataObject != null) {
                Map<String, Object> fieldMap = extractFieldsFromObject(dataObject, "");

                // 替换文档中的所有占位符
                for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
                    String placeholder = "${" + entry.getKey() + "}";
                    Object value = entry.getValue();
                    String valueStr = (value != null) ? value.toString() : "";

                    replaceText(document, placeholder, valueStr);
                }

                //单独处理调查人数组
                String placeHolder = "${investigators}";
                if (dataObject instanceof InvestigationRecordParam) {
                    InvestigationRecordParam param = (InvestigationRecordParam) dataObject;
                    String investigators = formatInvestigators(param);
                    replaceTextWithUnderline(document, placeHolder, investigators);
                }
            }

            // 清理文档中所有未替换的占位符
            clearRemainingPlaceholders(document);

            // 将文档保存到文件
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                document.write(fos);
            }

            logger.debug("问答题和对象数据填充到Word模板并保存到文件完成");
        }
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后导出到HTTP响应
     *
     * @param response           HTTP响应对象
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)和备注(remark)字段
     * @param dataObject         要填充的数据对象
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectToTemplate(HttpServletResponse response, String fileName,
                                                                     String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList, Object dataObject) throws IOException {
        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempFile = File.createTempFile("word_", ".docx", tempDir);

        try {
            // 先填充模板并保存到临时文件
            exportQuestionsWithAnswersAndObjectToTemplate(tempFile.getAbsolutePath(), templatePath, paperTitle, questionAnswerList, dataObject);

            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "docx", true);

            // 将文件写入响应流
            try (FileInputStream fis = new FileInputStream(tempFile)) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    response.getOutputStream().write(buffer, 0, bytesRead);
                }
            }

            logger.debug("Word文档已成功生成并输出到响应流");
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后转换为PDF导出
     *
     * @param response           HTTP响应对象
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)和备注(remark)字段
     * @param dataObject         要填充的数据对象
     * @param fontType           指定使用的字体类型
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectAsPdf(HttpServletResponse response, String fileName,
                                                                String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList,
                                                                Object dataObject, FontType fontType) throws IOException {
        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempWordFile = File.createTempFile("word_", ".docx", tempDir);
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 先填充模板并保存到临时Word文件
            exportQuestionsWithAnswersAndObjectToTemplate(tempWordFile.getAbsolutePath(), templatePath, paperTitle, questionAnswerList, dataObject);

            // 将Word转换为PDF，使用指定字体
            convertWordToPdfWithFont(tempWordFile.getAbsolutePath(), tempPdfFile.getAbsolutePath(), fontType);

            // 设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);

            logger.debug("PDF文档已成功生成并输出到响应流（使用{}字体）", fontType);
        } finally {
            // 清理临时文件
            deleteFileIfExists(tempWordFile);
            deleteFileIfExists(tempPdfFile);
        }
    }

    /**
     * 将Word文档转换为PDF并输出到HTTP响应
     * 注意：此方法需要添加相关依赖才能使用
     *
     * @param response     HTTP响应对象
     * @param fileName     输出文件名（不含扩展名）
     * @param wordFilePath Word文件路径
     * @throws IOException IO异常
     */
    public static void convertWordToPdfAndExport(HttpServletResponse response, String fileName, String wordFilePath) throws IOException {
        logger.debug("开始转换Word文档到PDF并输出到HTTP响应，源文件：{}", wordFilePath);

        // 创建临时文件保存PDF
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempPdfFile = new File(tempDir, fileName + ".pdf");

        try {
            // 调用转换方法
            convertWordToPdf(wordFilePath, tempPdfFile.getAbsolutePath());

            // 使用统一的方法设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);
        } finally {
            // 删除临时文件
            deleteFileIfExists(tempPdfFile);
        }

        logger.debug("转换并输出完成");
    }

    /**
     * 将文件写入HTTP响应流
     *
     * @param file     要写入的文件
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    public static void writeFileToResponse(File file, HttpServletResponse response) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
        }
    }

    /**
     * 如果文件存在则删除
     *
     * @param file 要删除的文件
     */
    public static void deleteFileIfExists(File file) {
        if (file != null && file.exists()) {
            file.delete();
        }
    }


    /**
     * 根据问答列表生成内容，支持答案填充和选项勾选
     *
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)、选中项(selectedOptions)、用户回答(userAnswer)、用户选择ID(userSelectId)和备注(remark)字段
     * @param startIndex         问题编号起始值
     * @return 格式化后的内容
     */
    public static String generateQuestionAnswerContentWithAnswers(List<Map<String, Object>> questionAnswerList, int startIndex) {
        StringBuilder allContent = new StringBuilder();

        // 处理所有问题
        for (int i = 0; i < questionAnswerList.size(); i++) {
            Map<String, Object> qaItem = questionAnswerList.get(i);
            String question = (String) qaItem.get("question");
            String answer = (String) qaItem.get("answer");
            String remark = (String) qaItem.get("remark");
            String userAnswer = (String) qaItem.get("userAnswer"); // 用户的回答
            String userSelectId = (String) qaItem.get("userSelectId"); // 用户选择的选项ID
            @SuppressWarnings("unchecked")
            List<String> options = (List<String>) qaItem.get("options");
            @SuppressWarnings("unchecked")
            List<String> optionIds = (List<String>) qaItem.get("optionIds"); // 选项ID列表
            @SuppressWarnings("unchecked")
            List<Integer> selectedOptions = (List<Integer>) qaItem.get("selectedOptions"); // 选中的选项索引列表
            // 检查是否需要减少空行
            Boolean reduceSpace = (Boolean) qaItem.get("reduceSpace");

            // 添加问题
            if (i > 0) {
                // 根据reduceSpace参数控制空行数量
                if (reduceSpace != null && reduceSpace) {
                    // 当reduceSpace为true时，不添加任何空行
                    // allContent.append("\n"); // 注释掉这行，不添加任何空行
                } else {
                    allContent.append("\n\n"); // 默认添加两个空行
                }
            }
            allContent.append(i + startIndex).append(".问：").append(question).append("\n");

            // 添加答案
            allContent.append("  答：");
            // 优先使用用户的回答，如果没有再使用默认答案
            if (userAnswer != null && !userAnswer.isEmpty()) {
                // 对于用户输入的答案，添加答案内容，并在后面处理时添加下划线
                allContent.append("[ANSWER_WITH_CONTENT]").append(userAnswer);
            } else if (answer != null && !answer.isEmpty()) {
                // 如果没有用户答案但有默认答案
                allContent.append("[ANSWER_WITH_CONTENT]").append(answer);
            } else {
                // 不使用下划线字符，而是在后面使用XWPFRun的方式添加下划线
                allContent.append("[PLACEHOLDER_FOR_UNDERLINE]");
            }
            allContent.append("\n");

            // 添加选项（如果有）
            if (options != null && !options.isEmpty()) {
                allContent.append("  选项：");
                for (int j = 0; j < options.size(); j++) {
                    // 检查该选项是否被选中
                    boolean isSelected = false;

                    // 首先检查用户选择
                    if (userSelectId != null && !userSelectId.isEmpty()) {
                        // 直接比较选项内容而不是ID
                        String currentOption = options.get(j);
                        if (currentOption != null && userSelectId.equals(currentOption)) {
                            isSelected = true;
                        }
                    }
                    // 如果没有用户选择，再检查默认选中项
                    else if (selectedOptions != null) {
                        isSelected = selectedOptions.equals(j);
                    }

                    // 如果选项被选中，添加勾选标记，不再添加字母标识
                    if (j == 0) {
                        if (isSelected) {
                            allContent.append("[CHECKBOX_CHECKED] ").append(options.get(j)).append("  ");
                        } else {
                            allContent.append("[CHECKBOX_UNCHECKED] ").append(options.get(j)).append("  ");
                        }
                    } else {
                        if (isSelected) {
                            allContent.append("[CHECKBOX_CHECKED] ").append(options.get(j)).append("  ");
                        } else {
                            allContent.append("[CHECKBOX_UNCHECKED] ").append(options.get(j)).append("  ");
                        }
                    }
                }
            }

            // 添加备注（如果有）
            if (remark != null && !remark.isEmpty()) {
                allContent.append("\n").append("  备注：").append(remark).append("\n");
            }
        }

        return allContent.toString();
    }

    /**
     * 清理文档中所有未替换的占位符
     *
     * @param document Word文档对象
     */
    public static void clearRemainingPlaceholders(XWPFDocument document) {
        logger.debug("开始清理文档中未替换的占位符");

        // 遍历所有段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String paragraphText = paragraph.getText();

            // 检查段落中是否包含占位符
            if (paragraphText.contains("${")) {
                // 遍历段落中的所有运行对象
                List<XWPFRun> runs = paragraph.getRuns();
                for (int i = 0; i < runs.size(); i++) {
                    XWPFRun run = runs.get(i);
                    String text = run.getText(0);
                    if (text != null && text.contains("${")) {
                        // 将占位符替换为4个全角空格
                        // 使用正则表达式替换所有占位符
                        text = text.replaceAll("\\$\\{[^\\}]*\\}", "　　");
                        run.setText(text, 0);
                    }
                }
            }
        }

        // 遍历所有表格
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        String paragraphText = paragraph.getText();

                        // 检查段落中是否包含占位符
                        if (paragraphText.contains("${")) {
                            // 遍历段落中的所有运行对象
                            List<XWPFRun> runs = paragraph.getRuns();
                            for (int i = 0; i < runs.size(); i++) {
                                XWPFRun run = runs.get(i);
                                String text = run.getText(0);
                                if (text != null && text.contains("${")) {
                                    // 将占位符替换为4个全角空格
                                    text = text.replaceAll("\\$\\{[^\\}]*\\}", "　　　　");
                                    run.setText(text, 0);
                                }
                            }
                        }
                    }
                }
            }
        }

        logger.debug("清理文档中未替换的占位符完成");
    }

    /**
     * 将问答内容添加到Word文档中
     *
     * @param document         Word文档对象
     * @param contentParagraph 内容段落
     * @param content          要添加的内容
     */
    public static void addQuestionAnswerContentToParagraph(XWPFDocument document, XWPFParagraph contentParagraph, String content) {
        // 清空占位符段落的内容
        for (int i = contentParagraph.getRuns().size() - 1; i >= 0; i--) {
            contentParagraph.removeRun(i);
        }

        // 将\n替换为段落分隔符
        String[] lines = content.split("\\n");
        for (int i = 0; i < lines.length; i++) {
            if (i > 0) {
                contentParagraph.createRun().addBreak(); // 添加换行
            }

            // 检查是否包含下划线占位符
            if (lines[i].contains("[PLACEHOLDER_FOR_UNDERLINE]")) {
                // 分割行，处理下划线部分
                String[] parts = lines[i].split("\\[PLACEHOLDER_FOR_UNDERLINE\\]");

                // 添加前面部分
                XWPFRun beforeRun = contentParagraph.createRun();
                beforeRun.setText(parts[0]);
                beforeRun.setFontSize(FONT_SIZE);
                ; // 添加字体大小设置

                // 创建下划线部分 - 使用更细的下划线样式
                XWPFRun underlineRun = contentParagraph.createRun();
                // 增加下划线的长度，使其能够延伸到行末尾
                StringBuilder underlineText = new StringBuilder();
                for (int j = 0; j < 30; j++) { // 增加到40个全角空格
                    underlineText.append("　");
                }
                underlineRun.setText(underlineText.toString());
                underlineRun.setUnderline(UnderlinePatterns.SINGLE);
                underlineRun.setFontSize(FONT_SIZE);
                ;
                // 显式设置下划线颜色为黑色，并调整下划线粗细
                underlineRun.setColor("000000");

                // 如果有后面部分，添加它
                if (parts.length > 1) {
                    XWPFRun afterRun = contentParagraph.createRun();
                    afterRun.setText(parts[1]);
                    afterRun.setFontSize(FONT_SIZE);
                    ; // 添加字体大小设置
                }
            }
            // 检查是否包含带内容的答案
            else if (lines[i].contains("[ANSWER_WITH_CONTENT]")) {
                // 分割行，处理带内容的答案部分
                String[] parts = lines[i].split("\\[ANSWER_WITH_CONTENT\\]");

                // 添加前面部分（通常是"答："）
                XWPFRun beforeRun = contentParagraph.createRun();
                beforeRun.setText(parts[0]);
                beforeRun.setFontSize(FONT_SIZE);
                ; // 添加字体大小设置

                // 创建答案内容部分
                if (parts.length > 1) {
                    // 获取答案内容
                    String answerContent = parts[1];

                    // 检查答案内容的长度
                    if (answerContent.length() > 30) { // 如果答案超过30个字符，考虑使用多行显示
                        // 将长答案分成多行显示，每行大约30个字符
                        int lineLength = 30;
                        int startPos = 0;
                        while (startPos < answerContent.length()) {
                            int endPos = Math.min(startPos + lineLength, answerContent.length());
                            String linePart = answerContent.substring(startPos, endPos);

                            // 创建当前行的运行对象
                            XWPFRun answerRun = contentParagraph.createRun();
                            answerRun.setText(linePart);
                            answerRun.setUnderline(UnderlinePatterns.SINGLE);
                            answerRun.setColor("000000");
                            answerRun.setFontSize(FONT_SIZE);
                            ; // 添加字体大小设置

                            // 如果不是最后一行，添加换行
                            if (endPos < answerContent.length()) {
                                answerRun.addBreak();
                                // 在新行开始添加缩进
                                XWPFRun indentRun = contentParagraph.createRun();
                                indentRun.setText("      "); // 添加6个空格的缩进
                                indentRun.setFontSize(FONT_SIZE);
                                ; // 添加字体大小设置
                            }

                            startPos = endPos;
                        }
                    } else {
                        // 对于短答案，将内容和下划线合并为一个Run
                        XWPFRun answerRun = contentParagraph.createRun();
                        int fixedTotalLength = 20; // 固定总长度
                        // 计算需要补齐的空格数
                        int spaceCount = Math.max(0, fixedTotalLength - answerContent.length());
                        // 构建带下划线的文本（内容+补齐空格）
                        StringBuilder underlinedText = new StringBuilder(answerContent);
                        for (int j = 0; j < spaceCount; j++) {
                            underlinedText.append("　"); // 使用全角空格补齐
                        }
                        answerRun.setText(underlinedText.toString());
                        answerRun.setUnderline(UnderlinePatterns.SINGLE);
                        answerRun.setFontSize(10);
                        answerRun.setColor("000000");
                        answerRun.setFontSize(FONT_SIZE);
                        ; // 添加字体大小设置
                    }
                }
            }
            // 检查是否包含带下划线的答案
            else if (lines[i].contains("[ANSWER_WITH_UNDERLINE]")) {
                // 分割行，处理带下划线的答案部分
                String[] parts = lines[i].split("\\[ANSWER_WITH_UNDERLINE\\]");

                // 添加前面部分（通常是"答："）
                XWPFRun beforeRun = contentParagraph.createRun();
                beforeRun.setText(parts[0]);
                beforeRun.setFontSize(FONT_SIZE);
                ; // 添加字体大小设置

                // 创建带下划线的答案部分 - 使用更细的下划线
                XWPFRun answerRun = contentParagraph.createRun();
                if (parts.length > 1) {
                    answerRun.setText(parts[1]);
                    answerRun.setUnderline(UnderlinePatterns.SINGLE);
                    // 显式设置下划线颜色为黑色
                    answerRun.setColor("000000");
                    answerRun.setFontSize(FONT_SIZE);
                    ; // 添加字体大小设置
                }

                // 如果有后面部分，添加它
                if (parts.length > 2) {
                    XWPFRun afterRun = contentParagraph.createRun();
                    afterRun.setText(parts[2]);
                    afterRun.setFontSize(FONT_SIZE);
                    ; // 添加字体大小设置
                }
            }
            // 处理包含选项勾选框的行
            else if (lines[i].contains("[CHECKBOX_CHECKED]") || lines[i].contains("[CHECKBOX_UNCHECKED]")) {
                // 分割行，处理勾选框部分
                String line = lines[i];
                while (line.contains("[CHECKBOX_")) {
                    int startIndex = line.indexOf("[CHECKBOX_");
                    int endIndex = line.indexOf("]", startIndex) + 1;
                    String beforeCheckbox = line.substring(0, startIndex);
                    String checkboxType = line.substring(startIndex, endIndex);
                    String afterCheckbox = line.substring(endIndex);

                    // 添加勾选框前的文本
                    if (!beforeCheckbox.isEmpty()) {
                        XWPFRun beforeRun = contentParagraph.createRun();
                        beforeRun.setText(beforeCheckbox);
                        beforeRun.setFontSize(FONT_SIZE);
                        ; // 添加字体大小设置
                    }

                    // 添加勾选框
                    XWPFRun checkboxRun = contentParagraph.createRun();
                    checkboxRun.setFontSize(FONT_SIZE);
                    ; // 设置字体大小
                    checkboxRun.setFontSize(FONT_SIZE);
                    ; // 添加字体大小设置
                    if ("[CHECKBOX_CHECKED]".equals(checkboxType)) {
                        // 使用Unicode字符表示勾选框
                        checkboxRun.setText("●"); // 已选中的复选框，修改为实心圆
                    } else {
                        // 使用Unicode字符表示空勾选框
                        checkboxRun.setText("○"); // 未选中的复选框，修改为空心圆
                    }

                    // 更新剩余的行
                    line = afterCheckbox;
                }

                // 添加剩余的文本
                if (!line.isEmpty()) {
                    XWPFRun remainingRun = contentParagraph.createRun();
                    remainingRun.setText(line);
                    remainingRun.setFontSize(FONT_SIZE);
                    ; // 添加字体大小设置
                }
            }
            // 处理普通文本行
            else {
                XWPFRun contentRun = contentParagraph.createRun();
                contentRun.setText(lines[i]);
                contentRun.setFontSize(FONT_SIZE);
                ; // 添加字体大小设置
            }
        }

        // 设置段落格式
        contentParagraph.setSpacingAfter(0);
        contentParagraph.setSpacingBefore(0);
        // 将原来的1.5倍行距修改为固定值30磅
        contentParagraph.setSpacingLineRule(LineSpacingRule.EXACT);
        contentParagraph.setSpacingBetween(30, LineSpacingRule.EXACT);
    }

    /**
     * 在Word文档中查找内容占位符所在的段落
     *
     * @param document    Word文档对象
     * @param placeholder 占位符文本
     * @return 占位符所在的段落，如果未找到则返回null
     */
    public static XWPFParagraph findPlaceholderParagraph(XWPFDocument document, String placeholder) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null && text.contains(placeholder)) {
                return paragraph;
            }
        }
        return null;
    }

    /**
     * 从输入流创建Word文档
     *
     * @param is 输入流
     * @return Word文档对象
     * @throws IOException IO异常
     */
    public static XWPFDocument createDocumentFromInputStream(InputStream is) throws IOException {
        return new XWPFDocument(is);
    }

    /**
     * 将Word文档写入输出流
     *
     * @param document Word文档对象
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    public static void writeDocumentToResponse(XWPFDocument document, HttpServletResponse response) throws IOException {
        document.write(response.getOutputStream());
        document.close();
    }

    /**
     * 精确替换Word文档中的占位符，避免替换相似的占位符
     * 此方法专门用于处理当文档中有相似占位符时的情况，如${name}和${nameExt}
     *
     * @param document       Word文档对象
     * @param placeholderMap 占位符和替换值的映射
     */
    public static void replaceTextExact(XWPFDocument document, Map<String, String> placeholderMap) {
        logger.debug("开始精确替换文档中的占位符");

        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, placeholderMap);
        }

        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceInParagraph(paragraph, placeholderMap);
                    }
                }
            }
        }

        logger.debug("精确替换文档中的占位符完成");
    }

    /**
     * 在段落中精确替换占位符
     *
     * @param paragraph      段落
     * @param placeholderMap 占位符和替换值的映射
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> placeholderMap) {
        String paragraphText = paragraph.getText();

        // 检查段落中是否可能包含任何占位符
        boolean mayContainPlaceholder = false;
        for (String placeholder : placeholderMap.keySet()) {
            if (paragraphText.contains(placeholder)) {
                mayContainPlaceholder = true;
                break;
            }
        }

        if (!mayContainPlaceholder) {
            return; // 没有占位符，直接返回
        }

        // 合并段落中的所有Run，以处理跨多个Run的占位符
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }

        // 收集所有文本
        StringBuilder fullText = new StringBuilder();
        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text != null) {
                fullText.append(text);
            }
        }

        // 进行替换
        String textAfterReplacement = fullText.toString();
        for (Map.Entry<String, String> entry : placeholderMap.entrySet()) {
            String placeholder = entry.getKey();
            String value = entry.getValue();

            // 如果包含完整的占位符，则进行替换
            if (textAfterReplacement.contains(placeholder)) {
                textAfterReplacement = textAfterReplacement.replace(placeholder, value);
            }
        }

        // 清除所有Run
        while (runs.size() > 0) {
            paragraph.removeRun(0);
        }

        // 添加替换后的文本
        XWPFRun newRun = paragraph.createRun();
        newRun.setText(textAfterReplacement);
    }

    /**
     * 使用精确替换方法来填充模板
     *
     * @param templatePath 模板文件路径
     * @param outputPath   输出文件路径
     * @param dataObject   数据对象
     * @throws IOException IO异常
     */
    public static void fillTemplateWithObjectExact(String templatePath, String outputPath, Object dataObject) throws IOException {
        logger.debug("开始精确替换方式填充Word模板，模板文件：{}，输出文件：{}", templatePath, outputPath);

        // 确保输出目录存在
        File outputFile = new File(outputPath);
        ensureDirectoryExists(outputFile);

        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 使用反射获取对象的所有字段和值
            Map<String, Object> fieldMap = extractFieldsFromObject(dataObject, "");

            // 转换为String类型
            Map<String, String> placeholderMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                Object value = entry.getValue();
                String valueStr = (value != null) ? value.toString() : "";
                placeholderMap.put(placeholder, valueStr);
            }

            // 精确替换文档中的所有占位符
            replaceTextExact(document, placeholderMap);

            // 将文档保存到文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                document.write(fos);
            }

            logger.debug("精确替换Word模板填充完成，已保存到：{}", outputPath);
        }
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据（使用精确替换方法）
     *
     * @param outputFilePath     输出文件路径
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表
     * @param dataObject         要填充的数据对象
     * @throws IOException IO异常
     */
    public static void exportQuestionsWithAnswersAndObjectToTemplateExact(String outputFilePath,
                                                                          String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList, Object dataObject) throws IOException {
        logger.debug("开始使用精确替换方法将问答题和对象数据填充到Word模板，模板文件：{}，输出文件：{}", templatePath, outputFilePath);

        // 确保输出目录存在
        File outputFile = new File(outputFilePath);
        ensureDirectoryExists(outputFile);

        // 读取模板文件
        try (InputStream is = new FileInputStream(templatePath)) {
            XWPFDocument document = createDocumentFromInputStream(is);

            // 替换文档中的标题占位符（使用精确替换）
            Map<String, String> titleMap = new HashMap<>();
            titleMap.put("${TITLE}", paperTitle);
            replaceTextExact(document, titleMap);

            // 查找内容占位符所在的段落
            XWPFParagraph contentParagraph = findPlaceholderParagraph(document, "${CONTENT}");

            // 如果没有找到占位符，则创建新段落
            if (contentParagraph == null) {
                logger.warn("在模板中未找到${CONTENT}占位符，将创建新段落");
                contentParagraph = document.createParagraph();
            }

            // 在占位符段落中创建所有问题
            if (!questionAnswerList.isEmpty()) {
                // 生成问答内容，并处理答案和选项勾选
                String content = generateQuestionAnswerContentWithAnswers(questionAnswerList, 3);

                // 将内容添加到段落中
                addQuestionAnswerContentToParagraph(document, contentParagraph, content);
            }

            // 使用反射获取对象的所有字段和值
            if (dataObject != null) {
                Map<String, Object> fieldMap = extractFieldsFromObject(dataObject, "");

                // 转换为String类型
                Map<String, String> placeholderMap = new HashMap<>();
                for (Map.Entry<String, Object> entry : fieldMap.entrySet()) {
                    String placeholder = "${" + entry.getKey() + "}";
                    Object value = entry.getValue();
                    String valueStr = (value != null) ? value.toString() : "";
                    placeholderMap.put(placeholder, valueStr);
                }

                // 精确替换文档中的所有占位符
                replaceTextExact(document, placeholderMap);
            }

            // 清理文档中所有未替换的占位符
            clearRemainingPlaceholders(document);

            // 将文档保存到文件
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                document.write(fos);
            }

            logger.debug("使用精确替换方法填充问答题和对象数据完成");
        }
    }

    /**
     * 处理从InvestigationRecordParam对象中的investigatorList转换为特定格式的调查人信息字符串
     * 姓名和单位值使用带下划线的格式
     *
     * @param param InvestigationRecordParam对象，包含investigatorList字段
     * @return 格式化后的调查人信息字符串
     */
    public static String formatInvestigators(InvestigationRecordParam param) {
        if (param == null || param.getInvestigatorList() == null || param.getInvestigatorList().isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        JSONArray investigatorList = param.getInvestigatorList();

        for (int i = 0; i < investigatorList.size(); i++) {
            JSONObject investigator = investigatorList.getJSONObject(i);
            String name = investigator.getString("nickName");
            String org = investigator.getString("orgNames");
            if (ObjectUtil.isAllNotEmpty(name, org)) {
                // 添加格式化的调查人信息，使用[ANSWER_WITH_UNDERLINE]标记需要加下划线的部分
                if (i > 0) {
                    // 改用[LINE_BREAK]作为换行标记，将在replaceTextWithUnderline方法中处理
                    result.append("[LINE_BREAK]");
                }
                result.append("调查人姓名：[ANSWER_WITH_UNDERLINE]").append(name)
                        .append("[/ANSWER_WITH_UNDERLINE]　单位：[ANSWER_WITH_UNDERLINE]").append(org)
                        .append("[/ANSWER_WITH_UNDERLINE]");
            }

        }

        return result.toString();
    }

    /**
     * 在文档中替换文本并为特定部分添加下划线
     *
     * @param document    Word文档
     * @param placeholder 要替换的占位符
     * @param value       包含[ANSWER_WITH_UNDERLINE]标记的替换值
     */
    private static void replaceTextWithUnderline(XWPFDocument document, String placeholder, String value) {
        // 查找包含占位符的段落
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            if (paragraph.getText().contains(placeholder)) {
                List<XWPFRun> runs = paragraph.getRuns();

                // 清除原有内容
                for (int i = runs.size() - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                // 修改为固定值30磅的行距
                paragraph.setSpacingLineRule(LineSpacingRule.EXACT);
                paragraph.setSpacingBetween(30, LineSpacingRule.EXACT);

                // 检查是否有换行标记
                if (value.contains("[LINE_BREAK]")) {
                    // 按换行标记分割
                    String[] lines = value.split("\\[LINE_BREAK\\]");

                    for (int lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                        // 如果不是第一行，添加一个换行
                        if (lineIndex > 0) {
                            XWPFRun breakRun = paragraph.createRun();
                            breakRun.addBreak(); // 添加换行
                        }

                        // 处理当前行中的下划线部分
                        String currentLine = lines[lineIndex];
                        String[] parts = currentLine.split("\\[ANSWER_WITH_UNDERLINE\\]|\\[/ANSWER_WITH_UNDERLINE\\]");
                        boolean isUnderline = false;

                        for (String part : parts) {
                            XWPFRun run = paragraph.createRun();
                            run.setText(part);
                            run.setFontSize(FONT_SIZE);
                            if (isUnderline) {
                                run.setUnderline(UnderlinePatterns.SINGLE);
                            }
                            isUnderline = !isUnderline;
                        }
                    }
                } else {
                    // 没有换行标记，使用原来的处理方式
                    String[] parts = value.split("\\[ANSWER_WITH_UNDERLINE\\]|\\[/ANSWER_WITH_UNDERLINE\\]");
                    boolean isUnderline = false;

                    for (String part : parts) {
                        XWPFRun run = paragraph.createRun();
                        run.setText(part);
                        run.setFontSize(FONT_SIZE);
                        if (isUnderline) {
                            run.setUnderline(UnderlinePatterns.SINGLE);
                        }
                        isUnderline = !isUnderline;
                    }
                }

                return; // 替换完成后退出
            }
        }

        // 检查表格中的段落
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        if (paragraph.getText().contains(placeholder)) {
                            List<XWPFRun> runs = paragraph.getRuns();

                            // 清除原有内容
                            for (int i = runs.size() - 1; i >= 0; i--) {
                                paragraph.removeRun(i);
                            }

                            // 修改为固定值30磅的行距
                            paragraph.setSpacingLineRule(LineSpacingRule.EXACT);
                            paragraph.setSpacingBetween(30, LineSpacingRule.EXACT);

                            // 检查是否有换行标记
                            if (value.contains("[LINE_BREAK]")) {
                                // 按换行标记分割
                                String[] lines = value.split("\\[LINE_BREAK\\]");

                                for (int lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                                    // 如果不是第一行，添加一个换行
                                    if (lineIndex > 0) {
                                        XWPFRun breakRun = paragraph.createRun();
                                        breakRun.addBreak(); // 添加换行
                                    }

                                    // 处理当前行中的下划线部分
                                    String currentLine = lines[lineIndex];
                                    String[] parts = currentLine.split("\\[ANSWER_WITH_UNDERLINE\\]|\\[/ANSWER_WITH_UNDERLINE\\]");
                                    boolean isUnderline = false;

                                    for (String part : parts) {
                                        XWPFRun run = paragraph.createRun();
                                        run.setText(part);
                                        run.setFontSize(FONT_SIZE);
                                        if (isUnderline) {
                                            run.setUnderline(UnderlinePatterns.SINGLE);
                                        }
                                        isUnderline = !isUnderline;
                                    }
                                }
                            } else {
                                // 没有换行标记，使用原来的处理方式
                                String[] parts = value.split("\\[ANSWER_WITH_UNDERLINE\\]|\\[/ANSWER_WITH_UNDERLINE\\]");
                                boolean isUnderline = false;

                                for (String part : parts) {
                                    XWPFRun run = paragraph.createRun();
                                    run.setText(part);
                                    run.setFontSize(FONT_SIZE);
                                    if (isUnderline) {
                                        run.setUnderline(UnderlinePatterns.SINGLE);
                                    }
                                    isUnderline = !isUnderline;
                                }
                            }

                            return; // 替换完成后退出
                        }
                    }
                }
            }
        }
    }

    /**
     * 将Word文档转换为PDF并指定字体类型
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  输出PDF文件路径
     * @param fontType     要使用的字体类型
     * @throws IOException IO异常
     */
    public static void convertWordToPdfWithFont(String wordFilePath, String pdfFilePath, FontType fontType) throws IOException {
        // 优先使用中文标点符号优化的转换方法
        try {
            convertWordToPdfWithPureIText(wordFilePath, pdfFilePath, fontType);
            return;
        } catch (Exception e) {
            logger.warn("使用纯iText转换失败，回退到原始方法: {}", e.getMessage());
        }

        // 回退到原始转换方法
        convertWordToPdfWithFontOriginal(wordFilePath, pdfFilePath, fontType);
    }

    /**
     * 将Word文档转换为PDF，使用指定字体（原始方法）
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  PDF输出文件路径
     * @param fontType     字体类型
     * @throws IOException IO异常
     */
    public static void convertWordToPdfWithFontOriginal(String wordFilePath, String pdfFilePath, FontType fontType) throws IOException {
        logger.debug("开始转换Word文档到PDF（使用{}字体-原始方法），源文件：{}，目标文件：{}", fontType, wordFilePath, pdfFilePath);

        // 确保输出目录存在
        File pdfFile = new File(pdfFilePath);
        ensureDirectoryExists(pdfFile);

        // 使用POI-XWPF-Converter-PDF进行转换
        try (FileInputStream fis = new FileInputStream(wordFilePath);
             FileOutputStream fos = new FileOutputStream(pdfFilePath)) {

            // 加载Word文档
            XWPFDocument document = new XWPFDocument(fis);

            // 创建PDF选项并配置字体
            PdfOptions options = PdfOptions.create();

            // 配置字体提供器，指定特定字体
            final FontType selectedFontType = fontType;
            options.fontProvider(new IFontProvider() {
                @Override
                public Font getFont(String familyName, String encoding, float size, int style, Color color) {
                    try {
                        // 优先使用指定的字体类型
                        BaseFont baseFont = getBaseFontByType(selectedFontType);
                        return new Font(baseFont, size, style, color);
                    } catch (Exception e) {
                        logger.error("使用指定字体失败，将使用默认字体逻辑: {}", e.getMessage());
                        // 如果指定字体失败，回退到默认字体选择逻辑
                        return createPdfFont(familyName, encoding, size, style, color);
                    }
                }
            });

            // 将Word转换为PDF
            PdfConverter.getInstance().convert(document, fos, options);

            // 关闭文档
            document.close();

            logger.debug("Word转PDF转换完成（使用{}字体-原始方法）", fontType);
        } catch (Exception e) {
            logger.error("Word转PDF转换失败（原始方法）", e);
            throw new IOException("Word转PDF转换失败（原始方法）: " + e.getMessage(), e);
        }
    }

    /**
     * 将Word文档转换为PDF并导出到HTTP响应，使用指定字体
     *
     * @param response     HTTP响应对象
     * @param fileName     输出文件名（不含扩展名）
     * @param wordFilePath Word文件路径
     * @param fontType     要使用的字体类型
     * @throws IOException IO异常
     */
    public static void convertWordToPdfAndExportWithFont(HttpServletResponse response, String fileName,
                                                         String wordFilePath, FontType fontType) throws IOException {
        logger.debug("开始转换Word文档到PDF并输出到HTTP响应（使用{}字体），源文件：{}", fontType, wordFilePath);

        // 创建临时文件保存PDF
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 调用转换方法，使用指定字体
            convertWordToPdfWithFont(wordFilePath, tempPdfFile.getAbsolutePath(), fontType);

            // 使用统一的方法设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);
        } finally {
            // 删除临时文件
            deleteFileIfExists(tempPdfFile);
        }

        logger.debug("转换并输出完成（使用{}字体）", fontType);
    }

    /**
     * 将Word文档转换为PDF并尝试根据文件名推断使用的字体
     *
     * @param wordFilePath Word文件路径
     * @param pdfFilePath  输出PDF文件路径
     * @throws IOException IO异常
     */
    public static void convertWordToPdfWithAutoFont(String wordFilePath, String pdfFilePath) throws IOException {
        // 尝试从文件名推断字体类型
        String fileName = new File(wordFilePath).getName().toLowerCase();
        FontType fontType = FontType.DEFAULT; // 默认使用仿宋

        // 根据文件名判断可能使用的字体
        if (fileName.contains("song") || fileName.contains("宋体")) {
            fontType = FontType.SONG; // 宋体
        } else if (fileName.contains("fangsong") || fileName.contains("仿宋")) {
            fontType = FontType.DEFAULT; // 仿宋
        }

        logger.debug("根据文件名[{}]自动推断使用{}字体", fileName, fontType);
        convertWordToPdfWithFont(wordFilePath, pdfFilePath, fontType);
    }

    /**
     * 将Word文档转换为PDF并导出到HTTP响应，自动识别字体
     *
     * @param response     HTTP响应对象
     * @param fileName     输出文件名（不含扩展名）
     * @param wordFilePath Word文件路径
     * @throws IOException IO异常
     */
    public static void convertWordToPdfAndExportWithAutoFont(HttpServletResponse response, String fileName,
                                                             String wordFilePath) throws IOException {
        logger.debug("开始转换Word文档到PDF并输出到HTTP响应（智能识别字体），源文件：{}", wordFilePath);

        // 创建临时文件保存PDF
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 调用自动识别字体的转换方法
            convertWordToPdfWithAutoFont(wordFilePath, tempPdfFile.getAbsolutePath());

            // 使用统一的方法设置文件下载头信息
            setFileDownloadHeader(response, fileName, "pdf", true);

            // 将PDF文件写入响应流
            writeFileToResponse(tempPdfFile, response);
        } finally {
            // 删除临时文件
            deleteFileIfExists(tempPdfFile);
        }

        logger.debug("转换并输出完成（智能识别字体）");
    }

    public static void exportQuestionsWithAnswersAndObjectAsWord(
            HttpServletResponse response,
            String fileName,
            String templatePath,
            String paperTitle,
            List<Map<String, Object>> questionAnswerList,
            InvestigationRecordParam recordParam) throws Exception {

        // 创建临时Word文件
        File tempFile = File.createTempFile("word_", ".docx");

        try {
            // 填充模板生成Word (使用与PDF相同的逻辑，但跳过PDF转换步骤)
            fillTemplateWithQuestionsAndObject(templatePath, tempFile.getAbsolutePath(),
                    paperTitle, questionAnswerList, recordParam);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            String encodedFileName = URLEncoder.encode(fileName + ".docx", CharsetUtil.UTF_8);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");

            // 将Word文件写入响应流
            try (FileInputStream fileInput = new FileInputStream(tempFile);
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fileInput.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
        } finally {
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 填充模板生成Word文档（与PDF生成共用的方法）
     *
     * @param templatePath       模板文件路径
     * @param outputFilePath     输出文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表
     * @param recordParam        调查记录参数
     * @throws IOException IO异常
     */
    public static void fillTemplateWithQuestionsAndObject(String templatePath, String outputFilePath,
                                                          String paperTitle, List<Map<String, Object>> questionAnswerList,
                                                          InvestigationRecordParam recordParam) throws IOException {
        // 复用已有方法
        exportQuestionsWithAnswersAndObjectToTemplate(outputFilePath, templatePath, paperTitle,
                questionAnswerList, recordParam);
    }


    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后转换为PDF导出
     * 使用默认字体
     *
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表
     * @param dataObject         要填充的数据对象
     * @throws IOException IO异常
     */
    public static File exportQuestionsWithAnswersAndObjectAsPdfFile(String fileName,
                                                                String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList, Object dataObject) throws IOException {
        // 调用带字体类型参数的方法，使用默认字体
        return exportQuestionsWithAnswersAndObjectAsPdfFile(fileName, templatePath, paperTitle, questionAnswerList, dataObject, FontType.DEFAULT);
    }

    /**
     * 将问答题填充到Word模板并同时填充对象数据，然后转换为PDF 返回File
     *
     * @param fileName           导出文件名
     * @param templatePath       模板文件路径
     * @param paperTitle         试卷标题
     * @param questionAnswerList 问答列表，每个元素是一个Map，包含问题(question)、答案(answer)、选项(options)和备注(remark)字段
     * @param dataObject         要填充的数据对象
     * @param fontType           指定使用的字体类型
     * @throws IOException IO异常
     */
    public static File exportQuestionsWithAnswersAndObjectAsPdfFile(String fileName,
                                                                String templatePath, String paperTitle, List<Map<String, Object>> questionAnswerList,
                                                                Object dataObject, FontType fontType) throws IOException {
        // 创建临时文件
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File tempWordFile = File.createTempFile("word_", ".docx", tempDir);
        File tempPdfFile = File.createTempFile("pdf_", ".pdf", tempDir);

        try {
            // 先填充模板并保存到临时Word文件
            exportQuestionsWithAnswersAndObjectToTemplate(tempWordFile.getAbsolutePath(), templatePath, paperTitle, questionAnswerList, dataObject);

            // 将Word转换为PDF，使用指定字体
            convertWordToPdfWithFont(tempWordFile.getAbsolutePath(), tempPdfFile.getAbsolutePath(), fontType);

            logger.debug("PDF文档已成功生成（使用{}字体）", fontType);
            return tempPdfFile;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("PDF文档已成功生成异常,{}", fileName);
        } finally {
            // 清理临时文件
            deleteFileIfExists(tempWordFile);
        }
        return null;
    }

    /**
     * 测试中文标点符号处理的方法
     * 可以用来验证中文标点符号换行问题是否得到解决
     *
     * @param inputText 测试文本
     * @param outputPath 输出PDF路径
     * @param fontType 字体类型
     * @throws IOException IO异常
     */
    public static void testChinesePunctuationHandling(String inputText, String outputPath, FontType fontType) throws IOException {
        logger.debug("开始测试中文标点符号处理，输出文件：{}", outputPath);

        // 确保输出目录存在
        File pdfFile = new File(outputPath);
        ensureDirectoryExists(pdfFile);

        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            // 创建PDF文档
            com.lowagie.text.Document pdfDocument = new com.lowagie.text.Document(com.lowagie.text.PageSize.A4);
            com.lowagie.text.pdf.PdfWriter writer = com.lowagie.text.pdf.PdfWriter.getInstance(pdfDocument, fos);

            pdfDocument.open();

            // 获取字体
            BaseFont baseFont = getBaseFontByType(fontType);
            com.lowagie.text.Font font = new com.lowagie.text.Font(baseFont, 12f, com.lowagie.text.Font.NORMAL, java.awt.Color.BLACK);

            // 添加测试文本，预处理中文标点符号
            String processedText = preprocessChineseText(inputText);
            com.lowagie.text.Paragraph paragraph = new com.lowagie.text.Paragraph(processedText, font);
            paragraph.setAlignment(com.lowagie.text.Element.ALIGN_LEFT);
            pdfDocument.add(paragraph);

            pdfDocument.close();
            logger.debug("中文标点符号处理测试完成");
        } catch (Exception e) {
            logger.error("中文标点符号处理测试失败", e);
            throw new IOException("中文标点符号处理测试失败: " + e.getMessage(), e);
        }
    }
}
