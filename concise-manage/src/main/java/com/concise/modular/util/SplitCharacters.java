package com.concise.modular.util;

import com.lowagie.text.pdf.PdfChunk;

/**
 * 中文标点符号换行处理类 - 兼容lowagie iText
 * 解决中文标点符号在PDF中换行时位置错误的问题
 *
 * <AUTHOR>
 * @date 2025/8/4
 */
public class SplitCharacters implements com.lowagie.text.SplitCharacter {

    /**
     * 判断是否可以在指定位置进行换行
     *
     * @param start 起始位置
     * @param current 当前位置
     * @param end 结束位置
     * @param cc 字符数组
     * @param ck PdfChunk数组
     * @return 是否可以换行
     */
    @Override
    public boolean isSplitCharacter(int start, int current, int end, char[] cc, PdfChunk[] ck) {
        char currentChar = getCurrentCharacter(current, cc, ck);

        // 如果当前字符是中文标点符号，不允许在其前面换行
        if (isChinesePunctuation(currentChar)) {
            return false;
        }

        // 如果下一个字符是中文标点符号，不允许在当前位置换行
        if (current + 1 < end) {
            char nextChar = getCurrentCharacter(current + 1, cc, ck);
            if (isChinesePunctuation(nextChar)) {
                return false;
            }
        }

        // 其他情况允许换行
        return true;
    }

    /**
     * 获取指定位置的字符
     */
    private char getCurrentCharacter(int pos, char[] cc, PdfChunk[] ck) {
        if (ck == null || ck.length == 0) {
            return pos < cc.length ? cc[pos] : ' ';
        }

        // 从PdfChunk中获取字符
        for (PdfChunk chunk : ck) {
            if (chunk != null) {
                String text = chunk.toString();
                if (text != null && pos < text.length()) {
                    return text.charAt(pos);
                }
            }
        }

        return pos < cc.length ? cc[pos] : ' ';
    }

    /**
     * 判断是否为中文标点符号
     */
    private boolean isChinesePunctuation(char c) {
        // 中文标点符号范围
        return (c >= 0x3000 && c <= 0x303F) ||  // CJK符号和标点
               (c >= 0xFF00 && c <= 0xFFEF) ||  // 全角ASCII、全角标点
               c == '，' || c == '。' || c == '；' || c == '：' ||
               c == '？' || c == '！' || c == '"' || c == '"' ||
               c == ''' || c == ''' || c == '（' || c == '）' ||
               c == '【' || c == '】' || c == '《' || c == '》' ||
               c == '、' || c == '…' || c == '—' || c == '·';
    }
}
