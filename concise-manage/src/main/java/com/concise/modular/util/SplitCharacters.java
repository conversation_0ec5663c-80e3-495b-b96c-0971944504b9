package com.concise.modular.util;

import com.itextpdf.io.font.otf.GlyphLine;
import com.itextpdf.layout.splitting.ISplitCharacters;

/**
 * <AUTHOR>
 * @date 2025/8/4
 */
public class SplitCharacters implements ISplitCharacters {
    /**
     * @param glyphLine 文本内容
     * @param glyphPos 索引
     * */
    @Override
    public boolean isSplitCharacter(GlyphLine glyphLine, int glyphPos) {
        if (glyphLine.size()>2){
            if (glyphPos+1<glyphLine.size() && glyphPos!=0){
                char[] charLeft = glyphLine.get(glyphPos-1).getChars();
                char[] charRight = glyphLine.get(glyphPos+1).getChars();
                if (String.valueOf(charRight[0]).matches("[\\p{P}]+") || String.valueOf(charLeft[0]).matches("[\\p{P}]+")){
                    return false;
                }
            }

        }
        return true;
    }
}
