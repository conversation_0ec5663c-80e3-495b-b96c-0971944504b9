package com.concise.app.modular.controller;

import com.concise.common.annotion.BusinessLog;
import com.concise.common.enums.LogAnnotionOpTypeEnum;
import com.concise.common.file.FileOperator;
import com.concise.common.file.entity.SysFileInfo;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.response.ResponseData;
import com.concise.common.pojo.response.SuccessResponseData;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <p>
 * UploadController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021/7/18
 * @Description
 */
@Slf4j
@Api("文件上传API")
@RequestMapping("file")
@RestController
public class UploadController {

    @Autowired
    FileOperator fileOperator;


    @Resource
    private SysFileInfoService sysFileInfoService;

    /**
     * 上传文件
     *
     * <AUTHOR>
     * @date 2020/6/7 22:15
     */
    @PostMapping("/sysFileInfo/upload")
    @BusinessLog(title = "文件信息表_上传文件", opType = LogAnnotionOpTypeEnum.OTHER)
    public ResponseData upload(@RequestPart("file") MultipartFile file) {
        SysFileInfo sysFileInfo = sysFileInfoService.uploadFile(file);
        return new SuccessResponseData(sysFileInfo);
    }

}
