package com.concise.app.modular.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.concise.common.pojo.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * AuthController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2021/7/19
 * @Description
 */
@Slf4j
@Api("AUTH_API")
@RequestMapping("auth")
@RestController
public class AuthController {



    @ApiOperation("登录")
    @PostMapping("login")
    public ResponseData login() {
        StpUtil.login("8888");

        return null;
    }
}
