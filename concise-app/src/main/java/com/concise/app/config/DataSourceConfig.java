//package com.concise.app.config;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.alibaba.druid.pool.DruidDataSource;
//import com.alibaba.druid.support.http.StatViewServlet;
//import com.concise.common.pojo.druid.DruidProperties;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.web.servlet.ServletRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.HashMap;
//
///**
// * Druid配置
// *
// * <AUTHOR>
// * @date 2017/5/20 21:58
// */
//@Configuration
//public class DataSourceConfig {
//
//    /**
//     * druid属性配置
//     *
//     * <AUTHOR>
//     * @date 2020/8/25
//     */
//    @Bean
//    @ConfigurationProperties(prefix = "spring.datasource")
//    public DruidProperties druidProperties() {
//        return new DruidProperties();
//    }
//
//    /**
//     * druid数据库连接池
//     *
//     * <AUTHOR>
//     * @date 2020/8/25
//     */
//    @Bean(initMethod = "init")
//    public DruidDataSource dataSource(DruidProperties druidProperties) {
//        DruidDataSource dataSource = new DruidDataSource();
//        druidProperties.config(dataSource);
//        return dataSource;
//    }
//
//    /**
//     * druid监控，配置StatViewServlet
//     *
//     * <AUTHOR>
//     * @date 2020/6/28 16:03
//     */
//    @Bean
//    public ServletRegistrationBean<StatViewServlet> druidServletRegistration() {
//
//        // 设置servlet的参数
//        HashMap<String, String> statViewServletParams = CollectionUtil.newHashMap();
//        statViewServletParams.put("resetEnable", "true");
//        ServletRegistrationBean<StatViewServlet> registration = new ServletRegistrationBean<>(new StatViewServlet());
//        registration.addUrlMappings("/druid/*");
//        registration.setInitParameters(statViewServletParams);
//        return registration;
//    }
//
//}
